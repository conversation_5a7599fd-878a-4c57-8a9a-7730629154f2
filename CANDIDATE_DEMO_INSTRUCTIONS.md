
# 🎯 JOB RECOMMENDATION SYSTEM - CANDIDATE DEMO GUIDE

## 🚀 QUICK DEMO STEPS FOR CANDIDATES

### 1. Start the job recommendation system:
```bash
python job_recommendation_system.py
```

### 2. Try job search for experienced Python developer:
- Select option 2 (Find job recommendations)
- Choose profile: nguyen_van_a_senior_python_dev.json
- Review personalized job recommendations!

### 3. Try job search for data scientist:
- Select option 2 again
- Choose profile: tran_thi_b_data_scientist.json
- See different recommendations based on skills!

### 4. Try career changer scenario:
- Select option 2 again
- Choose profile: le_van_c_career_changer.json
- Explore transition opportunities!

## 📊 EXPECTED RESULTS

### For Senior Python Developer (<PERSON><PERSON><PERSON> Van A):
- **Perfect matches**: Tech Lead, Senior Python positions
- **High salary compatibility**: 35-50M expectation vs market
- **Leadership opportunities**: Tech Lead roles highlighted

### For Data Scientist (Tran Thi B):
- **Best matches**: ML Engineer, Data Scientist roles
- **Growth potential**: Stretch opportunities identified
- **Remote options**: Remote-friendly positions prioritized

### For Career Changer (Le Van C):
- **Transition opportunities**: Python roles for Java developers
- **Skill development**: Learning paths suggested
- **Location flexibility**: Da Nang and remote options

## 🎯 WHAT TO OBSERVE

1. **Personalized Ranking**: How AI ranks jobs based on individual profiles
2. **Skill Matching**: Relevance of candidate skills to job requirements
3. **Career Growth**: Stretch opportunities and development paths
4. **Salary Compatibility**: Market rate vs expectations analysis
5. **Location Preferences**: Geographic and remote work matching

## 💡 TIPS FOR TESTING

- Try adjusting `minimum_confidence` in profiles (0.6, 0.7, 0.8)
- Modify `salary_expectation` to see impact on recommendations
- Change `preferred_locations` to test location matching
- Update skills to see how it affects job rankings

## 🚀 ADVANCED TESTING

- Create your own candidate profile using the template
- Add new skills and see recommendation changes
- Test with different experience levels
- Explore various industry preferences

Enjoy discovering your perfect job matches! 🎯
