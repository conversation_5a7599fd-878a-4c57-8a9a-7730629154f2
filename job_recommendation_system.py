#!/usr/bin/env python3
"""
Job Recommendation System for Candidates
<PERSON><PERSON> thống gợi ý công việc cho ứng viên sử dụng mô hình XGBoost
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from pathlib import Path
from xgboost_prediction import XGBoostPredictor

class JobRecommendationSystem:
    def __init__(self):
        self.predictor = XGBoostPredictor()
        self.results_folder = "candidate_results"
        self.ensure_folders_exist()
        
    def ensure_folders_exist(self):
        """Tạo các folder cần thiết"""
        os.makedirs(self.results_folder, exist_ok=True)
        os.makedirs("candidate_profiles", exist_ok=True)
    
    def create_candidate_profile_template(self):
        """
        Tạo template để ứng viên điền thông tin profile
        """
        template = {
            "candidate_info": {
                "candidate_id": "CAND_2024_001",
                "name": "<PERSON><PERSON><PERSON>",
                "email": "<EMAIL>",
                "phone": "+84 901 234 567",
                "location": "Ho Chi Minh City",
                "current_position": "Python Developer",
                "linkedin": "linkedin.com/in/nguyen-van-a"
            },
            "skills": {
                "programming_languages": ["Python", "JavaScript", "Java"],
                "frameworks": ["Django", "React", "Spring"],
                "databases": ["PostgreSQL", "MySQL", "MongoDB"],
                "tools": ["Git", "Docker", "AWS"],
                "soft_skills": ["Team Leadership", "Problem Solving", "Communication"]
            },
            "experience": {
                "total_years": 4,
                "current_company": "ABC Tech",
                "current_role": "Senior Python Developer",
                "previous_roles": [
                    {
                        "company": "XYZ Startup",
                        "position": "Python Developer",
                        "duration": "2 years",
                        "key_achievements": ["Built REST APIs", "Database optimization"]
                    }
                ]
            },
            "education": {
                "level": 2,  # 1=High school, 2=Bachelor, 3=Master, 4=PhD
                "degree": "Bachelor of Computer Science",
                "university": "HCMUT",
                "graduation_year": 2020,
                "gpa": 3.5
            },
            "preferences": {
                "desired_positions": ["Senior Python Developer", "Full-stack Developer", "Tech Lead"],
                "preferred_locations": ["Ho Chi Minh City", "Remote"],
                "salary_expectation": "30-45M VND",
                "work_style": "Hybrid",
                "company_size": ["Startup", "Scale-up"],
                "industries": ["Fintech", "E-commerce", "Healthcare"]
            },
            "job_search_settings": {
                "minimum_confidence": 0.7,  # Chỉ show jobs có confidence >= 70%
                "max_jobs_to_show": 15,     # Top 15 jobs
                "exclude_current_company": True,
                "include_stretch_goals": True  # Include challenging positions
            }
        }
        
        # Save template
        template_file = "candidate_profiles/candidate_profile_template.json"
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Candidate profile template created: {template_file}")
        print("📝 Please edit this file with your personal information")
        return template_file
    
    def load_candidate_profile(self, profile_file):
        """
        Load candidate profile từ file JSON
        """
        try:
            with open(profile_file, 'r', encoding='utf-8') as f:
                candidate_data = json.load(f)
            print(f"✅ Loaded candidate profile: {candidate_data['candidate_info']['name']}")
            return candidate_data
        except FileNotFoundError:
            print(f"❌ Profile file not found: {profile_file}")
            return None
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON format in: {profile_file}")
            return None
    
    def load_available_jobs(self):
        """
        Load tất cả available jobs từ job_requirements folder
        """
        jobs = []
        job_folder = Path("job_requirements")
        
        if not job_folder.exists():
            print("❌ No job_requirements folder found")
            return []
        
        for job_file in job_folder.glob("*.json"):
            try:
                with open(job_file, 'r', encoding='utf-8') as f:
                    job_data = json.load(f)
                    job_data['source_file'] = job_file.name
                    jobs.append(job_data)
            except Exception as e:
                print(f"⚠️ Error loading {job_file}: {e}")
        
        print(f"✅ Loaded {len(jobs)} available job positions")
        return jobs
    
    def calculate_candidate_job_features(self, candidate_data, job_data):
        """
        Tính toán features giữa candidate profile và job requirements
        """
        # Extract candidate skills
        all_candidate_skills = []
        for skill_category in candidate_data['skills'].values():
            if isinstance(skill_category, list):
                all_candidate_skills.extend([skill.lower() for skill in skill_category])
        
        # Extract job skills
        job_skills = []
        job_skills.extend([skill.lower() for skill in job_data['requirements']['required_skills']])
        job_skills.extend([skill.lower() for skill in job_data['requirements'].get('preferred_skills', [])])
        
        # Skill similarity
        candidate_skills_set = set(all_candidate_skills)
        job_skills_set = set(job_skills)
        skill_similarity = len(candidate_skills_set.intersection(job_skills_set)) / len(candidate_skills_set.union(job_skills_set)) if candidate_skills_set.union(job_skills_set) else 0
        
        # Experience match
        required_exp = job_data['requirements']['required_experience']
        candidate_exp = candidate_data['experience']['total_years']
        experience_match = min(candidate_exp / required_exp, 1.0) if required_exp > 0 else 1.0
        
        # Education match
        required_edu = job_data['requirements']['required_education']
        candidate_edu = candidate_data['education']['level']
        education_match = 1.0 if candidate_edu >= required_edu else candidate_edu / required_edu
        
        # Location compatibility
        job_location = job_data['job_info']['location'].lower()
        preferred_locations = [loc.lower() for loc in candidate_data['preferences']['preferred_locations']]
        location_compatibility = 1.0 if any(job_location in loc or loc in job_location or loc == 'remote' for loc in preferred_locations) else 0.6
        
        # Text similarity (simplified)
        job_text = job_data.get('job_description', '').lower()
        candidate_text = f"{candidate_data['candidate_info']['current_position']} {' '.join(all_candidate_skills)}".lower()
        
        # Simple word overlap
        job_words = set(job_text.split())
        candidate_words = set(candidate_text.split())
        text_similarity = len(job_words.intersection(candidate_words)) / len(job_words.union(candidate_words)) if job_words.union(candidate_words) else 0
        
        # Create feature vector (same as HR system but reversed perspective)
        features = {
            'tfidf_unigram': text_similarity,
            'tfidf_bigram': text_similarity * 0.8,
            'tfidf_trigram': text_similarity * 0.6,
            'tfidf_char': text_similarity * 0.4,
            'skill_similarity': skill_similarity,
            'experience_match': experience_match,
            'education_match': education_match,
            'semantic_similarity': text_similarity * 0.7,
            'location_compatibility': location_compatibility,
            'length_ratio': min(len(candidate_text) / len(job_text), 1.0) if len(job_text) > 0 else 0.5,
            'skill_count_ratio': min(len(all_candidate_skills) / len(job_skills), 1.0) if len(job_skills) > 0 else 0.5,
            'required_experience': required_exp,
            'candidate_experience': candidate_exp,
            'required_education': required_edu,
            'candidate_education': candidate_edu,
            'job_skill_count': len(job_skills),
            'resume_skill_count': len(all_candidate_skills),
            'job_length': len(job_text),
            'resume_length': len(candidate_text),
            'exp_edu_interaction': experience_match * education_match
        }
        
        return features
    
    def recommend_jobs_for_candidate(self, candidate_data, available_jobs):
        """
        Gợi ý jobs phù hợp cho candidate
        """
        print(f"\n🔍 FINDING JOBS FOR {candidate_data['candidate_info']['name']}")
        print(f"📋 Analyzing {len(available_jobs)} available positions")
        print("="*60)
        
        recommendations = []
        
        for job in available_jobs:
            # Skip current company if requested
            if (candidate_data['job_search_settings'].get('exclude_current_company', True) and 
                job['job_info'].get('company', '').lower() == candidate_data['experience'].get('current_company', '').lower()):
                continue
            
            # Calculate features
            features = self.calculate_candidate_job_features(candidate_data, job)
            
            # Get prediction
            prediction = self.predictor.predict_single(features)
            
            # Calculate additional matching factors
            salary_match = self.calculate_salary_compatibility(candidate_data, job)
            company_culture_match = self.calculate_culture_fit(candidate_data, job)
            
            # Add job info to result
            result = {
                'job_id': job['job_info']['job_id'],
                'job_title': job['job_info']['job_title'],
                'company': job['job_info'].get('company', 'Company Name'),
                'department': job['job_info'].get('department', 'N/A'),
                'location': job['job_info']['location'],
                'salary_range': job['job_info'].get('salary_range', 'Negotiable'),
                'employment_type': job['job_info'].get('employment_type', 'Full-time'),
                'source_file': job['source_file'],
                'prediction': prediction['prediction'],
                'prediction_label': prediction['prediction_label'],
                'confidence': prediction['confidence'],
                'probabilities': prediction['probabilities'],
                'features': features,
                'salary_match': salary_match,
                'culture_match': company_culture_match,
                'overall_score': self.calculate_overall_score(prediction, salary_match, company_culture_match),
                'match_reasons': self.generate_job_match_reasons(features, prediction, salary_match, company_culture_match),
                'growth_potential': self.assess_growth_potential(candidate_data, job, features)
            }
            
            recommendations.append(result)
        
        # Sort by overall score and confidence
        recommendations.sort(key=lambda x: (x['overall_score'], x['confidence']), reverse=True)
        
        # Apply candidate preferences
        filtered_recommendations = self.apply_candidate_preferences(recommendations, candidate_data['job_search_settings'])
        
        print(f"✅ Found {len(filtered_recommendations)} matching job opportunities")
        return filtered_recommendations

    def calculate_salary_compatibility(self, candidate_data, job_data):
        """
        Tính toán độ phù hợp về salary
        """
        candidate_expectation = candidate_data['preferences'].get('salary_expectation', '')
        job_salary = job_data['job_info'].get('salary_range', '')

        if not candidate_expectation or not job_salary or 'negotiable' in job_salary.lower():
            return 0.7  # Neutral score

        try:
            # Extract numbers from salary strings (simplified)
            import re
            candidate_nums = [int(x) for x in re.findall(r'\d+', candidate_expectation)]
            job_nums = [int(x) for x in re.findall(r'\d+', job_salary)]

            if len(candidate_nums) >= 2 and len(job_nums) >= 2:
                candidate_min, candidate_max = candidate_nums[0], candidate_nums[1]
                job_min, job_max = job_nums[0], job_nums[1]

                # Check overlap
                if job_max >= candidate_min and candidate_max >= job_min:
                    overlap = min(job_max, candidate_max) - max(job_min, candidate_min)
                    total_range = max(job_max, candidate_max) - min(job_min, candidate_min)
                    return overlap / total_range if total_range > 0 else 0.8
                else:
                    return 0.3  # No overlap
        except:
            pass

        return 0.7  # Default neutral score

    def calculate_culture_fit(self, candidate_data, job_data):
        """
        Tính toán độ phù hợp về company culture
        """
        score = 0.5  # Base score

        # Work style match
        candidate_work_style = candidate_data['preferences'].get('work_style', '').lower()
        job_work_style = job_data.get('company_culture', {}).get('work_style', '').lower()

        if candidate_work_style and job_work_style:
            if candidate_work_style == job_work_style:
                score += 0.2
            elif 'hybrid' in candidate_work_style or 'hybrid' in job_work_style:
                score += 0.1

        # Company size preference
        preferred_sizes = [size.lower() for size in candidate_data['preferences'].get('company_size', [])]
        job_stage = job_data.get('company_culture', {}).get('company_stage', '').lower()

        if job_stage in preferred_sizes:
            score += 0.2

        # Industry match (if available)
        preferred_industries = [ind.lower() for ind in candidate_data['preferences'].get('industries', [])]
        # This would need to be added to job data in real implementation

        return min(score, 1.0)

    def calculate_overall_score(self, prediction, salary_match, culture_match):
        """
        Tính overall score kết hợp nhiều factors
        """
        # Weighted combination
        prediction_weight = 0.6
        salary_weight = 0.2
        culture_weight = 0.2

        prediction_score = prediction['confidence'] if prediction['prediction'] >= 1 else prediction['confidence'] * 0.5

        overall = (prediction_score * prediction_weight +
                  salary_match * salary_weight +
                  culture_match * culture_weight)

        return overall

    def generate_job_match_reasons(self, features, prediction, salary_match, culture_match):
        """
        Tạo lý do tại sao job này phù hợp với candidate
        """
        reasons = []

        if features['skill_similarity'] > 0.7:
            reasons.append(f"🎯 Excellent skill match ({features['skill_similarity']:.1%})")
        elif features['skill_similarity'] > 0.5:
            reasons.append(f"✅ Good skill match ({features['skill_similarity']:.1%})")

        if features['experience_match'] >= 1.0:
            reasons.append(f"💼 Your experience exceeds requirements")
        elif features['experience_match'] > 0.8:
            reasons.append(f"💼 Your experience fits well")

        if features['education_match'] >= 1.0:
            reasons.append("🎓 Education requirement satisfied")

        if features['location_compatibility'] >= 1.0:
            reasons.append("📍 Perfect location match")
        elif features['location_compatibility'] > 0.5:
            reasons.append("📍 Location is acceptable")

        if salary_match > 0.7:
            reasons.append("💰 Salary range matches your expectations")

        if culture_match > 0.7:
            reasons.append("🏢 Good company culture fit")

        if prediction['confidence'] > 0.9:
            reasons.append("🔥 Very high confidence match")
        elif prediction['confidence'] > 0.8:
            reasons.append("⭐ High confidence match")

        return reasons

    def assess_growth_potential(self, candidate_data, job_data, features):
        """
        Đánh giá growth potential của job cho candidate
        """
        growth_factors = []

        # Experience level comparison
        if features['candidate_experience'] < features['required_experience']:
            growth_factors.append("📈 Stretch opportunity - will accelerate your growth")
        elif features['candidate_experience'] > features['required_experience'] * 1.5:
            growth_factors.append("👑 Leadership opportunity - you can mentor others")

        # Skill development opportunities
        job_skills = set(job_data['requirements']['required_skills'] + job_data['requirements'].get('preferred_skills', []))
        candidate_skills = set()
        for skills in candidate_data['skills'].values():
            if isinstance(skills, list):
                candidate_skills.update([s.lower() for s in skills])

        new_skills = job_skills - candidate_skills
        if new_skills:
            growth_factors.append(f"🚀 Learn new skills: {', '.join(list(new_skills)[:3])}")

        # Company stage growth
        company_stage = job_data.get('company_culture', {}).get('company_stage', '').lower()
        if 'startup' in company_stage:
            growth_factors.append("🌱 Startup environment - high impact potential")
        elif 'scale-up' in company_stage:
            growth_factors.append("📊 Scale-up phase - rapid career advancement")

        return growth_factors

    def apply_candidate_preferences(self, recommendations, preferences):
        """
        Áp dụng candidate preferences để filter results
        """
        # Filter by minimum confidence
        min_confidence = preferences.get('minimum_confidence', 0.7)
        filtered = [r for r in recommendations if r['confidence'] >= min_confidence]

        # Limit number of jobs
        max_jobs = preferences.get('max_jobs_to_show', 15)
        filtered = filtered[:max_jobs]

        return filtered

    def generate_candidate_report(self, candidate_data, recommendations):
        """
        Tạo báo cáo job recommendations cho candidate
        """
        candidate_name = candidate_data['candidate_info']['name']
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create summary statistics
        total_jobs = len(recommendations)
        highly_suitable = len([r for r in recommendations if r['prediction'] == 2])
        moderately_suitable = len([r for r in recommendations if r['prediction'] == 1])
        stretch_goals = len([r for r in recommendations if r['prediction'] == 0 and r['confidence'] > 0.6])

        avg_confidence = np.mean([r['confidence'] for r in recommendations]) if recommendations else 0
        avg_salary_match = np.mean([r['salary_match'] for r in recommendations]) if recommendations else 0

        # Create detailed report
        report = {
            'candidate_info': candidate_data['candidate_info'],
            'search_summary': {
                'total_jobs_found': total_jobs,
                'highly_suitable': highly_suitable,
                'moderately_suitable': moderately_suitable,
                'stretch_goals': stretch_goals,
                'average_confidence': avg_confidence,
                'average_salary_match': avg_salary_match,
                'search_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            'top_recommendations': recommendations[:10],  # Top 10 for detailed review
            'career_insights': self.generate_career_insights(candidate_data, recommendations)
        }

        # Save detailed results to CSV
        csv_file = f"{self.results_folder}/{candidate_name.replace(' ', '_')}_job_recommendations_{timestamp}.csv"
        self.save_recommendations_to_csv(recommendations, csv_file)

        # Save summary report to JSON
        json_file = f"{self.results_folder}/{candidate_name.replace(' ', '_')}_career_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n📊 CAREER REPORT GENERATED")
        print(f"📁 Detailed results: {csv_file}")
        print(f"📁 Summary report: {json_file}")

        return report, csv_file, json_file

    def save_recommendations_to_csv(self, recommendations, filename):
        """
        Lưu job recommendations vào CSV file
        """
        csv_data = []
        for rec in recommendations:
            row = {
                'job_id': rec['job_id'],
                'job_title': rec['job_title'],
                'company': rec['company'],
                'department': rec['department'],
                'location': rec['location'],
                'salary_range': rec['salary_range'],
                'employment_type': rec['employment_type'],
                'prediction_score': rec['prediction'],
                'prediction_label': rec['prediction_label'],
                'confidence': rec['confidence'],
                'overall_score': rec['overall_score'],
                'skill_similarity': rec['features']['skill_similarity'],
                'experience_match': rec['features']['experience_match'],
                'education_match': rec['features']['education_match'],
                'location_compatibility': rec['features']['location_compatibility'],
                'salary_match': rec['salary_match'],
                'culture_match': rec['culture_match'],
                'match_reasons': ' | '.join(rec['match_reasons']),
                'growth_potential': ' | '.join(rec['growth_potential'])
            }
            csv_data.append(row)

        df = pd.DataFrame(csv_data)
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"✅ Recommendations saved to CSV: {filename}")

    def generate_career_insights(self, candidate_data, recommendations):
        """
        Tạo career insights cho candidate
        """
        insights = []

        if not recommendations:
            insights.append("❌ No suitable jobs found. Consider:")
            insights.append("   • Expanding your skill set")
            insights.append("   • Lowering salary expectations")
            insights.append("   • Considering remote opportunities")
            return insights

        # Skill analysis
        skill_matches = [r['features']['skill_similarity'] for r in recommendations]
        avg_skill_match = np.mean(skill_matches)

        if avg_skill_match > 0.7:
            insights.append("🎯 STRONG SKILL PROFILE: Your skills are highly valued in the market")
        elif avg_skill_match > 0.5:
            insights.append("✅ GOOD SKILL MATCH: You have relevant skills for most positions")
        else:
            insights.append("⚠️ SKILL GAP DETECTED: Consider developing these in-demand skills:")
            # Analyze most common required skills
            all_job_skills = []
            for rec in recommendations:
                # This would need job skills data - simplified for demo
                pass

        # Experience analysis
        exp_matches = [r['features']['experience_match'] for r in recommendations]
        avg_exp_match = np.mean(exp_matches)

        if avg_exp_match > 0.9:
            insights.append("💼 EXPERIENCE ADVANTAGE: You exceed requirements for most positions")
        elif avg_exp_match > 0.7:
            insights.append("💼 EXPERIENCE FIT: Your experience level is well-matched")
        else:
            insights.append("📈 GROWTH OPPORTUNITY: Many positions offer career advancement")

        # Salary analysis
        salary_matches = [r['salary_match'] for r in recommendations]
        avg_salary_match = np.mean(salary_matches)

        if avg_salary_match > 0.7:
            insights.append("💰 SALARY ALIGNMENT: Your expectations match market rates")
        else:
            insights.append("💰 SALARY CONSIDERATION: Market rates may differ from expectations")

        # Location analysis
        location_matches = [r['features']['location_compatibility'] for r in recommendations]
        remote_friendly = len([r for r in recommendations if 'remote' in r['location'].lower()])

        if remote_friendly > len(recommendations) * 0.3:
            insights.append("🌍 REMOTE OPPORTUNITIES: Many positions offer remote work")

        return insights

    def display_job_recommendations(self, report):
        """
        Hiển thị job recommendations cho candidate
        """
        print(f"\n🎯 JOB RECOMMENDATIONS FOR {report['candidate_info']['name']}")
        print("="*70)

        summary = report['search_summary']
        print(f"📊 SEARCH RESULTS:")
        print(f"Total Jobs Found: {summary['total_jobs_found']}")
        print(f"Highly Suitable: {summary['highly_suitable']} positions")
        print(f"Moderately Suitable: {summary['moderately_suitable']} positions")
        print(f"Stretch Goals: {summary['stretch_goals']} positions")
        print(f"Average Match Confidence: {summary['average_confidence']:.1%}")
        print(f"Average Salary Compatibility: {summary['average_salary_match']:.1%}")

        print(f"\n🏆 TOP 5 JOB OPPORTUNITIES:")
        print("-" * 100)
        print(f"{'Rank':<4} {'Job Title':<25} {'Company':<20} {'Confidence':<12} {'Overall Score':<13} {'Salary':<15}")
        print("-" * 100)

        for i, job in enumerate(report['top_recommendations'][:5], 1):
            print(f"{i:<4} {job['job_title'][:24]:<25} {job['company'][:19]:<20} {job['confidence']:<11.1%} {job['overall_score']:<12.1%} {job['salary_range'][:14]:<15}")

        print(f"\n💡 CAREER INSIGHTS:")
        for insight in report['career_insights']:
            print(f"   {insight}")

    def run_interactive_job_search(self):
        """
        Chạy job search process interactive cho candidate
        """
        print("🎯 JOB RECOMMENDATION SYSTEM")
        print("="*50)
        print("Welcome to the AI-powered job recommendation system!")

        while True:
            print(f"\n📋 MAIN MENU:")
            print("1. Create candidate profile template")
            print("2. Find job recommendations")
            print("3. View previous search results")
            print("4. Exit")

            choice = input("\nSelect option (1-4): ").strip()

            if choice == "1":
                self.create_candidate_profile_template()

            elif choice == "2":
                self.run_job_recommendation_search()

            elif choice == "3":
                self.view_previous_searches()

            elif choice == "4":
                print("👋 Thank you for using Job Recommendation System!")
                break

            else:
                print("❌ Invalid choice. Please select 1-4.")

    def run_job_recommendation_search(self):
        """
        Chạy quá trình tìm job recommendations
        """
        print(f"\n🔍 JOB RECOMMENDATION SEARCH")
        print("-" * 35)

        # Step 1: Load candidate profile
        print("Step 1: Load your profile")
        profile_files = list(Path("candidate_profiles").glob("*.json"))

        if not profile_files:
            print("❌ No candidate profile files found!")
            print("Please create a candidate profile first (Option 1)")
            return

        print("Available candidate profiles:")
        for i, file in enumerate(profile_files, 1):
            print(f"  {i}. {file.name}")

        try:
            file_choice = int(input(f"Select profile file (1-{len(profile_files)}): ")) - 1
            profile_file = profile_files[file_choice]
        except (ValueError, IndexError):
            print("❌ Invalid selection")
            return

        candidate_data = self.load_candidate_profile(profile_file)
        if not candidate_data:
            return

        # Step 2: Load available jobs
        print(f"\nStep 2: Loading available job positions...")
        available_jobs = self.load_available_jobs()
        if not available_jobs:
            print("❌ No job positions found")
            return

        # Step 3: Find recommendations
        print(f"\nStep 3: Finding job recommendations...")
        recommendations = self.recommend_jobs_for_candidate(candidate_data, available_jobs)

        # Step 4: Generate report
        print(f"\nStep 4: Generating career report...")
        report, csv_file, json_file = self.generate_candidate_report(candidate_data, recommendations)

        # Step 5: Display results
        self.display_job_recommendations(report)

        # Ask if candidate wants to see detailed job info
        if recommendations:
            show_details = input(f"\nWould you like to see detailed job information? (y/n): ").lower()
            if show_details == 'y':
                self.show_detailed_jobs(recommendations[:5])

    def show_detailed_jobs(self, jobs):
        """
        Hiển thị thông tin chi tiết của jobs
        """
        for i, job in enumerate(jobs, 1):
            print(f"\n{'='*70}")
            print(f"JOB OPPORTUNITY #{i}: {job['job_title']}")
            print(f"{'='*70}")
            print(f"🏢 Company: {job['company']}")
            print(f"📍 Location: {job['location']}")
            print(f"💰 Salary: {job['salary_range']}")
            print(f"📋 Department: {job['department']}")
            print(f"⏰ Type: {job['employment_type']}")

            print(f"\n🤖 AI ASSESSMENT:")
            print(f"   Match Level: {job['prediction_label']}")
            print(f"   Confidence: {job['confidence']:.1%}")
            print(f"   Overall Score: {job['overall_score']:.1%}")

            print(f"\n✅ WHY THIS JOB FITS YOU:")
            for reason in job['match_reasons']:
                print(f"   {reason}")

            if job['growth_potential']:
                print(f"\n🚀 GROWTH POTENTIAL:")
                for growth in job['growth_potential']:
                    print(f"   {growth}")

            print(f"\n📊 DETAILED MATCH SCORES:")
            features = job['features']
            print(f"   • Skill Similarity: {features['skill_similarity']:.1%}")
            print(f"   • Experience Match: {features['experience_match']:.1%}")
            print(f"   • Education Match: {features['education_match']:.1%}")
            print(f"   • Location Compatibility: {features['location_compatibility']:.1%}")
            print(f"   • Salary Compatibility: {job['salary_match']:.1%}")
            print(f"   • Culture Fit: {job['culture_match']:.1%}")

            if i < len(jobs):
                input("\nPress Enter to see next job opportunity...")

    def view_previous_searches(self):
        """
        Xem kết quả job search trước đó
        """
        results_files = list(Path(self.results_folder).glob("*_career_report_*.json"))

        if not results_files:
            print("❌ No previous search results found")
            return

        print(f"\n📁 PREVIOUS JOB SEARCHES:")
        for i, file in enumerate(results_files, 1):
            # Extract info from filename
            parts = file.stem.split('_')
            candidate_name = ' '.join(parts[:-3])
            date_time = '_'.join(parts[-2:])
            print(f"  {i}. {candidate_name} - {date_time}")

        try:
            choice = int(input(f"Select report to view (1-{len(results_files)}): ")) - 1
            selected_file = results_files[choice]
        except (ValueError, IndexError):
            print("❌ Invalid selection")
            return

        # Load and display report
        with open(selected_file, 'r', encoding='utf-8') as f:
            report = json.load(f)

        self.display_job_recommendations(report)


def main():
    """
    Main function để chạy Job Recommendation System
    """
    system = JobRecommendationSystem()
    system.run_interactive_job_search()


if __name__ == "__main__":
    main()
