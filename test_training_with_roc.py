#!/usr/bin/env python3
"""
Test script to run training and generate ROC curves
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run training and show results"""
    print("🎯 TESTING XGBOOST TRAINING WITH ROC CURVES")
    print("=" * 60)
    
    # Check if data file exists
    data_file = Path("progress/csv/jd_cr_similarity.csv")
    if not data_file.exists():
        print(f"❌ Data file not found: {data_file}")
        print("Please make sure the CSV file exists.")
        return
    
    print(f"✅ Data file found: {data_file}")
    print(f"📊 Starting training with 4 models...")
    print("   - Linear Regression")
    print("   - Decision Tree") 
    print("   - AdaBoost")
    print("   - XGBoost")
    print("\n🎨 Will generate ROC curves for all models")
    print("=" * 60)
    
    try:
        # Run training
        result = subprocess.run([
            sys.executable, "four_models_training.py"
        ], capture_output=True, text=True, timeout=600)  # 10 minutes timeout
        
        if result.returncode == 0:
            print("✅ Training completed successfully!")
            
            # Show output
            print("\n📊 Training Results:")
            print("-" * 40)
            print(result.stdout)
            
            # Check generated files
            print("\n📁 Generated Files:")
            files_to_check = [
                "four_models_results.csv",
                "four_models_detailed_results.csv", 
                "roc_curves_comparison.png",
                "api/models/xgboost_model.joblib",
                "api/models/model_metadata.json"
            ]
            
            for file_path in files_to_check:
                path = Path(file_path)
                if path.exists():
                    if path.suffix == '.png':
                        print(f"  ✅ {file_path} (Image)")
                    elif path.suffix == '.csv':
                        print(f"  ✅ {file_path} (CSV)")
                    elif path.suffix == '.joblib':
                        size = path.stat().st_size
                        print(f"  ✅ {file_path} ({size:,} bytes)")
                    else:
                        print(f"  ✅ {file_path}")
                else:
                    print(f"  ❌ {file_path} - NOT FOUND")
            
            print("\n🎉 Training and visualization completed!")
            print("📈 Check 'roc_curves_comparison.png' for ROC curves")
            print("📊 Check CSV files for detailed results")
            print("🤖 XGBoost model saved for API usage")
            
        else:
            print("❌ Training failed!")
            print("Error output:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("❌ Training timeout (10 minutes)")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
