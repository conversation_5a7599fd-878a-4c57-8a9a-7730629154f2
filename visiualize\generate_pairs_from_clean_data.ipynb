{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 🎯 Generate Pairs from Clean Data (Alternative)\n", "## Create Job-Candidate Pairs Directly from Clean Resume/Job Data\n", "\n", "**Use this if four categories extraction is not working**\n", "\n", "**Input**: \n", "- `data/clean_resumes.csv`\n", "- `data/clean_jobs.csv`\n", "\n", "**Output**: \n", "- `data/job_candidate_pairs_from_clean.csv`"]}, {"cell_type": "code", "execution_count": 1, "id": "setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Setup completed\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import random\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "random.seed(42)\n", "np.random.seed(42)\n", "\n", "print(\"✅ Setup completed\")"]}, {"cell_type": "code", "execution_count": 2, "id": "load-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Loaded data:\n", "   Resumes: 962\n", "   Jobs: 995\n", "   Potential pairs: 957,190\n"]}], "source": ["# Load clean data\n", "try:\n", "    clean_resumes = pd.read_csv('../data/clean/clean_resumes.csv')\n", "    clean_jobs = pd.read_csv('../data/clean/clean_jobs.csv')\n", "    \n", "    print(f\"✅ Loaded data:\")\n", "    print(f\"   Resumes: {len(clean_resumes):,}\")\n", "    print(f\"   Jobs: {len(clean_jobs):,}\")\n", "    print(f\"   Potential pairs: {len(clean_resumes) * len(clean_jobs):,}\")\n", "    \n", "except FileNotFoundError as e:\n", "    print(f\"❌ Error: {e}\")\n", "    print(\"Please run data_preprocessing.ipynb first\")"]}, {"cell_type": "code", "execution_count": 3, "id": "sample-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Using sample data for demonstration...\n", "Sample size:\n", "   Jobs: 20\n", "   Resumes: 50\n", "   Total pairs: 1,000\n", "\n", "📋 Job columns: ['id', 'title', 'title_clean', 'company', 'company_clean', 'location', 'location_clean', 'salary', 'salary_clean', 'work_type', 'description', 'requirements', 'skills', 'combined_text', 'clean_text', 'required_skills', 'skills_text', 'skills_count', 'required_experience', 'text_length', 'word_count', 'processed_date']\n", "📋 Resume columns: ['Category', 'category_clean', 'Resume', 'clean_text', 'extracted_skills', 'skills_text', 'skills_count', 'experience_years', 'text_length', 'word_count', 'processed_date']\n"]}], "source": ["# Use sample for demonstration (modify for full dataset)\n", "print(\"📊 Using sample data for demonstration...\")\n", "\n", "# Take sample\n", "sample_jobs = clean_jobs.head(20)  # First 20 jobs\n", "sample_resumes = clean_resumes.head(50)  # First 50 resumes\n", "\n", "print(f\"Sample size:\")\n", "print(f\"   Jobs: {len(sample_jobs)}\")\n", "print(f\"   Resumes: {len(sample_resumes)}\")\n", "print(f\"   Total pairs: {len(sample_jobs) * len(sample_resumes):,}\")\n", "\n", "# Show sample data structure\n", "print(f\"\\n📋 Job columns: {list(sample_jobs.columns)}\")\n", "print(f\"📋 Resume columns: {list(sample_resumes.columns)}\")"]}, {"cell_type": "code", "execution_count": 4, "id": "similarity-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Similarity functions defined\n"]}], "source": ["# Simple similarity functions\n", "def jaccard_similarity(list1, list2):\n", "    \"\"\"Calculate Jaccard similarity between two lists\"\"\"\n", "    if not list1 or not list2:\n", "        return 0.0\n", "    \n", "    # Convert to sets\n", "    set1 = set(str(item).lower() for item in list1 if pd.notna(item))\n", "    set2 = set(str(item).lower() for item in list2 if pd.notna(item))\n", "    \n", "    if not set1 or not set2:\n", "        return 0.0\n", "    \n", "    intersection = len(set1.intersection(set2))\n", "    union = len(set1.union(set2))\n", "    \n", "    return intersection / union if union > 0 else 0.0\n", "\n", "def text_similarity(text1, text2):\n", "    \"\"\"Calculate text similarity using TF-IDF\"\"\"\n", "    if pd.isna(text1) or pd.isna(text2) or text1 == '' or text2 == '':\n", "        return 0.0\n", "    \n", "    try:\n", "        vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')\n", "        tfidf_matrix = vectorizer.fit_transform([str(text1), str(text2)])\n", "        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]\n", "        return similarity\n", "    except:\n", "        return 0.0\n", "\n", "def parse_skills(skills_text):\n", "    \"\"\"Parse skills from text\"\"\"\n", "    if pd.isna(skills_text) or skills_text == '':\n", "        return []\n", "    \n", "    # Try to parse as list first\n", "    try:\n", "        if skills_text.startswith('[') and skills_text.endswith(']'):\n", "            return eval(skills_text)\n", "    except:\n", "        pass\n", "    \n", "    # Parse as comma-separated\n", "    if ',' in str(skills_text):\n", "        return [skill.strip() for skill in str(skills_text).split(',')]\n", "    \n", "    # Single skill\n", "    return [str(skills_text).strip()]\n", "\n", "print(\"✅ Similarity functions defined\")"]}, {"cell_type": "code", "execution_count": 5, "id": "generate-pairs", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Generating job-candidate pairs...\n", "Processing 1,000 combinations...\n", "   Processed 100/1000 (10.0%)\n", "   Processed 200/1000 (20.0%)\n", "   Processed 300/1000 (30.0%)\n", "   Processed 400/1000 (40.0%)\n", "   Processed 500/1000 (50.0%)\n", "   Processed 600/1000 (60.0%)\n", "   Processed 700/1000 (70.0%)\n", "   Processed 800/1000 (80.0%)\n", "   Processed 900/1000 (90.0%)\n", "   Processed 1000/1000 (100.0%)\n", "\n", "✅ Generated 1,000 pairs\n", "\n", "📊 Suitability distribution:\n", "   0 (Not Suitable): 979 (97.9%)\n", "   1 (Moderately Suitable): 21 (2.1%)\n", "   2 (Highly Suitable): 0 (0.0%)\n", "\n", "🔥 Top 5 matches:\n", "   JOB012 + CAND041: 0.440\n", "      Job: <PERSON><PERSON> (Network Solution Engineer)...\n", "      Candidate: Hr\n", "   JOB015 + CAND047: 0.412\n", "      Job: Ict Enterprise Sales Director...\n", "      Candidate: Hr\n", "   JOB007 + CAND047: 0.406\n", "      Job: Bridge Project Manager (Brse/ It Communicator)...\n", "      Candidate: Hr\n", "   JOB005 + CAND049: 0.401\n", "      Job: Quality Assurance Manager (Qam)...\n", "      Candidate: Hr\n", "   JOB006 + CAND049: 0.401\n", "      Job: Platform Manager (Cdn Ecosystems)...\n", "      Candidate: Hr\n"]}], "source": ["# Generate job-candidate pairs\n", "print(\"🔄 Generating job-candidate pairs...\")\n", "\n", "all_pairs = []\n", "pair_id = 1\n", "\n", "total_combinations = len(sample_jobs) * len(sample_resumes)\n", "print(f\"Processing {total_combinations:,} combinations...\")\n", "\n", "processed = 0\n", "for job_idx, job_row in sample_jobs.iterrows():\n", "    for resume_idx, resume_row in sample_resumes.iterrows():\n", "        # Extract features\n", "        job_skills = parse_skills(job_row.get('skills_text', ''))\n", "        resume_skills = parse_skills(resume_row.get('skills_text', ''))\n", "        \n", "        # Calculate similarities\n", "        skills_similarity = jaccard_similarity(job_skills, resume_skills)\n", "        \n", "        # Text similarity\n", "        job_text = str(job_row.get('clean_text', ''))\n", "        resume_text = str(resume_row.get('clean_text', ''))\n", "        text_sim = text_similarity(job_text, resume_text)\n", "        \n", "        # Experience match\n", "        job_exp = job_row.get('required_experience', 0)\n", "        resume_exp = resume_row.get('experience_years', 0)\n", "        \n", "        if job_exp > 0:\n", "            exp_match = min(resume_exp / job_exp, 1.0) if job_exp > 0 else 1.0\n", "        else:\n", "            exp_match = 1.0\n", "        \n", "        # Category match\n", "        job_title = str(job_row.get('title_clean', '')).lower()\n", "        resume_category = str(resume_row.get('category_clean', '')).lower()\n", "        \n", "        category_match = 1.0 if resume_category in job_title or job_title in resume_category else 0.0\n", "        \n", "        # Overall suitability\n", "        overall = (\n", "            skills_similarity * 0.4 +\n", "            text_sim * 0.2 +\n", "            exp_match * 0.2 +\n", "            category_match * 0.2\n", "        )\n", "        \n", "        # Suitability label\n", "        if overall >= 0.7:\n", "            label = 2  # Highly suitable\n", "        elif overall >= 0.4:\n", "            label = 1  # Moderately suitable\n", "        else:\n", "            label = 0  # Not suitable\n", "        \n", "        # Create pair\n", "        pair = {\n", "            'pair_id': pair_id,\n", "            'job_id': f\"JOB{job_idx:03d}\",\n", "            'candidate_id': f\"CAND{resume_idx:03d}\",\n", "            'skills_jaccard': round(skills_similarity, 3),\n", "            'text_similarity': round(text_sim, 3),\n", "            'experience_match': round(exp_match, 3),\n", "            'category_match': round(category_match, 3),\n", "            'overall_suitability': round(overall, 3),\n", "            'suitability_label': label,\n", "            'job_title': job_row.get('title_clean', ''),\n", "            'candidate_category': resume_row.get('category_clean', ''),\n", "            'job_required_exp': job_exp,\n", "            'candidate_exp': resume_exp\n", "        }\n", "        \n", "        all_pairs.append(pair)\n", "        pair_id += 1\n", "        processed += 1\n", "        \n", "        if processed % 100 == 0:\n", "            print(f\"   Processed {processed}/{total_combinations} ({processed/total_combinations*100:.1f}%)\")\n", "\n", "print(f\"\\n✅ Generated {len(all_pairs):,} pairs\")\n", "\n", "# Create DataFrame\n", "pairs_df = pd.DataFrame(all_pairs)\n", "\n", "# Show distribution\n", "print(f\"\\n📊 Suitability distribution:\")\n", "for label in [0, 1, 2]:\n", "    count = len(pairs_df[pairs_df['suitability_label'] == label])\n", "    label_name = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}[label]\n", "    print(f\"   {label} ({label_name}): {count:,} ({count/len(pairs_df)*100:.1f}%)\")\n", "\n", "# Show top matches\n", "print(f\"\\n🔥 Top 5 matches:\")\n", "top_5 = pairs_df.nlargest(5, 'overall_suitability')\n", "for _, row in top_5.iterrows():\n", "    print(f\"   {row['job_id']} + {row['candidate_id']}: {row['overall_suitability']:.3f}\")\n", "    print(f\"      Job: {row['job_title'][:50]}...\")\n", "    print(f\"      Candidate: {row['candidate_category']}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "export", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Exporting results...\n", "✅ Exported 1,000 pairs to: ../data/job_candidate_pairs_from_clean.csv\n", "✅ Summary saved\n", "\n", "🎉 PAIRS GENERATION FROM CLEAN DATA COMPLETED!\n", "==================================================\n", "📊 Results: 1,000 job-candidate pairs\n", "📁 Output: ../data/job_candidate_pairs_from_clean.csv\n", "\n", "💡 To process full dataset:\n", "   - Change 'sample_jobs = clean_jobs.head(20)' to 'sample_jobs = clean_jobs'\n", "   - Change 'sample_resumes = clean_resumes.head(50)' to 'sample_resumes = clean_resumes'\n", "   - This will create 957,190 pairs\n", "\n", "🚀 Ready for ML training!\n"]}], "source": ["# Export results\n", "print(\"💾 Exporting results...\")\n", "\n", "# Add metadata\n", "pairs_df['created_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "# Export main file\n", "output_file = '../data/job_candidate_pairs_from_clean.csv'\n", "pairs_df.to_csv(output_file, index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(pairs_df):,} pairs to: {output_file}\")\n", "\n", "# Export summary\n", "summary = {\n", "    'created_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "    'total_pairs': len(pairs_df),\n", "    'source_jobs': len(sample_jobs),\n", "    'source_resumes': len(sample_resumes),\n", "    'suitability_distribution': {\n", "        int(label): int(count) for label, count in pairs_df['suitability_label'].value_counts().items()\n", "    },\n", "    'similarity_stats': {\n", "        'skills_jaccard': {\n", "            'mean': float(pairs_df['skills_jaccard'].mean()),\n", "            'max': float(pairs_df['skills_jaccard'].max())\n", "        },\n", "        'overall_suitability': {\n", "            'mean': float(pairs_df['overall_suitability'].mean()),\n", "            'max': float(pairs_df['overall_suitability'].max())\n", "        }\n", "    }\n", "}\n", "\n", "import json\n", "with open('../data/pairs_from_clean_summary.json', 'w') as f:\n", "    json.dump(summary, f, indent=2)\n", "\n", "print(f\"✅ Summary saved\")\n", "\n", "print(\"\\n🎉 PAIRS GENERATION FROM CLEAN DATA COMPLETED!\")\n", "print(\"=\"*50)\n", "print(f\"📊 Results: {len(pairs_df):,} job-candidate pairs\")\n", "print(f\"📁 Output: {output_file}\")\n", "print(f\"\\n💡 To process full dataset:\")\n", "print(f\"   - Change 'sample_jobs = clean_jobs.head(20)' to 'sample_jobs = clean_jobs'\")\n", "print(f\"   - Change 'sample_resumes = clean_resumes.head(50)' to 'sample_resumes = clean_resumes'\")\n", "print(f\"   - This will create {len(clean_jobs) * len(clean_resumes):,} pairs\")\n", "print(f\"\\n🚀 Ready for ML training!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}