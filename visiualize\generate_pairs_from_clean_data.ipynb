import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import random
import warnings
warnings.filterwarnings('ignore')

random.seed(42)
np.random.seed(42)

print("✅ Setup completed")

# Load clean data
try:
    clean_resumes = pd.read_csv('../data/clean/clean_resumes.csv')
    clean_jobs = pd.read_csv('../data/clean/clean_jobs.csv')
    
    print(f"✅ Loaded data:")
    print(f"   Resumes: {len(clean_resumes):,}")
    print(f"   Jobs: {len(clean_jobs):,}")
    print(f"   Potential pairs: {len(clean_resumes) * len(clean_jobs):,}")
    
except FileNotFoundError as e:
    print(f"❌ Error: {e}")
    print("Please run data_preprocessing.ipynb first")

# Use sample for demonstration (modify for full dataset)
print("📊 Using sample data for demonstration...")

# Take sample
sample_jobs = clean_jobs.head(20)  # First 20 jobs
sample_resumes = clean_resumes.head(50)  # First 50 resumes

print(f"Sample size:")
print(f"   Jobs: {len(sample_jobs)}")
print(f"   Resumes: {len(sample_resumes)}")
print(f"   Total pairs: {len(sample_jobs) * len(sample_resumes):,}")

# Show sample data structure
print(f"\n📋 Job columns: {list(sample_jobs.columns)}")
print(f"📋 Resume columns: {list(sample_resumes.columns)}")

# Simple similarity functions
def jaccard_similarity(list1, list2):
    """Calculate Jaccard similarity between two lists"""
    if not list1 or not list2:
        return 0.0
    
    # Convert to sets
    set1 = set(str(item).lower() for item in list1 if pd.notna(item))
    set2 = set(str(item).lower() for item in list2 if pd.notna(item))
    
    if not set1 or not set2:
        return 0.0
    
    intersection = len(set1.intersection(set2))
    union = len(set1.union(set2))
    
    return intersection / union if union > 0 else 0.0

def text_similarity(text1, text2):
    """Calculate text similarity using TF-IDF"""
    if pd.isna(text1) or pd.isna(text2) or text1 == '' or text2 == '':
        return 0.0
    
    try:
        vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        tfidf_matrix = vectorizer.fit_transform([str(text1), str(text2)])
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        return similarity
    except:
        return 0.0

def parse_skills(skills_text):
    """Parse skills from text"""
    if pd.isna(skills_text) or skills_text == '':
        return []
    
    # Try to parse as list first
    try:
        if skills_text.startswith('[') and skills_text.endswith(']'):
            return eval(skills_text)
    except:
        pass
    
    # Parse as comma-separated
    if ',' in str(skills_text):
        return [skill.strip() for skill in str(skills_text).split(',')]
    
    # Single skill
    return [str(skills_text).strip()]

print("✅ Similarity functions defined")

# Generate job-candidate pairs
print("🔄 Generating job-candidate pairs...")

all_pairs = []
pair_id = 1

total_combinations = len(sample_jobs) * len(sample_resumes)
print(f"Processing {total_combinations:,} combinations...")

processed = 0
for job_idx, job_row in sample_jobs.iterrows():
    for resume_idx, resume_row in sample_resumes.iterrows():
        # Extract features
        job_skills = parse_skills(job_row.get('skills_text', ''))
        resume_skills = parse_skills(resume_row.get('skills_text', ''))
        
        # Calculate similarities
        skills_similarity = jaccard_similarity(job_skills, resume_skills)
        
        # Text similarity
        job_text = str(job_row.get('clean_text', ''))
        resume_text = str(resume_row.get('clean_text', ''))
        text_sim = text_similarity(job_text, resume_text)
        
        # Experience match
        job_exp = job_row.get('required_experience', 0)
        resume_exp = resume_row.get('experience_years', 0)
        
        if job_exp > 0:
            exp_match = min(resume_exp / job_exp, 1.0) if job_exp > 0 else 1.0
        else:
            exp_match = 1.0
        
        # Category match
        job_title = str(job_row.get('title_clean', '')).lower()
        resume_category = str(resume_row.get('category_clean', '')).lower()
        
        category_match = 1.0 if resume_category in job_title or job_title in resume_category else 0.0
        
        # Overall suitability
        overall = (
            skills_similarity * 0.4 +
            text_sim * 0.2 +
            exp_match * 0.2 +
            category_match * 0.2
        )
        
        # Suitability label
        if overall >= 0.7:
            label = 2  # Highly suitable
        elif overall >= 0.4:
            label = 1  # Moderately suitable
        else:
            label = 0  # Not suitable
        
        # Create pair
        pair = {
            'pair_id': pair_id,
            'job_id': f"JOB{job_idx:03d}",
            'candidate_id': f"CAND{resume_idx:03d}",
            'skills_jaccard': round(skills_similarity, 3),
            'text_similarity': round(text_sim, 3),
            'experience_match': round(exp_match, 3),
            'category_match': round(category_match, 3),
            'overall_suitability': round(overall, 3),
            'suitability_label': label,
            'job_title': job_row.get('title_clean', ''),
            'candidate_category': resume_row.get('category_clean', ''),
            'job_required_exp': job_exp,
            'candidate_exp': resume_exp
        }
        
        all_pairs.append(pair)
        pair_id += 1
        processed += 1
        
        if processed % 100 == 0:
            print(f"   Processed {processed}/{total_combinations} ({processed/total_combinations*100:.1f}%)")

print(f"\n✅ Generated {len(all_pairs):,} pairs")

# Create DataFrame
pairs_df = pd.DataFrame(all_pairs)

# Show distribution
print(f"\n📊 Suitability distribution:")
for label in [0, 1, 2]:
    count = len(pairs_df[pairs_df['suitability_label'] == label])
    label_name = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}[label]
    print(f"   {label} ({label_name}): {count:,} ({count/len(pairs_df)*100:.1f}%)")

# Show top matches
print(f"\n🔥 Top 5 matches:")
top_5 = pairs_df.nlargest(5, 'overall_suitability')
for _, row in top_5.iterrows():
    print(f"   {row['job_id']} + {row['candidate_id']}: {row['overall_suitability']:.3f}")
    print(f"      Job: {row['job_title'][:50]}...")
    print(f"      Candidate: {row['candidate_category']}")

# Export results
print("💾 Exporting results...")

# Add metadata
pairs_df['created_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# Export main file
output_file = '../data/job_candidate_pairs_from_clean.csv'
pairs_df.to_csv(output_file, index=False, encoding='utf-8')
print(f"✅ Exported {len(pairs_df):,} pairs to: {output_file}")

# Export summary
summary = {
    'created_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    'total_pairs': len(pairs_df),
    'source_jobs': len(sample_jobs),
    'source_resumes': len(sample_resumes),
    'suitability_distribution': {
        int(label): int(count) for label, count in pairs_df['suitability_label'].value_counts().items()
    },
    'similarity_stats': {
        'skills_jaccard': {
            'mean': float(pairs_df['skills_jaccard'].mean()),
            'max': float(pairs_df['skills_jaccard'].max())
        },
        'overall_suitability': {
            'mean': float(pairs_df['overall_suitability'].mean()),
            'max': float(pairs_df['overall_suitability'].max())
        }
    }
}

import json
with open('../data/pairs_from_clean_summary.json', 'w') as f:
    json.dump(summary, f, indent=2)

print(f"✅ Summary saved")

print("\n🎉 PAIRS GENERATION FROM CLEAN DATA COMPLETED!")
print("="*50)
print(f"📊 Results: {len(pairs_df):,} job-candidate pairs")
print(f"📁 Output: {output_file}")
print(f"\n💡 To process full dataset:")
print(f"   - Change 'sample_jobs = clean_jobs.head(20)' to 'sample_jobs = clean_jobs'")
print(f"   - Change 'sample_resumes = clean_resumes.head(50)' to 'sample_resumes = clean_resumes'")
print(f"   - This will create {len(clean_jobs) * len(clean_resumes):,} pairs")
print(f"\n🚀 Ready for ML training!")