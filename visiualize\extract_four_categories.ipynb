{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 🎯 Extract Four Categories from Clean Data\n", "## Linguistic Analysis: Primary Skills, Secondary Skills, Adjectives, Adverbs\n", "\n", "**Objective**: Extract 4 linguistic categories from clean resume and job data following research methodology\n", "\n", "**Input**: \n", "- `data/clean_resumes.csv` (Processed candidate resumes)\n", "- `data/clean_jobs.csv` (Processed job descriptions)\n", "\n", "**Output**: \n", "- `data/primary_skills.csv` (Core technical skills)\n", "- `data/secondary_skills.csv` (Supporting skills and soft skills)\n", "- `data/adjectives.csv` (Descriptive adjectives)\n", "- `data/adverbs.csv` (Descriptive adverbs)\n", "\n", "**Based on**: Academic research methodology for job-resume text analysis"]}, {"cell_type": "markdown", "id": "setup", "metadata": {}, "source": ["## 🔧 Setup and Configuration"]}, {"cell_type": "code", "execution_count": 1, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully\n", "📅 Processing started at: 2025-06-17 22:16:47\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package wordnet to C:\\Users\\<USER>", "[nltk_data]     ASPIRE\\AppData\\Roaming\\nltk_data...\n", "[nltk_data]   Package wordnet is already up-to-date!\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "from datetime import datetime\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# NLP libraries\n", "import nltk\n", "from nltk.tokenize import word_tokenize, sent_tokenize\n", "from nltk.tag import pos_tag\n", "from nltk.corpus import stopwords\n", "from nltk.stem import WordNetLemmatizer\n", "\n", "# Download required NLTK data\n", "nltk_downloads = ['punkt', 'averaged_perceptron_tagger', 'stopwords', 'wordnet']\n", "for item in nltk_downloads:\n", "    try:\n", "        nltk.data.find(f'tokenizers/{item}' if item == 'punkt' else f'taggers/{item}' if 'tagger' in item else f'corpora/{item}')\n", "    except LookupError:\n", "        nltk.download(item)\n", "\n", "print(\"✅ Libraries imported successfully\")\n", "print(f\"📅 Processing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "id": "load-data", "metadata": {}, "source": ["## 📂 Load Clean Data"]}, {"cell_type": "code", "execution_count": 2, "id": "load-clean-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Loading clean datasets...\n", "✅ Loaded 962 clean resume records\n", "✅ Loaded 995 clean job records\n", "\n", "📊 Total text records to process: 1,957\n", "📋 Resume categories: 25\n", "🏢 Job companies: 510\n"]}], "source": ["# Load clean datasets\n", "print(\"📊 Loading clean datasets...\")\n", "\n", "try:\n", "    # Load clean resume data\n", "    clean_resumes = pd.read_csv('../data/clean_resumes.csv')\n", "    print(f\"✅ Loaded {len(clean_resumes):,} clean resume records\")\n", "    \n", "    # Load clean job data\n", "    clean_jobs = pd.read_csv('../data/clean_jobs.csv')\n", "    print(f\"✅ Loaded {len(clean_jobs):,} clean job records\")\n", "    \n", "except FileNotFoundError as e:\n", "    print(f\"❌ Error loading data: {e}\")\n", "    print(\"Please run data_preprocessing.ipynb first to generate clean data\")\n", "\n", "# Display basic info\n", "print(f\"\\n📊 Total text records to process: {len(clean_resumes) + len(clean_jobs):,}\")\n", "print(f\"📋 Resume categories: {clean_resumes['category_clean'].nunique()}\")\n", "print(f\"🏢 Job companies: {clean_jobs['company_clean'].nunique()}\")"]}, {"cell_type": "markdown", "id": "category-definitions", "metadata": {}, "source": ["## 📚 Category Definitions and Extraction Functions"]}, {"cell_type": "code", "execution_count": 3, "id": "category-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Category definitions loaded\n", "📊 Primary skills categories: 4\n", "📊 Secondary skills categories: 4\n", "📊 Job adjectives: 43\n", "📊 Job adverbs: 36\n"]}], "source": ["# Initialize NLP tools\n", "lemmatizer = WordNetLemmatizer()\n", "stop_words = set(stopwords.words('english'))\n", "\n", "# Define Primary Skills (Core Technical Skills)\n", "PRIMARY_SKILLS = {\n", "    # Programming Languages\n", "    'programming_languages': [\n", "        'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift',\n", "        'kotlin', 'typescript', 'scala', 'r', 'matlab', 'perl', 'shell', 'bash', 'powershell'\n", "    ],\n", "    \n", "    # Frameworks & Libraries\n", "    'frameworks': [\n", "        'react', 'angular', 'vue', 'nodejs', 'express', 'django', 'flask', 'spring', 'laravel',\n", "        'symfony', 'rails', 'asp.net', 'jquery', 'bootstrap', 'tensorflow', 'pytorch', 'keras'\n", "    ],\n", "    \n", "    # Databases\n", "    'databases': [\n", "        'mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sql server', 'sqlite',\n", "        'cassandra', 'elasticsearch', 'neo4j', 'dynamodb'\n", "    ],\n", "    \n", "    # Cloud & DevOps\n", "    'cloud_devops': [\n", "        'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'gitlab', 'github',\n", "        'terraform', 'ansible', 'chef', 'puppet', 'vagrant'\n", "    ]\n", "}\n", "\n", "# Define Secondary Skills (Supporting & Soft Skills)\n", "SECONDARY_SKILLS = {\n", "    # Soft Skills\n", "    'soft_skills': [\n", "        'leadership', 'communication', 'teamwork', 'problem solving', 'analytical thinking',\n", "        'creativity', 'adaptability', 'time management', 'project management', 'collaboration'\n", "    ],\n", "    \n", "    # Methodologies\n", "    'methodologies': [\n", "        'agile', 'scrum', 'kanban', 'waterfall', 'devops', 'ci/cd', 'tdd', 'bdd',\n", "        'microservices', 'rest api', 'graphql', 'soap'\n", "    ],\n", "    \n", "    # Tools & Platforms\n", "    'tools': [\n", "        'git', 'jira', 'confluence', 'slack', 'trello', 'notion', 'figma', 'sketch',\n", "        'photoshop', 'illustrator', 'tableau', 'power bi', 'excel'\n", "    ],\n", "    \n", "    # Operating Systems\n", "    'operating_systems': [\n", "        'linux', 'windows', 'macos', 'ubuntu', 'centos', 'debian', 'redhat'\n", "    ]\n", "}\n", "\n", "# Define Job-related Adjectives\n", "JOB_ADJECTIVES = [\n", "    # Experience Level\n", "    'senior', 'junior', 'experienced', 'skilled', 'expert', 'advanced', 'beginner',\n", "    'professional', 'certified', 'qualified',\n", "    \n", "    # Quality Descriptors\n", "    'excellent', 'outstanding', 'exceptional', 'strong', 'solid', 'proven', 'reliable',\n", "    'efficient', 'effective', 'innovative', 'creative', 'analytical', 'detail-oriented',\n", "    \n", "    # Technical Descriptors\n", "    'technical', 'hands-on', 'practical', 'theoretical', 'comprehensive', 'extensive',\n", "    'in-depth', 'broad', 'specialized', 'versatile', 'flexible', 'adaptable',\n", "    \n", "    # Work Style\n", "    'collaborative', 'independent', 'self-motivated', 'proactive', 'results-driven',\n", "    'goal-oriented', 'customer-focused', 'team-oriented'\n", "]\n", "\n", "# Define Job-related Adverbs\n", "JOB_ADVERBS = [\n", "    # Performance\n", "    'efficiently', 'effectively', 'successfully', 'consistently', 'reliably',\n", "    'accurately', 'precisely', 'thoroughly', 'carefully', 'systematically',\n", "    \n", "    # Speed & Frequency\n", "    'quickly', 'rapidly', 'immediately', 'frequently', 'regularly', 'continuously',\n", "    'constantly', 'daily', 'weekly', 'monthly',\n", "    \n", "    # Quality\n", "    'excellently', 'professionally', 'skillfully', 'expertly', 'competently',\n", "    'confidently', 'independently', 'collaboratively',\n", "    \n", "    # <PERSON>er\n", "    'creatively', 'innovatively', 'analytically', 'strategically', 'tactically',\n", "    'methodically', 'logically', 'practically'\n", "]\n", "\n", "print(\"✅ Category definitions loaded\")\n", "print(f\"📊 Primary skills categories: {len(PRIMARY_SKILLS)}\")\n", "print(f\"📊 Secondary skills categories: {len(SECONDARY_SKILLS)}\")\n", "print(f\"📊 Job adjectives: {len(JOB_ADJECTIVES)}\")\n", "print(f\"📊 Job adverbs: {len(JOB_ADVERBS)}\")"]}, {"cell_type": "markdown", "id": "extraction-functions", "metadata": {}, "source": ["## 🔍 Text Analysis and Extraction Functions"]}, {"cell_type": "code", "execution_count": 4, "id": "extraction-logic", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Extraction functions defined\n"]}], "source": ["def extract_primary_skills(text):\n", "    \"\"\"\n", "    Extract primary technical skills from text\n", "    \"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return []\n", "    \n", "    text_lower = str(text).lower()\n", "    found_skills = []\n", "    \n", "    # Check all primary skill categories\n", "    for category, skills in PRIMARY_SKILLS.items():\n", "        for skill in skills:\n", "            if skill in text_lower:\n", "                found_skills.append({\n", "                    'skill': skill,\n", "                    'category': category,\n", "                    'type': 'primary'\n", "                })\n", "    \n", "    return found_skills\n", "\n", "def extract_secondary_skills(text):\n", "    \"\"\"\n", "    Extract secondary skills from text\n", "    \"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return []\n", "    \n", "    text_lower = str(text).lower()\n", "    found_skills = []\n", "    \n", "    # Check all secondary skill categories\n", "    for category, skills in SECONDARY_SKILLS.items():\n", "        for skill in skills:\n", "            if skill in text_lower:\n", "                found_skills.append({\n", "                    'skill': skill,\n", "                    'category': category,\n", "                    'type': 'secondary'\n", "                })\n", "    \n", "    return found_skills\n", "\n", "def extract_adjectives_adverbs(text):\n", "    \"\"\"\n", "    Extract adjectives and adverbs using POS tagging\n", "    \"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return {'adjectives': [], 'adverbs': []}\n", "    \n", "    try:\n", "        # Tokenize and POS tag\n", "        tokens = word_tokenize(str(text).lower())\n", "        pos_tags = pos_tag(tokens)\n", "        \n", "        adjectives = []\n", "        adverbs = []\n", "        \n", "        for word, pos in pos_tags:\n", "            # Skip stop words and short words\n", "            if word in stop_words or len(word) < 3:\n", "                continue\n", "            \n", "            # Extract adjectives (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, JJS)\n", "            if pos.startswith('JJ'):\n", "                # Check if it's a job-related adjective\n", "                if word in JOB_ADJECTIVES:\n", "                    adjectives.append({\n", "                        'word': word,\n", "                        'pos': pos,\n", "                        'type': 'job_related'\n", "                    })\n", "                else:\n", "                    adjectives.append({\n", "                        'word': word,\n", "                        'pos': pos,\n", "                        'type': 'general'\n", "                    })\n", "            \n", "            # Extract adverbs (RB, RBR, RBS)\n", "            elif pos.startswith('RB'):\n", "                # Check if it's a job-related adverb\n", "                if word in JOB_ADVERBS:\n", "                    adverbs.append({\n", "                        'word': word,\n", "                        'pos': pos,\n", "                        'type': 'job_related'\n", "                    })\n", "                else:\n", "                    adverbs.append({\n", "                        'word': word,\n", "                        'pos': pos,\n", "                        'type': 'general'\n", "                    })\n", "        \n", "        return {'adjectives': adjectives, 'adverbs': adverbs}\n", "    \n", "    except Exception as e:\n", "        return {'adjectives': [], 'adverbs': []}\n", "\n", "def process_text_record(text, record_id, source_type):\n", "    \"\"\"\n", "    Process a single text record and extract all categories\n", "    \"\"\"\n", "    # Extract primary skills\n", "    primary_skills = extract_primary_skills(text)\n", "    \n", "    # Extract secondary skills\n", "    secondary_skills = extract_secondary_skills(text)\n", "    \n", "    # Extract adjectives and adverbs\n", "    adj_adv = extract_adjectives_adverbs(text)\n", "    \n", "    return {\n", "        'record_id': record_id,\n", "        'source_type': source_type,  # 'resume' or 'job'\n", "        'primary_skills': primary_skills,\n", "        'secondary_skills': secondary_skills,\n", "        'adjectives': adj_adv['adjectives'],\n", "        'adverbs': adj_adv['adverbs']\n", "    }\n", "\n", "print(\"✅ Extraction functions defined\")"]}, {"cell_type": "markdown", "id": "process-data", "metadata": {}, "source": ["## 🔄 Process All Data Records"]}, {"cell_type": "code", "execution_count": 5, "id": "process-all-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Processing all text records...\n", "📄 Processing 962 resume records...\n", "\n", "🏢 Processing 995 job records...\n", "   Processed 1,000/1,957 records (51.1%)\n", "\n", "✅ Processing completed: 1,957 records processed\n"]}], "source": ["# Process all resume and job records\n", "print(\"🔄 Processing all text records...\")\n", "\n", "all_results = []\n", "total_records = len(clean_resumes) + len(clean_jobs)\n", "processed_count = 0\n", "\n", "# Process resume records\n", "print(f\"📄 Processing {len(clean_resumes):,} resume records...\")\n", "for idx, row in clean_resumes.iterrows():\n", "    result = process_text_record(\n", "        text=row['clean_text'],\n", "        record_id=f\"resume_{idx}\",\n", "        source_type='resume'\n", "    )\n", "    \n", "    # Add additional resume info\n", "    result['category'] = row['category_clean']\n", "    result['experience_years'] = row['experience_years']\n", "    \n", "    all_results.append(result)\n", "    processed_count += 1\n", "    \n", "    # Progress update\n", "    if processed_count % 1000 == 0:\n", "        print(f\"   Processed {processed_count:,}/{total_records:,} records ({processed_count/total_records*100:.1f}%)\")\n", "\n", "# Process job records\n", "print(f\"\\n🏢 Processing {len(clean_jobs):,} job records...\")\n", "for idx, row in clean_jobs.iterrows():\n", "    result = process_text_record(\n", "        text=row['clean_text'],\n", "        record_id=f\"job_{row['id'] if 'id' in row else idx}\",\n", "        source_type='job'\n", "    )\n", "    \n", "    # Add additional job info\n", "    result['job_title'] = row['title_clean']\n", "    result['company'] = row['company_clean']\n", "    result['location'] = row['location_clean']\n", "    result['required_experience'] = row['required_experience']\n", "    \n", "    all_results.append(result)\n", "    processed_count += 1\n", "    \n", "    # Progress update\n", "    if processed_count % 1000 == 0:\n", "        print(f\"   Processed {processed_count:,}/{total_records:,} records ({processed_count/total_records*100:.1f}%)\")\n", "\n", "print(f\"\\n✅ Processing completed: {len(all_results):,} records processed\")"]}, {"cell_type": "markdown", "id": "create-datasets", "metadata": {}, "source": ["## 📊 Create Category-Specific Datasets"]}, {"cell_type": "code", "execution_count": 6, "id": "create-category-datasets", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Creating category-specific datasets...\n", "✅ Primary Skills: 8,711 records\n", "✅ Secondary Skills: 4,003 records\n", "✅ Adjectives: 0 records\n", "✅ Adverbs: 0 records\n", "\n", "📊 Total extracted items: 12,714\n"]}], "source": ["# Create separate datasets for each category\n", "print(\"📊 Creating category-specific datasets...\")\n", "\n", "# 1. Primary Skills Dataset\n", "primary_skills_data = []\n", "for result in all_results:\n", "    for skill in result['primary_skills']:\n", "        primary_skills_data.append({\n", "            'record_id': result['record_id'],\n", "            'source_type': result['source_type'],\n", "            'skill': skill['skill'],\n", "            'skill_category': skill['category'],\n", "            'category': result.get('category', ''),\n", "            'job_title': result.get('job_title', ''),\n", "            'company': result.get('company', ''),\n", "            'location': result.get('location', ''),\n", "            'experience_years': result.get('experience_years', 0),\n", "            'required_experience': result.get('required_experience', 0)\n", "        })\n", "\n", "primary_skills_df = pd.DataFrame(primary_skills_data)\n", "print(f\"✅ Primary Skills: {len(primary_skills_df):,} records\")\n", "\n", "# 2. Secondary Skills Dataset\n", "secondary_skills_data = []\n", "for result in all_results:\n", "    for skill in result['secondary_skills']:\n", "        secondary_skills_data.append({\n", "            'record_id': result['record_id'],\n", "            'source_type': result['source_type'],\n", "            'skill': skill['skill'],\n", "            'skill_category': skill['category'],\n", "            'category': result.get('category', ''),\n", "            'job_title': result.get('job_title', ''),\n", "            'company': result.get('company', ''),\n", "            'location': result.get('location', ''),\n", "            'experience_years': result.get('experience_years', 0),\n", "            'required_experience': result.get('required_experience', 0)\n", "        })\n", "\n", "secondary_skills_df = pd.DataFrame(secondary_skills_data)\n", "print(f\"✅ Secondary Skills: {len(secondary_skills_df):,} records\")\n", "\n", "# 3. Adjectives Dataset\n", "adjectives_data = []\n", "for result in all_results:\n", "    for adj in result['adjectives']:\n", "        adjectives_data.append({\n", "            'record_id': result['record_id'],\n", "            'source_type': result['source_type'],\n", "            'adjective': adj['word'],\n", "            'pos_tag': adj['pos'],\n", "            'adj_type': adj['type'],\n", "            'category': result.get('category', ''),\n", "            'job_title': result.get('job_title', ''),\n", "            'company': result.get('company', ''),\n", "            'location': result.get('location', ''),\n", "            'experience_years': result.get('experience_years', 0),\n", "            'required_experience': result.get('required_experience', 0)\n", "        })\n", "\n", "adjectives_df = pd.DataFrame(adjectives_data)\n", "print(f\"✅ Adjectives: {len(adjectives_df):,} records\")\n", "\n", "# 4. Adverbs Dataset\n", "adverbs_data = []\n", "for result in all_results:\n", "    for adv in result['adverbs']:\n", "        adverbs_data.append({\n", "            'record_id': result['record_id'],\n", "            'source_type': result['source_type'],\n", "            'adverb': adv['word'],\n", "            'pos_tag': adv['pos'],\n", "            'adv_type': adv['type'],\n", "            'category': result.get('category', ''),\n", "            'job_title': result.get('job_title', ''),\n", "            'company': result.get('company', ''),\n", "            'location': result.get('location', ''),\n", "            'experience_years': result.get('experience_years', 0),\n", "            'required_experience': result.get('required_experience', 0)\n", "        })\n", "\n", "adverbs_df = pd.DataFrame(adverbs_data)\n", "print(f\"✅ Adverbs: {len(adverbs_df):,} records\")\n", "\n", "print(f\"\\n📊 Total extracted items: {len(primary_skills_df) + len(secondary_skills_df) + len(adjectives_df) + len(adverbs_df):,}\")"]}, {"cell_type": "markdown", "id": "analysis", "metadata": {}, "source": ["## 📈 Category Analysis and Statistics"]}, {"cell_type": "code", "execution_count": 7, "id": "category-analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 Analyzing extracted categories...\n", "\n", "🎯 PRIMARY SKILLS ANALYSIS:\n", "Total primary skills extracted: 8,711\n", "Unique primary skills: 55\n", "From resumes: 4,052\n", "From jobs: 4,659\n", "\n", "🔥 Top 10 Primary Skills:\n", "  • r: 1,957\n", "  • go: 993\n", "  • java: 756\n", "  • python: 379\n", "  • javascript: 323\n", "  • aws: 321\n", "  • php: 312\n", "  • scala: 276\n", "  • mysql: 250\n", "  • nodejs: 210\n", "\n", "📊 Primary Skills by Category:\n", "  • programming_languages: 5,550\n", "  • frameworks: 1,296\n", "  • cloud_devops: 1,088\n", "  • databases: 777\n", "\n", "🎯 SECONDARY SKILLS ANALYSIS:\n", "Total secondary skills extracted: 4,003\n", "Unique secondary skills: 39\n", "From resumes: 1,833\n", "From jobs: 2,170\n", "\n", "🔥 Top 10 Secondary Skills:\n", "  • communication: 695\n", "  • windows: 380\n", "  • agile: 373\n", "  • excel: 320\n", "  • git: 306\n", "  • scrum: 233\n", "  • linux: 228\n", "  • devops: 197\n", "  • problem solving: 159\n", "  • jira: 153\n", "\n", "🎯 ADJECTIVES ANALYSIS:\n", "Total adjectives extracted: 0\n"]}, {"ename": "KeyError", "evalue": "'adjective'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 36\u001b[0m\n\u001b[0;32m     34\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m🎯 ADJECTIVES ANALYSIS:\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     35\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTotal adjectives extracted: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(adjectives_df)\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m,\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m---> 36\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnique adjectives: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43madjectives_df\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43madjective\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mnunique()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     37\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mJob-related adjectives: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(adjectives_df[adjectives_df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124madj_type\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;250m \u001b[39m\u001b[38;5;241m==\u001b[39m\u001b[38;5;250m \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mjob_related\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m,\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     38\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGeneral adjectives: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(adjectives_df[adjectives_df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124madj_type\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;250m \u001b[39m\u001b[38;5;241m==\u001b[39m\u001b[38;5;250m \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgeneral\u001b[39m\u001b[38;5;124m'\u001b[39m])\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m,\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\frame.py:3761\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   3760\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3761\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3762\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3763\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\indexes\\range.py:349\u001b[0m, in \u001b[0;36mRangeIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    347\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m    348\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(key, <PERSON><PERSON><PERSON>):\n\u001b[1;32m--> 349\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key)\n\u001b[0;32m    350\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n\u001b[0;32m    351\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'adjective'"]}], "source": ["# Analyze extracted categories\n", "print(\"📈 Analyzing extracted categories...\")\n", "\n", "# Primary Skills Analysis\n", "print(\"\\n🎯 PRIMARY SKILLS ANALYSIS:\")\n", "print(f\"Total primary skills extracted: {len(primary_skills_df):,}\")\n", "print(f\"Unique primary skills: {primary_skills_df['skill'].nunique()}\")\n", "print(f\"From resumes: {len(primary_skills_df[primary_skills_df['source_type'] == 'resume']):,}\")\n", "print(f\"From jobs: {len(primary_skills_df[primary_skills_df['source_type'] == 'job']):,}\")\n", "\n", "print(\"\\n🔥 Top 10 Primary Skills:\")\n", "top_primary = primary_skills_df['skill'].value_counts().head(10)\n", "for skill, count in top_primary.items():\n", "    print(f\"  • {skill}: {count:,}\")\n", "\n", "print(\"\\n📊 Primary Skills by Category:\")\n", "primary_by_cat = primary_skills_df['skill_category'].value_counts()\n", "for cat, count in primary_by_cat.items():\n", "    print(f\"  • {cat}: {count:,}\")\n", "\n", "# Secondary Skills Analysis\n", "print(\"\\n🎯 SECONDARY SKILLS ANALYSIS:\")\n", "print(f\"Total secondary skills extracted: {len(secondary_skills_df):,}\")\n", "print(f\"Unique secondary skills: {secondary_skills_df['skill'].nunique()}\")\n", "print(f\"From resumes: {len(secondary_skills_df[secondary_skills_df['source_type'] == 'resume']):,}\")\n", "print(f\"From jobs: {len(secondary_skills_df[secondary_skills_df['source_type'] == 'job']):,}\")\n", "\n", "print(\"\\n🔥 Top 10 Secondary Skills:\")\n", "top_secondary = secondary_skills_df['skill'].value_counts().head(10)\n", "for skill, count in top_secondary.items():\n", "    print(f\"  • {skill}: {count:,}\")\n", "\n", "# Adjectives Analysis\n", "print(\"\\n🎯 ADJECTIVES ANALYSIS:\")\n", "print(f\"Total adjectives extracted: {len(adjectives_df):,}\")\n", "print(f\"Unique adjectives: {adjectives_df['adjective'].nunique()}\")\n", "print(f\"Job-related adjectives: {len(adjectives_df[adjectives_df['adj_type'] == 'job_related']):,}\")\n", "print(f\"General adjectives: {len(adjectives_df[adjectives_df['adj_type'] == 'general']):,}\")\n", "\n", "print(\"\\n🔥 Top 10 Job-Related Adjectives:\")\n", "job_adj = adjectives_df[adjectives_df['adj_type'] == 'job_related']['adjective'].value_counts().head(10)\n", "for adj, count in job_adj.items():\n", "    print(f\"  • {adj}: {count:,}\")\n", "\n", "# Adverbs Analysis\n", "print(\"\\n🎯 ADVERBS ANALYSIS:\")\n", "print(f\"Total adverbs extracted: {len(adverbs_df):,}\")\n", "print(f\"Unique adverbs: {adverbs_df['adverb'].nunique()}\")\n", "print(f\"Job-related adverbs: {len(adverbs_df[adverbs_df['adv_type'] == 'job_related']):,}\")\n", "print(f\"General adverbs: {len(adverbs_df[adverbs_df['adv_type'] == 'general']):,}\")\n", "\n", "print(\"\\n🔥 Top 10 Job-Related Adverbs:\")\n", "job_adv = adverbs_df[adverbs_df['adv_type'] == 'job_related']['adverb'].value_counts().head(10)\n", "for adv, count in job_adv.items():\n", "    print(f\"  • {adv}: {count:,}\")\n", "\n", "print(\"\\n✅ Category analysis completed\")"]}, {"cell_type": "markdown", "id": "export-data", "metadata": {}, "source": ["## 💾 Export Category Datasets"]}, {"cell_type": "code", "execution_count": null, "id": "export-categories", "metadata": {}, "outputs": [], "source": ["# Export all category datasets\n", "print(\"💾 Exporting category datasets...\")\n", "\n", "# Add processing metadata\n", "processing_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "for df, name in [(primary_skills_df, 'primary_skills'),\n", "                 (secondary_skills_df, 'secondary_skills'),\n", "                 (adjectives_df, 'adjectives'),\n", "                 (adverbs_df, 'adverbs')]:\n", "    df['extracted_date'] = processing_date\n", "\n", "# Export to CSV files\n", "primary_skills_df.to_csv('../data/primary_skills.csv', index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(primary_skills_df):,} primary skills to: data/primary_skills.csv\")\n", "\n", "secondary_skills_df.to_csv('../data/secondary_skills.csv', index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(secondary_skills_df):,} secondary skills to: data/secondary_skills.csv\")\n", "\n", "adjectives_df.to_csv('../data/adjectives.csv', index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(adjectives_df):,} adjectives to: data/adjectives.csv\")\n", "\n", "adverbs_df.to_csv('../data/adverbs.csv', index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(adverbs_df):,} adverbs to: data/adverbs.csv\")\n", "\n", "# Create summary report\n", "summary_report = {\n", "    'extraction_info': {\n", "        'extracted_date': processing_date,\n", "        'source_files': {\n", "            'clean_resumes': '../data/clean/clean_resumes.csv',\n", "            'clean_jobs': '../data/clean/clean_jobs.csv'\n", "        },\n", "        'total_records_processed': len(all_results)\n", "    },\n", "    'category_statistics': {\n", "        'primary_skills': {\n", "            'total_extracted': len(primary_skills_df),\n", "            'unique_skills': primary_skills_df['skill'].nunique(),\n", "            'from_resumes': len(primary_skills_df[primary_skills_df['source_type'] == 'resume']),\n", "            'from_jobs': len(primary_skills_df[primary_skills_df['source_type'] == 'job']),\n", "            'top_skills': primary_skills_df['skill'].value_counts().head(10).to_dict()\n", "        },\n", "        'secondary_skills': {\n", "            'total_extracted': len(secondary_skills_df),\n", "            'unique_skills': secondary_skills_df['skill'].nunique(),\n", "            'from_resumes': len(secondary_skills_df[secondary_skills_df['source_type'] == 'resume']),\n", "            'from_jobs': len(secondary_skills_df[secondary_skills_df['source_type'] == 'job']),\n", "            'top_skills': secondary_skills_df['skill'].value_counts().head(10).to_dict()\n", "        },\n", "        'adjectives': {\n", "            'total_extracted': len(adjectives_df),\n", "            'unique_adjectives': adjectives_df['adjective'].nunique(),\n", "            'job_related': len(adjectives_df[adjectives_df['adj_type'] == 'job_related']),\n", "            'general': len(adjectives_df[adjectives_df['adj_type'] == 'general']),\n", "            'top_job_related': adjectives_df[adjectives_df['adj_type'] == 'job_related']['adjective'].value_counts().head(10).to_dict()\n", "        },\n", "        'adverbs': {\n", "            'total_extracted': len(adverbs_df),\n", "            'unique_adverbs': adverbs_df['adverb'].nunique(),\n", "            'job_related': len(adverbs_df[adverbs_df['adv_type'] == 'job_related']),\n", "            'general': len(adverbs_df[adverbs_df['adv_type'] == 'general']),\n", "            'top_job_related': adverbs_df[adverbs_df['adv_type'] == 'job_related']['adverb'].value_counts().head(10).to_dict()\n", "        }\n", "    }\n", "}\n", "\n", "# Save summary report\n", "import json\n", "summary_file = '../data/four_categories_summary.json'\n", "with open(summary_file, 'w', encoding='utf-8') as f:\n", "    json.dump(summary_report, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "print(f\"✅ Summary report saved to: {summary_file}\")\n", "\n", "print(\"\\n🎉 FOUR CATEGORIES EXTRACTION COMPLETED!\")\n", "print(\"=\"*60)\n", "print(f\"📊 Final Output:\")\n", "print(f\"   • Primary Skills: {len(primary_skills_df):,} records\")\n", "print(f\"   • Secondary Skills: {len(secondary_skills_df):,} records\")\n", "print(f\"   • Adjectives: {len(adjectives_df):,} records\")\n", "print(f\"   • Adverbs: {len(adverbs_df):,} records\")\n", "print(f\"   • Total Items: {len(primary_skills_df) + len(secondary_skills_df) + len(adjectives_df) + len(adverbs_df):,}\")\n", "print(f\"\\n🚀 Ready for linguistic analysis and research!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}