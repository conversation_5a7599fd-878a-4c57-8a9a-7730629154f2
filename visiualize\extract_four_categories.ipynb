# Import required libraries
import pandas as pd
import numpy as np
import re
from datetime import datetime
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# NLP libraries
import nltk
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.tag import pos_tag
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer

# Download required NLTK data
nltk_downloads = ['punkt', 'averaged_perceptron_tagger', 'stopwords', 'wordnet']
for item in nltk_downloads:
    try:
        nltk.data.find(f'tokenizers/{item}' if item == 'punkt' else f'taggers/{item}' if 'tagger' in item else f'corpora/{item}')
    except LookupError:
        nltk.download(item)

print("✅ Libraries imported successfully")
print(f"📅 Processing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Load clean datasets
print("📊 Loading clean datasets...")

try:
    # Load clean resume data
    clean_resumes = pd.read_csv('../data/clean_resumes.csv')
    print(f"✅ Loaded {len(clean_resumes):,} clean resume records")
    
    # Load clean job data
    clean_jobs = pd.read_csv('../data/clean_jobs.csv')
    print(f"✅ Loaded {len(clean_jobs):,} clean job records")
    
except FileNotFoundError as e:
    print(f"❌ Error loading data: {e}")
    print("Please run data_preprocessing.ipynb first to generate clean data")

# Display basic info
print(f"\n📊 Total text records to process: {len(clean_resumes) + len(clean_jobs):,}")
print(f"📋 Resume categories: {clean_resumes['category_clean'].nunique()}")
print(f"🏢 Job companies: {clean_jobs['company_clean'].nunique()}")

# Initialize NLP tools
lemmatizer = WordNetLemmatizer()
stop_words = set(stopwords.words('english'))

# Define Primary Skills (Core Technical Skills)
PRIMARY_SKILLS = {
    # Programming Languages
    'programming_languages': [
        'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift',
        'kotlin', 'typescript', 'scala', 'r', 'matlab', 'perl', 'shell', 'bash', 'powershell'
    ],
    
    # Frameworks & Libraries
    'frameworks': [
        'react', 'angular', 'vue', 'nodejs', 'express', 'django', 'flask', 'spring', 'laravel',
        'symfony', 'rails', 'asp.net', 'jquery', 'bootstrap', 'tensorflow', 'pytorch', 'keras'
    ],
    
    # Databases
    'databases': [
        'mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sql server', 'sqlite',
        'cassandra', 'elasticsearch', 'neo4j', 'dynamodb'
    ],
    
    # Cloud & DevOps
    'cloud_devops': [
        'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'gitlab', 'github',
        'terraform', 'ansible', 'chef', 'puppet', 'vagrant'
    ]
}

# Define Secondary Skills (Supporting & Soft Skills)
SECONDARY_SKILLS = {
    # Soft Skills
    'soft_skills': [
        'leadership', 'communication', 'teamwork', 'problem solving', 'analytical thinking',
        'creativity', 'adaptability', 'time management', 'project management', 'collaboration'
    ],
    
    # Methodologies
    'methodologies': [
        'agile', 'scrum', 'kanban', 'waterfall', 'devops', 'ci/cd', 'tdd', 'bdd',
        'microservices', 'rest api', 'graphql', 'soap'
    ],
    
    # Tools & Platforms
    'tools': [
        'git', 'jira', 'confluence', 'slack', 'trello', 'notion', 'figma', 'sketch',
        'photoshop', 'illustrator', 'tableau', 'power bi', 'excel'
    ],
    
    # Operating Systems
    'operating_systems': [
        'linux', 'windows', 'macos', 'ubuntu', 'centos', 'debian', 'redhat'
    ]
}

# Define Job-related Adjectives
JOB_ADJECTIVES = [
    # Experience Level
    'senior', 'junior', 'experienced', 'skilled', 'expert', 'advanced', 'beginner',
    'professional', 'certified', 'qualified',
    
    # Quality Descriptors
    'excellent', 'outstanding', 'exceptional', 'strong', 'solid', 'proven', 'reliable',
    'efficient', 'effective', 'innovative', 'creative', 'analytical', 'detail-oriented',
    
    # Technical Descriptors
    'technical', 'hands-on', 'practical', 'theoretical', 'comprehensive', 'extensive',
    'in-depth', 'broad', 'specialized', 'versatile', 'flexible', 'adaptable',
    
    # Work Style
    'collaborative', 'independent', 'self-motivated', 'proactive', 'results-driven',
    'goal-oriented', 'customer-focused', 'team-oriented'
]

# Define Job-related Adverbs
JOB_ADVERBS = [
    # Performance
    'efficiently', 'effectively', 'successfully', 'consistently', 'reliably',
    'accurately', 'precisely', 'thoroughly', 'carefully', 'systematically',
    
    # Speed & Frequency
    'quickly', 'rapidly', 'immediately', 'frequently', 'regularly', 'continuously',
    'constantly', 'daily', 'weekly', 'monthly',
    
    # Quality
    'excellently', 'professionally', 'skillfully', 'expertly', 'competently',
    'confidently', 'independently', 'collaboratively',
    
    # Manner
    'creatively', 'innovatively', 'analytically', 'strategically', 'tactically',
    'methodically', 'logically', 'practically'
]

print("✅ Category definitions loaded")
print(f"📊 Primary skills categories: {len(PRIMARY_SKILLS)}")
print(f"📊 Secondary skills categories: {len(SECONDARY_SKILLS)}")
print(f"📊 Job adjectives: {len(JOB_ADJECTIVES)}")
print(f"📊 Job adverbs: {len(JOB_ADVERBS)}")

def extract_primary_skills(text):
    """
    Extract primary technical skills from text
    """
    if pd.isna(text) or text == '':
        return []
    
    text_lower = str(text).lower()
    found_skills = []
    
    # Check all primary skill categories
    for category, skills in PRIMARY_SKILLS.items():
        for skill in skills:
            if skill in text_lower:
                found_skills.append({
                    'skill': skill,
                    'category': category,
                    'type': 'primary'
                })
    
    return found_skills

def extract_secondary_skills(text):
    """
    Extract secondary skills from text
    """
    if pd.isna(text) or text == '':
        return []
    
    text_lower = str(text).lower()
    found_skills = []
    
    # Check all secondary skill categories
    for category, skills in SECONDARY_SKILLS.items():
        for skill in skills:
            if skill in text_lower:
                found_skills.append({
                    'skill': skill,
                    'category': category,
                    'type': 'secondary'
                })
    
    return found_skills

def extract_adjectives_adverbs(text):
    """
    Extract adjectives and adverbs using POS tagging
    """
    if pd.isna(text) or text == '':
        return {'adjectives': [], 'adverbs': []}
    
    try:
        # Tokenize and POS tag
        tokens = word_tokenize(str(text).lower())
        pos_tags = pos_tag(tokens)
        
        adjectives = []
        adverbs = []
        
        for word, pos in pos_tags:
            # Skip stop words and short words
            if word in stop_words or len(word) < 3:
                continue
            
            # Extract adjectives (JJ, JJR, JJS)
            if pos.startswith('JJ'):
                # Check if it's a job-related adjective
                if word in JOB_ADJECTIVES:
                    adjectives.append({
                        'word': word,
                        'pos': pos,
                        'type': 'job_related'
                    })
                else:
                    adjectives.append({
                        'word': word,
                        'pos': pos,
                        'type': 'general'
                    })
            
            # Extract adverbs (RB, RBR, RBS)
            elif pos.startswith('RB'):
                # Check if it's a job-related adverb
                if word in JOB_ADVERBS:
                    adverbs.append({
                        'word': word,
                        'pos': pos,
                        'type': 'job_related'
                    })
                else:
                    adverbs.append({
                        'word': word,
                        'pos': pos,
                        'type': 'general'
                    })
        
        return {'adjectives': adjectives, 'adverbs': adverbs}
    
    except Exception as e:
        return {'adjectives': [], 'adverbs': []}

def process_text_record(text, record_id, source_type):
    """
    Process a single text record and extract all categories
    """
    # Extract primary skills
    primary_skills = extract_primary_skills(text)
    
    # Extract secondary skills
    secondary_skills = extract_secondary_skills(text)
    
    # Extract adjectives and adverbs
    adj_adv = extract_adjectives_adverbs(text)
    
    return {
        'record_id': record_id,
        'source_type': source_type,  # 'resume' or 'job'
        'primary_skills': primary_skills,
        'secondary_skills': secondary_skills,
        'adjectives': adj_adv['adjectives'],
        'adverbs': adj_adv['adverbs']
    }

print("✅ Extraction functions defined")

# Process all resume and job records
print("🔄 Processing all text records...")

all_results = []
total_records = len(clean_resumes) + len(clean_jobs)
processed_count = 0

# Process resume records
print(f"📄 Processing {len(clean_resumes):,} resume records...")
for idx, row in clean_resumes.iterrows():
    result = process_text_record(
        text=row['clean_text'],
        record_id=f"resume_{idx}",
        source_type='resume'
    )
    
    # Add additional resume info
    result['category'] = row['category_clean']
    result['experience_years'] = row['experience_years']
    
    all_results.append(result)
    processed_count += 1
    
    # Progress update
    if processed_count % 1000 == 0:
        print(f"   Processed {processed_count:,}/{total_records:,} records ({processed_count/total_records*100:.1f}%)")

# Process job records
print(f"\n🏢 Processing {len(clean_jobs):,} job records...")
for idx, row in clean_jobs.iterrows():
    result = process_text_record(
        text=row['clean_text'],
        record_id=f"job_{row['id'] if 'id' in row else idx}",
        source_type='job'
    )
    
    # Add additional job info
    result['job_title'] = row['title_clean']
    result['company'] = row['company_clean']
    result['location'] = row['location_clean']
    result['required_experience'] = row['required_experience']
    
    all_results.append(result)
    processed_count += 1
    
    # Progress update
    if processed_count % 1000 == 0:
        print(f"   Processed {processed_count:,}/{total_records:,} records ({processed_count/total_records*100:.1f}%)")

print(f"\n✅ Processing completed: {len(all_results):,} records processed")

# Create separate datasets for each category
print("📊 Creating category-specific datasets...")

# 1. Primary Skills Dataset
primary_skills_data = []
for result in all_results:
    for skill in result['primary_skills']:
        primary_skills_data.append({
            'record_id': result['record_id'],
            'source_type': result['source_type'],
            'skill': skill['skill'],
            'skill_category': skill['category'],
            'category': result.get('category', ''),
            'job_title': result.get('job_title', ''),
            'company': result.get('company', ''),
            'location': result.get('location', ''),
            'experience_years': result.get('experience_years', 0),
            'required_experience': result.get('required_experience', 0)
        })

primary_skills_df = pd.DataFrame(primary_skills_data)
print(f"✅ Primary Skills: {len(primary_skills_df):,} records")

# 2. Secondary Skills Dataset
secondary_skills_data = []
for result in all_results:
    for skill in result['secondary_skills']:
        secondary_skills_data.append({
            'record_id': result['record_id'],
            'source_type': result['source_type'],
            'skill': skill['skill'],
            'skill_category': skill['category'],
            'category': result.get('category', ''),
            'job_title': result.get('job_title', ''),
            'company': result.get('company', ''),
            'location': result.get('location', ''),
            'experience_years': result.get('experience_years', 0),
            'required_experience': result.get('required_experience', 0)
        })

secondary_skills_df = pd.DataFrame(secondary_skills_data)
print(f"✅ Secondary Skills: {len(secondary_skills_df):,} records")

# 3. Adjectives Dataset
adjectives_data = []
for result in all_results:
    for adj in result['adjectives']:
        adjectives_data.append({
            'record_id': result['record_id'],
            'source_type': result['source_type'],
            'adjective': adj['word'],
            'pos_tag': adj['pos'],
            'adj_type': adj['type'],
            'category': result.get('category', ''),
            'job_title': result.get('job_title', ''),
            'company': result.get('company', ''),
            'location': result.get('location', ''),
            'experience_years': result.get('experience_years', 0),
            'required_experience': result.get('required_experience', 0)
        })

adjectives_df = pd.DataFrame(adjectives_data)
print(f"✅ Adjectives: {len(adjectives_df):,} records")

# 4. Adverbs Dataset
adverbs_data = []
for result in all_results:
    for adv in result['adverbs']:
        adverbs_data.append({
            'record_id': result['record_id'],
            'source_type': result['source_type'],
            'adverb': adv['word'],
            'pos_tag': adv['pos'],
            'adv_type': adv['type'],
            'category': result.get('category', ''),
            'job_title': result.get('job_title', ''),
            'company': result.get('company', ''),
            'location': result.get('location', ''),
            'experience_years': result.get('experience_years', 0),
            'required_experience': result.get('required_experience', 0)
        })

adverbs_df = pd.DataFrame(adverbs_data)
print(f"✅ Adverbs: {len(adverbs_df):,} records")

print(f"\n📊 Total extracted items: {len(primary_skills_df) + len(secondary_skills_df) + len(adjectives_df) + len(adverbs_df):,}")

# Analyze extracted categories
print("📈 Analyzing extracted categories...")

# Primary Skills Analysis
print("\n🎯 PRIMARY SKILLS ANALYSIS:")
print(f"Total primary skills extracted: {len(primary_skills_df):,}")
print(f"Unique primary skills: {primary_skills_df['skill'].nunique()}")
print(f"From resumes: {len(primary_skills_df[primary_skills_df['source_type'] == 'resume']):,}")
print(f"From jobs: {len(primary_skills_df[primary_skills_df['source_type'] == 'job']):,}")

print("\n🔥 Top 10 Primary Skills:")
top_primary = primary_skills_df['skill'].value_counts().head(10)
for skill, count in top_primary.items():
    print(f"  • {skill}: {count:,}")

print("\n📊 Primary Skills by Category:")
primary_by_cat = primary_skills_df['skill_category'].value_counts()
for cat, count in primary_by_cat.items():
    print(f"  • {cat}: {count:,}")

# Secondary Skills Analysis
print("\n🎯 SECONDARY SKILLS ANALYSIS:")
print(f"Total secondary skills extracted: {len(secondary_skills_df):,}")
print(f"Unique secondary skills: {secondary_skills_df['skill'].nunique()}")
print(f"From resumes: {len(secondary_skills_df[secondary_skills_df['source_type'] == 'resume']):,}")
print(f"From jobs: {len(secondary_skills_df[secondary_skills_df['source_type'] == 'job']):,}")

print("\n🔥 Top 10 Secondary Skills:")
top_secondary = secondary_skills_df['skill'].value_counts().head(10)
for skill, count in top_secondary.items():
    print(f"  • {skill}: {count:,}")

# Adjectives Analysis
print("\n🎯 ADJECTIVES ANALYSIS:")
print(f"Total adjectives extracted: {len(adjectives_df):,}")
print(f"Unique adjectives: {adjectives_df['adjective'].nunique()}")
print(f"Job-related adjectives: {len(adjectives_df[adjectives_df['adj_type'] == 'job_related']):,}")
print(f"General adjectives: {len(adjectives_df[adjectives_df['adj_type'] == 'general']):,}")

print("\n🔥 Top 10 Job-Related Adjectives:")
job_adj = adjectives_df[adjectives_df['adj_type'] == 'job_related']['adjective'].value_counts().head(10)
for adj, count in job_adj.items():
    print(f"  • {adj}: {count:,}")

# Adverbs Analysis
print("\n🎯 ADVERBS ANALYSIS:")
print(f"Total adverbs extracted: {len(adverbs_df):,}")
print(f"Unique adverbs: {adverbs_df['adverb'].nunique()}")
print(f"Job-related adverbs: {len(adverbs_df[adverbs_df['adv_type'] == 'job_related']):,}")
print(f"General adverbs: {len(adverbs_df[adverbs_df['adv_type'] == 'general']):,}")

print("\n🔥 Top 10 Job-Related Adverbs:")
job_adv = adverbs_df[adverbs_df['adv_type'] == 'job_related']['adverb'].value_counts().head(10)
for adv, count in job_adv.items():
    print(f"  • {adv}: {count:,}")

print("\n✅ Category analysis completed")

# Export all category datasets
print("💾 Exporting category datasets...")

# Add processing metadata
processing_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

for df, name in [(primary_skills_df, 'primary_skills'),
                 (secondary_skills_df, 'secondary_skills'),
                 (adjectives_df, 'adjectives'),
                 (adverbs_df, 'adverbs')]:
    df['extracted_date'] = processing_date

# Export to CSV files
primary_skills_df.to_csv('../data/primary_skills.csv', index=False, encoding='utf-8')
print(f"✅ Exported {len(primary_skills_df):,} primary skills to: data/primary_skills.csv")

secondary_skills_df.to_csv('../data/secondary_skills.csv', index=False, encoding='utf-8')
print(f"✅ Exported {len(secondary_skills_df):,} secondary skills to: data/secondary_skills.csv")

adjectives_df.to_csv('../data/adjectives.csv', index=False, encoding='utf-8')
print(f"✅ Exported {len(adjectives_df):,} adjectives to: data/adjectives.csv")

adverbs_df.to_csv('../data/adverbs.csv', index=False, encoding='utf-8')
print(f"✅ Exported {len(adverbs_df):,} adverbs to: data/adverbs.csv")

# Create summary report
summary_report = {
    'extraction_info': {
        'extracted_date': processing_date,
        'source_files': {
            'clean_resumes': '../data/clean_resumes.csv',
            'clean_jobs': '../data/clean_jobs.csv'
        },
        'total_records_processed': len(all_results)
    },
    'category_statistics': {
        'primary_skills': {
            'total_extracted': len(primary_skills_df),
            'unique_skills': primary_skills_df['skill'].nunique(),
            'from_resumes': len(primary_skills_df[primary_skills_df['source_type'] == 'resume']),
            'from_jobs': len(primary_skills_df[primary_skills_df['source_type'] == 'job']),
            'top_skills': primary_skills_df['skill'].value_counts().head(10).to_dict()
        },
        'secondary_skills': {
            'total_extracted': len(secondary_skills_df),
            'unique_skills': secondary_skills_df['skill'].nunique(),
            'from_resumes': len(secondary_skills_df[secondary_skills_df['source_type'] == 'resume']),
            'from_jobs': len(secondary_skills_df[secondary_skills_df['source_type'] == 'job']),
            'top_skills': secondary_skills_df['skill'].value_counts().head(10).to_dict()
        },
        'adjectives': {
            'total_extracted': len(adjectives_df),
            'unique_adjectives': adjectives_df['adjective'].nunique(),
            'job_related': len(adjectives_df[adjectives_df['adj_type'] == 'job_related']),
            'general': len(adjectives_df[adjectives_df['adj_type'] == 'general']),
            'top_job_related': adjectives_df[adjectives_df['adj_type'] == 'job_related']['adjective'].value_counts().head(10).to_dict()
        },
        'adverbs': {
            'total_extracted': len(adverbs_df),
            'unique_adverbs': adverbs_df['adverb'].nunique(),
            'job_related': len(adverbs_df[adverbs_df['adv_type'] == 'job_related']),
            'general': len(adverbs_df[adverbs_df['adv_type'] == 'general']),
            'top_job_related': adverbs_df[adverbs_df['adv_type'] == 'job_related']['adverb'].value_counts().head(10).to_dict()
        }
    }
}

# Save summary report
import json
summary_file = '../data/four_categories_summary.json'
with open(summary_file, 'w', encoding='utf-8') as f:
    json.dump(summary_report, f, indent=2, ensure_ascii=False, default=str)

print(f"✅ Summary report saved to: {summary_file}")

print("\n🎉 FOUR CATEGORIES EXTRACTION COMPLETED!")
print("="*60)
print(f"📊 Final Output:")
print(f"   • Primary Skills: {len(primary_skills_df):,} records")
print(f"   • Secondary Skills: {len(secondary_skills_df):,} records")
print(f"   • Adjectives: {len(adjectives_df):,} records")
print(f"   • Adverbs: {len(adverbs_df):,} records")
print(f"   • Total Items: {len(primary_skills_df) + len(secondary_skills_df) + len(adjectives_df) + len(adverbs_df):,}")
print(f"\n🚀 Ready for linguistic analysis and research!")