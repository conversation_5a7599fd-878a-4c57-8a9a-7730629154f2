{"cells": [{"cell_type": "code", "execution_count": 4, "id": "e0514e1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Số lượng Job Descriptions: 841\n", "Số lượng Candidate Resumes: 690\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from ast import literal_eval\n", "import seaborn as sns\n", "\n", "# Đọc file CSV\n", "df_jd = pd.read_csv('../../data/clean/clean_jobs_v2.csv')\n", "df_cr = pd.read_csv('../../data/clean/clean_resumes_v2.csv')\n", "\n", "print(f\"Số lượng Job Descriptions: {len(df_jd)}\")\n", "print(f\"Số lượng Candidate Resumes: {len(df_cr)}\")"]}, {"cell_type": "code", "execution_count": 41, "id": "2d6252df", "metadata": {}, "outputs": [], "source": ["\n", "# Hàm chuyển đổi an toàn: xử lý cả chuỗi và danh sách\n", "def safe_literal_eval(val):\n", "    if isinstance(val, list):\n", "        return val\n", "    if pd.isna(val):\n", "        return []\n", "    try:\n", "        return literal_eval(val)\n", "    except (<PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "        return []\n", "def jaccard_similarity(list1, list2):\n", "    set1, set2 = set(list1), set(list2)\n", "    return len(set1 & set2) / len(set1 | set2) if set1 | set2 else 0\n", "\n", "def assign_suitability(score):\n", "    if score > 1.0:\n", "        return 'Most Suitable'          # Top 25%\n", "    elif score > 0.3:\n", "        return 'Moderately Suitable'    # <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    else:\n", "        return 'Not Suitable'\n", "\n"]}, {"cell_type": "code", "execution_count": 42, "id": "58fc5c9a", "metadata": {}, "outputs": [], "source": ["\n", "# <PERSON><PERSON><PERSON><PERSON> các cột từ chuỗi sang list\n", "for col in ['primary_skills', 'secondary_skills', 'adjectives', 'adverbs']:\n", "    df_jd[col] = df_jd[col].apply(safe_literal_eval)\n", "    df_cr[col] = df_cr[col].apply(safe_literal_eval)\n", "\n", "# Tính similarity\n", "similarity_scores = []\n", "for _, jd in df_jd.iterrows():\n", "    for _, cr in df_cr.iterrows():\n", "        primary_sim = jaccard_similarity(jd['primary_skills'], cr['primary_skills'])\n", "        secondary_sim = jaccard_similarity(jd['secondary_skills'], cr['secondary_skills'])\n", "        adj_sim = jac<PERSON>_similarity(jd['adjectives'], cr['adjectives'])\n", "        \n", "        adj_weight = len(cr['adjectives']) if cr['adjectives'] else 1  # tránh nhân 0\n", "\n", "        total_sim = primary_sim + secondary_sim + (adj_sim * adj_weight)\n", "\n", "        scores = {\n", "            'jd_id': jd['id'],\n", "            'cr_id': cr['id'],\n", "            'cr_category': cr['Category'],\n", "            'jd_name': jd['title'],\n", "            'primary_skills_sim': primary_sim,\n", "            'secondary_skills_sim': secondary_sim,\n", "            'adjectives_sim': adj_sim,\n", "            'adj_weight': adj_weight,\n", "            'total_similarity': total_sim,\n", "            'suitability': assign_suitability(total_sim)\n", "        }\n", "        similarity_scores.append(scores)\n", "\n", "df_similarity = pd.DataFrame(similarity_scores)\n", "\n", "# <PERSON><PERSON><PERSON> n<PERSON>u cần: lo<PERSON><PERSON> các dòng không có primary_sim\n", "df_similarity = df_similarity[df_similarity['primary_skills_sim'] > 0]\n", "\n", "# <PERSON><PERSON><PERSON> kết quả\n", "\n"]}, {"cell_type": "code", "execution_count": 43, "id": "18296eff", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>primary_skills_sim</th>\n", "      <th>secondary_skills_sim</th>\n", "      <th>adjectives_sim</th>\n", "      <th>adj_weight</th>\n", "      <th>total_similarity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>149357.000000</td>\n", "      <td>149357.000000</td>\n", "      <td>149357.000000</td>\n", "      <td>149357.000000</td>\n", "      <td>149357.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.166045</td>\n", "      <td>0.024407</td>\n", "      <td>0.009618</td>\n", "      <td>19.869112</td>\n", "      <td>0.447700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.098625</td>\n", "      <td>0.078340</td>\n", "      <td>0.020246</td>\n", "      <td>15.835445</td>\n", "      <td>0.623548</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.031250</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.040000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.100000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>8.000000</td>\n", "      <td>0.111111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.142857</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>16.000000</td>\n", "      <td>0.200000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.200000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>25.000000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.153846</td>\n", "      <td>84.000000</td>\n", "      <td>7.169884</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       primary_skills_sim  secondary_skills_sim  adjectives_sim  \\\n", "count       149357.000000         149357.000000   149357.000000   \n", "mean             0.166045              0.024407        0.009618   \n", "std              0.098625              0.078340        0.020246   \n", "min              0.031250              0.000000        0.000000   \n", "25%              0.100000              0.000000        0.000000   \n", "50%              0.142857              0.000000        0.000000   \n", "75%              0.200000              0.000000        0.000000   \n", "max              1.000000              1.000000        0.153846   \n", "\n", "          adj_weight  total_similarity  \n", "count  149357.000000     149357.000000  \n", "mean       19.869112          0.447700  \n", "std        15.835445          0.623548  \n", "min         1.000000          0.040000  \n", "25%         8.000000          0.111111  \n", "50%        16.000000          0.200000  \n", "75%        25.000000          0.500000  \n", "max        84.000000          7.169884  "]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["df_similarity.describe()"]}, {"cell_type": "code", "execution_count": 44, "id": "8380a3d2", "metadata": {}, "outputs": [], "source": ["df_similarity.dropna(inplace=True)"]}, {"cell_type": "code", "execution_count": 45, "id": "80da5938", "metadata": {}, "outputs": [], "source": ["df_similarity = df_similarity.drop_duplicates(subset=[\n", "    'primary_skills_sim',\n", "    'secondary_skills_sim',\n", "    'adjectives_sim',\n", "    'adj_weight',\n", "    'total_similarity'\n", "])\n", "\n", "# Reset index nếu muốn\n", "df_similarity.reset_index(drop=True, inplace=True)"]}, {"cell_type": "code", "execution_count": 46, "id": "aa04fbb2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1480 entries, 0 to 1479\n", "Data columns (total 10 columns):\n", " #   Column                Non-Null Count  Dtype  \n", "---  ------                --------------  -----  \n", " 0   jd_id                 1480 non-null   object \n", " 1   cr_id                 1480 non-null   object \n", " 2   cr_category           1480 non-null   object \n", " 3   jd_name               1480 non-null   object \n", " 4   primary_skills_sim    1480 non-null   float64\n", " 5   secondary_skills_sim  1480 non-null   float64\n", " 6   adjectives_sim        1480 non-null   float64\n", " 7   adj_weight            1480 non-null   int64  \n", " 8   total_similarity      1480 non-null   float64\n", " 9   suitability           1480 non-null   object \n", "dtypes: float64(4), int64(1), object(5)\n", "memory usage: 115.8+ KB\n"]}], "source": ["df_similarity.info()"]}, {"cell_type": "code", "execution_count": null, "id": "48e796e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 47, "id": "7fa8a183", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<PERSON><PERSON> bố nhãn Suitability:\n", "suitability\n", "Moderately Suitable    565\n", "Not Suitable           533\n", "Most Suitable          382\n", "Name: count, dtype: int64\n", "\n", "Tổng số cặp JD-CR: 1480\n", "\n", "Thống kê Primary Skills Similarity:\n", "Mean: 0.1615\n", "Median: 0.1250\n", "Min: 0.0312\n", "Max: 1.0000\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA0kAAAKTCAYAAADMq0O9AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAs0FJREFUeJzs3Qd8m+W1P/CfZHnvvR1n70kIJIEMEpJA2HsV6C4tZcPt7aD73/be3tK9B+VyS8mEhJA9CZlkb2c5zrLjvbX1/5zXUZAd2ZYdSY/G7+uPY0fLR+vVe97nPOfRORwOB4iIiIiIiEijb/tBREREREREgkkSERERERGRCyZJRERERERELpgkERERERERuWCSRERERERE5IJJEhERERERkQsmSURERERERC6YJBEREREREblgkkREREREROSCSRL5zfe+9z3odDps2LABoW7atGnafVXhzTff1P62/HQlp0lcqjz99NNaDKWlpQgUn3zyCW699VZkZmZqsY0ZMwahxh+Pe09f77INkMvLNqG72+nssv504cIFxMfH4//9v/+HcBBO2+pA2kaG4jYl2N7rgfL5f/PNN+OGG27wWkzUO0yS6Jrs2rULn//85zFw4EBtJyI2Nhb9+/fHZz7zGaxevRqh8EHg/DYYDEhNTcWwYcPw+OOPY8GCBTCbzV7/u8H6QdFZchaoGhoaMHfuXOzYsQMPP/wwvvvd7+IrX/mK3+Oorq7GN77xDQwfPhxxcXHad58+fTBjxgx8//vfR0VFhdf/ZrC+xlwVFxdr3/7wrW99S3tennvuuaticN1GyHd0dDT69u2LL33pSwF1QCAUObc5rt/yGTRkyBC89NJLqKqqUh1iUHA4HHj77bdxyy23ID09HVFRUcjOzsbYsWPx1a9+FRs3bkQosNls+Mc//nHlwJjcz5ycHNx5551YuHAhAolsm+Wz6d///rfqUMKaQXUAFJzsdjteeeUVvPHGG1ryIBvXu+66C5GRkTh16hSWLVumbXR/8IMf4Dvf+Q6CmSSBBQUF2geJ7FgfP34cS5cuxb/+9S8MHTpU24iNGjWq3XXeeusttLS0KIn33nvvxY033ojc3FwEkp/85CdaMpCfn49AIB9Aly5dwo9//GN885vfVBLDuXPnMGnSJJw9e1YbxfrsZz+LlJQUXLx4EVu2bNE+KCdPnqztsITK4y4mTJiAI0eOICMjw6uX9QV5v8v7WRKlhISEq86PiIjAt7/97Sv/r6urw/bt2/GXv/wFixYtwu7du1FUVOTnqMOLHFC46aabtN8rKyuxcuVK7bNJHn85kCc7/p6Q15kkw+Hmc5/7nJZwykHAO+64Q9tWtLa2Yt++ffjb3/6mfe5NnTq1V7et+v3rJNv6u+++G9u2bdM+G+X3rKwsbRss+ysffPCBliy988472gHfQHhNjxs3Tjt4JwfxVFWmhDsmSdQrslMgH0KyYycjKjJ65Eo2sL/97W+1o+TB7gtf+IKWdLhqbGzUNl7yGMyaNUvbEcrLy7tyvsqdouTkZO070MgHUyAlblJCJVyfN3+T15AkSJ0dTDhw4ICWNIXS4y5kR1SO9nv7sr7w5z//WTsoJKPj7shBIncjcl/72tfw+9//Hn/961+155d8Z+bMmdqBACeLxYLZs2dj/fr1+M1vfuPxiKnK15kqH330kZYgyWe5jBglJSW1O1+S/sOHD/f69lW/f52vh3vuuUdLkOSgp7wmZMTR9T4+8cQT2sFPOVA1b948BAKJSUZE161bpyVN5H8st6MeO3HiBP7rv/5LOzq3YsWKqxIkIRugV199VSsX6s7f//537aiOlK7ExMQgLS3tygecOzIsLke15CiQXF52cuVDsuNwuVz/tttu086XEhg5Gi91vrLTc60SExPxi1/8QivJk3KoH/3oR93WJMuOluwwyZE1uY/yGMkIlRy9ctb+y4f59OnTtd/lsXMtI3GW7jjLAGXE7n/+53+08j+5f3K6J2VvcuTs0Ucf1Y7syQeYjFSsWbOmR3XVHWvS5f/y4SLkp2vcnV3HlZRASP21HKmXb/ndXfyuZWLO+UTyXEhSKCNonpY3yW089dRTV8Xr+jfPnDmjfaDKUVUpy5DnSv5fVlbW6WNlNBq1AwjynpBR1e52zrZu3ar9/PrXv+72/JEjR6KwsPDK/+X+yd9xPteezKno+Lh78horKSnBa6+9ph3JlPe5vM8GDRqk7Yg2NTV1en/k/stl5CCBXEdGWmWHREZhe1vu1/GyzsdAnh/5do1fLiOvZfldyoTcOXnyJPR6vbaN6Y68Z//5z39qO5BSUtwTc+bM0X52LPnq6WMro4rPP/+89vdlmyFJszyuUhpaX1/f7rJS/ivbJbltORou7w3Z5i1ZssRtjJKgy7ZAtkfyvpPt6qZNm9AbH3/8sVa+Krcl90l2jOUggLsRdefrVLad8j6UbZHcNzkY5Y15UPLe+/KXv6z9vnPnzqteRzJKKwe35LF03UZ19f6R7e3Pf/5z7bmSWGW76yyFksddRhqdn2FSWbB8+fKr4pJRrWeffRYjRozQtllyO/Ie/+lPf6rtyHdWUio78XI92RZIUi7bKRk5k9/l9eHOk08+qcXt3MZ0xnm+PA8dEyQhj5GMdntCHmO5vLz/jx496rXSXhnNle20lLHKZ528xkaPHo0XXnjhqm2LO/Ielvsp7wUZ4XVNkITEPH/+fAwYMED7KUmJu22u7PvI54yMuMn7S/Y7ZLStO/K5L7ch+03uyN+T852vWacHH3xQ+xksJeyhiCNJ1GPyhpXaXnlDd1cGJBu07sgRV9ngyQZH6oTPnz+P9957T/u/lEtIAuX0hz/8Qdv5kSPjsrGSnYzy8nKtdGrx4sW4//77tcvJ8LkkH7Lxk+vL5aUMQzZo//u//6vNF/AGOfovj4ccefrd737X5ZD4f/7nf2obSdmBfuyxx7QdGLmvmzdv1nbs5MNZvmWjLBt12WFx/cDuOKIgO9ZyZEx2TOS+StLYndraWi0pksdZRsjkMXn33Xe1HToZEZSjbb0h15MP8vfff197vHvSAEHmeciOtCQjkoQISXjlQ3HPnj341a9+ddV1ZMdHHkvZ2ZfXoVxOXjMy8nLw4EFtR6UrsvO2d+/eq+J1/pQdWdkJkcdHHluZLyS3Kwm9HG2U50x2ljqS15+8xuTxlOdLPtS74iwDkr8nybM/ePIak/edlNnI4yvnS7Igr7Wf/exn2tFm2ZGWHdGOHnroIe25cL4P5XmU51f+niT03iAxyvP3y1/+Uvu/7Ci53je5T/Iek3JY2antWD4lOyyyY/XFL36x278lryd5DTjvT0+sWrVK+ykJi6uePLaSYMj7VR4/2amXbZ7skJ8+fVrbjknJs3PU2GQyaa872SmV17G8l2THW7aF8hqX95jsaDvJzvXEiRO1bZAkjBKnlEXJgQdnEu0p2bGUZEu291IaJNsiuf8ygialbxJTx/ekbC/kPSbxyyidlEPJtkhikWRCEglv6LhNlgRJGnDIfZTPAXcHPdyRI/pSRinbAymxlARJtuOywyyPrYy2yLZYDhTIa08ec3k8XQ8iyg66bD+mTJmC22+/XXt+5bGRzwbZprmbFyPPq5SzSwItJe2SGMnnrmz3JDGVA0wdy4XlsZXtuWy35Dn2dBt0LSR2masr91eeczmo5K0Rf9k2Njc3a4+vvL7kd0mcZKRW3uPymHRFHiMhiWxnn9GSOL388st45plntO28POau5D0oSbw8plKeKAdb5PNDXkfyPHe1LyTvDblted/LAZKO5HUhOm6T5DGUxHjt2rVd3j/yIQdRD02bNk0O3TjWrFnTo+t997vf1a63fv36dqefOnXqqsteuHDBkZeX5xg4cGC708eNG+eIiopyVFRUXHWdqqqqK7/fd9992t/au3dvl5frylNPPaXdxtatW7u8XGFhoXa5kydPXjlt6tSp2mmu0tLStPvU3Nx81W1UV1df+V0eH7muPF5dxVVQUOA4c+bMVef/4x//0M6Xn67kNPl+7LHHHHa7/crp+/bt0x7TzMxMR0tLS5f3oWMMp0+f7vbvdnWdjRs3aqcNHTrUUVdXd+X0mpoax6BBg7TzNm3adNVjI9///ve/293+Zz7zGe30d955x+3f9/RxEtOnT9fO+9Of/tTu9N/97nfa6bfccku7052P1ZgxY9o9l9359a9/rV0vKyvL8frrr2v3r76+vtPLy2Mnl5fH0h05T2Lp7nHv7jV27tw5h8lkuur073//+9r13n77bbf3f/Dgwe2eR/ldTtPpdI6dO3d2+/fdveY6u2yfPn20b3d+9rOfadd58803251usVgcubm52uNtNpsd3XE+33/5y1/cni9/PyIiQovN+f3iiy86Jk+e7NDr9Y6HH374qsexJ4/tkiVLtNNeeOGFqy7f2NjoMBqNV/7/zW9+U7vsd77znXbv74aGBsf48eO19/j58+evel386Ec/ane78pp3vsc6bqvdkddrcnKyIzo6WtuWONlsNu3+y+384Ac/aHcd5+1/9atf1S7n9Ne//lU7/ctf/rKjJ+/hn/zkJ1c9z/IelfPkce247fj73//e4/ePbI8uXbp05fTt27drp6ekpDhuuukmR1NT05Xz3n33Xe28r3/96+1uS7bXVqu13WnyXH3uc5/TLr958+arXl9y+uzZs9ttm0Vra6v2mdKvX792z7f47W9/q13vl7/8paM7Z8+edSQlJWnvUflsmD9/vqO0tLTL63TcpvzhD3/QXu+TJk3Stt2urvW97txGursvnmxr5bUQGRnpMBgM2mPWlZKSEu1vyWPacZsr3z/96U/bXf7b3/6229efu/v2zDPPaKdt2LDhqvsg7x357HDn3nvv1a7nbj+JfI/ldtRjMnIjvHWkyN3Rdhn5kaO3crRISmpcyVFWd0ex3U3O7Tis3tnlroVzTosnnZSkbEuOQnYk5QM9JeWMPZ37JH9bjqK6Hk2T0hA5kitHzD/88EP4k4xmCCnFcJ1HJUdnZbSgs1IDORIrRxRdydE91/Ka3pIjy1KqKeU0HY/sSYmTlBFJeYSUKnUk5Ws9eS7lyL48j3LkV466y1FJGSmRo5VSftVZKY2vOUsM3cUr3JVnOkdWXZ9H+V3KD2X/0/lc+4OMQkr8MmrkSkZV5DGV0iJ32xB3pamiq6PEMqouz7vzW+YpyhF+eQ7lNdrxcezNY+tuOyblcc6RehmNklF2OYrvLKF0khHr119/XRuBklEsIb/LqI2M+MgRblcywtyT0kI5mi5lf/L+c21gIyWNMtrrLA/rSMqVZPRMLuckz4tcvqfvYXnMZBsi3zLCLu9deY/KZ4vr6JmQETNnaXBPyCiEjMA7yehGv379tPeuNH9xnewvn13y+upYiiXb647bf3mupJrCeT/ckcex42tARubk8ZIyQNfyMCEjFvLa6GwenSv5HJdRIBmxkBEwKfGSEj95bcjrt+NtdySvNxl9kZExiV+23b7g7j3gybZW5kXLiKqUdHZXYeAsbXa33ZXXkmyrXTkrHzx5vTo7p3bcJsmIsIwWdjay7dz2OLdF5F9Mkkg52cjLBkI+4GUj5pxfICUMrhPsxSOPPKINtUsphmywZKdeOu90JJcTMjwuH5JSiqe6HazEJEP2ErvsTMqHjzS46K3elGfJh7S0l+5IarWFlEr5k/PvuVubxFnyI2VxHV133XVXneZM2mWn5Vo4/56UbXUszZAdOknQOourp8+Js05dSp4kiZCdjfHjx2v1/LIDKTt7UuLjb5LUSMmJ3FfZEZEdO4nVeYDB9T3p7nWk+rUlO7P33XefVhbpnBvhuoMiiYAnnI1numqeITuj8ng5v6Wpi5TPyU6ZxODcjvXmsZXLyAEjmbMipUaSCElZV8d5GMeOHdNKaWX7KTutzoTB+S1zR4XzsZDLS1mYvNY67jjKa1xK/LzxHpbtjSQSso2Xx8WVlKt27BboLCXr6XtYypGcSaqULsl9kPI4KcPuuCN9/fXXozfclRA7G6J0PE+eU0kyOr5PnHPGZDsh838kTnnundszd+8reX5k3pI7zrJxZ7mWkFJFZ8mrpwdspLRdysdk2Q75bJKER3bcpYxcGgZ01v1TSl3l9SXzdeQz1l0ic62kvFESUEkkJWmT0jl5PfmbPMeuCX1PP3PkAILsj0gZpOvlJaGVkmApVXTH+Ryq3n8JV5yTRD0m6wrIh63s2A0ePPiabksmQsoHhiQ6slMsG0Tnh4fUakuNvmysnaQGX3YmZGdB5jg465FlB0KO4DpHpeRomMxRkQ+kP/7xj1fmC8nfkOt5c9FQ5web61FGd2RujcQnG3lp9CDf8gEo8zgkpp62SO1NW+jOruM8veNEcF+T512ea3ePncQkz5m7JNjdBGNnXboc2b/WmJx/3x3njpG7uHrbqluee5loLd/O0VpJ7uUIr+wIeTI52JtkHpF0p5QjqzIPQu6zc9RCdkRd35Pd3X9Vry2ZsyHzRiQxku2EvE9lMr0kv+7mk7nj3OmThMJTzsYjMmojO1EykiZHnJ1zo3ry2MpInCRcMhIkc1mcI71yXRlpdDanqKmp0X4eOnRI++6MHGByfS46m8fYk9exJ+8Xme8il5NRra7ew873cU/fw842957o7Xu0q21OZ+d1bMbwwAMPaM+jvP6cc7dkxEl2muXzwd37Si7T2TwaGdWW17N81klCL5+NzgMBnsy56xivJEvyLaxWqzYCKAdu5PGV2DvOr3M2+ZDP7e7mBfWWjGrJe0CSMXn9OzvPyX2X0Xdnc4POyGMij7EkGfI+7mo0yVkd4K4bqDc+c2SbJKOYsjyKbN/lAJjMe5QRwc460joPpIZja/pAwJEk6jHnUUZvTCaUxEaOgMrGWI5iyWRs2fDJBtFd21D5sJCyDhnelvIwOXolR2ul5EPWd3DdWMnEWUmy5PZl50iOHkviJZObr3W0wUmOaMmGVXbyu1vYUjaokuTJTowkmFLaIEfZZQ2Wzo4idaU36yZ0tjCp83TXDbXzqJl8WHbkrR1e+eCRUiF5LjuSidxyxLyznSlfcf69zh4rZ7mpu7i8tZaFHIiQMgzZed6/f/+VEQ1/PCfyuMtBBTnyKQdD5L0pO0nynuxusV13j5m715Y/yMiGbEPk/SVH8OXghGwferLz6EzenUlIT8jokxxEkuTAOSm+N4+tjMbI5eQ9IiMEMsIo7xk5si5ruri+FmX0wHVUq+O3cwK787mQeNzpyQLG1/J+UUHVejPymSUJkjSmkNFAGf2RMj157p2VD72JV143klw51+aT14SUS7ob2esJ+bySz0xpTiHcdZuVz1858CfxO0s5fUGqL2QERt6H0qVODhrI60oSTSlt7e5+yOihbDO7WxTXuU/TXbOL3pJ4ZbvgTGQ9SWid257uDsKSbzBJoh6ToXUpJ5BW2u52bl11dsTZSYb4hWsHOyEf6N1t/OQIkXRVk9p66UQjHzwyMtWRHL2UxEjidbbs9lYJ0w9/+EPtZ08Xe5N5TNLxRspgpO2o1HI7jxg5a9avdUSks/k2Hed4OdfKELLCupOztlwSOleyg+ZuZKM3cTv/nru2v87TvDnq5wnn35OjpB3LmuT/zqOnvo5LEqSO82acZV8dn5OelrN19VxJ4i/3U44odzx66XyddMbd+e5eW94g96G715qMwsk2So60S4mbvKZ70qnOWeYk5Wm9IQdonO+Za31sJUGW15x0x3ImR87W3tISXJIQaYvvrpV0RzKSIUfU5fIdR8kkVukA5433sBxAkm28lNy5jiKFI+dnnVQ9dJyX1N1z3xU5SCg70LLDLV0G5WCJp+WknnC3gLKTlG7L8y4jm/IZ6K47nzfJ9lBK1mTE9de//rX2XpJFYLvjXDJBDkh01jJc3gdSeeI6v9XbZGRaqgXk81OSTtl3kfduV+Wtsu2R+616ralwxSSJekx26uWDWoavZR0iaUfb2Qanu7URnPNjZO6AK6nBl5bLHckGueNGTnYKnEdbnEPpsiPrbgfKeeS0uwmc3ZF2rDLhWY7wytB8ZzXbrsmiux0PKX+R25KNoHOUwFmD7K4xwLWSx0RidX0MZaRCRi3kg1Zq0TvW7necdC3Pq7vnvDdxO9cqkg891/I1+aB3rrHlvIy/yJF7KcuUET/ZsXYliba0e5Wk3HX9ot6SMkvXOTOupCRLXhvy4eicryI7wjI6Ie8X1wMCMt9D2gh7qqvnyvmelNerc+feOXG4u78hBw1cR7TkdykrdV2XylvkPjhLaDojf1Pe6y+++KKWoMhE9p6892WkV96XvTmoIkfZ5X0iiZmznXVPH1t5DXY1Oue8L3K0XMqi5ACIjFa7S5Rke+rc/kkCLmW+8v+OrdllZ7sn7aDlAJeMTMkolWupn2xj/uM//kM7gt/Zul7hpLPPOnnMZOe9t6QJiDy+cpBQtu3yWdKTx1sO1EklhrvRadnGSOIlpF17Z9tL+VyW+ycjSjLi400yx8pdaXPH90BX5PGQElgZSZKRt47bDNlOSZInjaKkfK9j+29vcq6FJAvFyna7q1EkGQGXg18yd5DldmpwThL1iuz4yIZGyuVkp002KrIjIBto2TGQkREpEeq4yGpHssGSD1c5uisf2rIzKPXHu3fv1o64STcqVzJyJDuKcjRJNsqyMyBlevIBITXTzg8iqfuXOQiyYZcyONlJkw8nmcgr1+1sg++O7DTIB4lzUrZsSGVjK79LByuZ9+CuhtmVjBLJ0SI5giuTdOWDRXaA5SiYlA3Ijo1zXoLsFMtIk9yunCbzGiR+6dp0rSVLUuYjj4MkQHI027lOknxASgLgOvFWaqelqYAkutKkQBpryJFn2dmSOviOpQtSoiDXl5JJOYLuLA+QORmdkYnpcr9kcru8fpzlQnJEUnYc5Xl0NkrwJ5nzJq8R+QCTEhlpoCA7M3LkXu6XnO8NzrVuZMRCPsRl/oGUgjrfA/J4dvxbkpzLCIk83vKBLjvbUk7akwnpXb3GnJ0l5TmQD2eZuC07JPJald+dR8Tdkde383kUzudRJtHLbXmTbHPk9SgHaiSZkZ1Fea24vl4kkZLHSB7n3szTkARHXuvynulsPoO8d1wPBsmBD3mtyDbD2YDG2c2up4+tbNukQY1z2yHbR0n25HUosTi7ogk5qCCvGTnCLttNeRzk9SSjjjLvQY5eS6mScx6SHIiS8iJ5f8r9kxEhOQAg8z5kTSbnOk/dke2xlI7JyLi8hmVnU94j8hkgO7gy57RjV7BwJI+DfMucGumeJp9DMrIvz6V81l1LciE73s55d/L68mTNPCc5SCMHEWRepLxmZDsv22BJkOS1IDvqkoDLc9sZOWAkiZIcXJLXgVy/u7lCnpL37p/+9KcrscnrTT7vJTZ5f3vSqVD2SyQRlDmA8jkn7zc5IOh8f8j/ZX9FSvadJam+Ip8lsr2S0UPZ9jrnobojl5EDrL1dv5C8wA9tximEydonssbDgAEDHLGxsVq//+LiYm29hdWrV3u0TpL8X9YVSUxM1NacuP322x27du1ye/nf//73jrvuuktbPyImJsaRnp7umDBhgrZOg+u6J7KGzkMPPeTo37+/Iy4uTlvHY/To0dr6KbK+iCeca0E4v2U9FIlv2LBhjscff1xbT6KztVY6rpMgl5O/PWvWLG19I1mzJDs72zFlyhTHv/71r6vWudi2bZt2G/KYOP++c00Kd+veeLpOktymrIsh65fIGhvyGE6cONGxatUqt7cl60zNmDFDewxlLY27777bcfz48U5jWLZsmeP666/XXgvOuDs+nu7ilnVL5Hryd+Rbfne3lklX6/t0t4aQp4+Tk6wV8tnPflZbV0fW2JCf8n93a4h0taZUV3bv3q2t4yLXl/W25HUhj92QIUO0dTVk3Y7O1u+RNcRk/Y+ioiJtjSV5jXm6TlJ3rzF5j7z88svae1ne0/K3fvjDH3b6N5z3X9Yhee21167cF1kjSdY56fj69sY6SRLjF7/4Re15kfdmZ68LWc9NzrvxxhsdveFc80Z+duRcx8b12/lauf/++x0ff/zxVdfpyWN7+PBhx/PPP+8YO3astq2Ty8saLvKcHjp06KrbljV4ZJ0j2Z7K+1UuL6+POXPmaNtI17V8nOv2yLZAtmvyvrv55pu1tcs621Z3RdYzu+2227Tbkude1hWSNZs6/k3h7jXkyfpXnq6T5E53a4N1FldX262u3vfu7oessySfl7Jenmx7R44cqb2XZQ0cd9uunjwWslaT3MaKFSs8urxrTLIO2AMPPKC9X2V7INsVeQ3fcccdjgULFlx1nc4eE1kDTF7P8h5wvl+u9b0u2ylZN2vEiBHaa0u2j/I3nn32WbfrBHa3ZpKsxSVraMn7Se6nrJk2d+5c7fPcW2vTdfd54FwP7JFHHuky3qefflp7L7muz0X+pZN/vJFsERERBRo5wi4jGdJqtzdzDWS0WkbL5Si2jOwQBRoZ5ZTRYJk/JCONHVtVU2CRznbSwEVGcjsr7ZNqDKmMkQqZjmXf5D98JxERUcjuPMrcLimb66qDWHelOjJnRMrHetLQgMhfpERMysWk7I4JUmCTEndZE08OvDjXAnRH5v7KHGJncyhSg3OSiIgopMgcG5kzt3LlSq2ZgSQ51zLxWebZyPwRZyt2okAg88pkp1vm7Mj8Gue6WRR4ZJ6gzBmUuWcyH1nmMXbVEVfmW0lb9/z8fL/GSe2x3I6IiEKK7IBIIwOZjC4d7aQBia8WuyRSRXayZaRz9OjRWoMQaQZBgUk67MkIkjTMkXK7nnQjJXWYJBEREREREblg8SoREREREZELJklEREREREQumCQRERERERG5YJJERERERETkgkkSERERERGRCyZJRERERERELpgkERERERERuWCSRERERERE5IJJEhERERERkQsmSURERERERC6YJBEREREREblgkkREREREROSCSRIREREREZELJklEREREREQumCQRERERERG5YJJERERERETkgkkSERERERGRCyZJRERERERELpgkERERERERuWCSRERERERE5IJJEhERERERkQsmSURERERERC6YJBEREREREblgkkREREREROSCSRIREREREZELJklEREREREQumCQRERERERG5YJJERERERETkgkkSERERERGRCyZJRERERERELpgkERERERERuTC4/oeIiMhbrHagyQw0m9t+ar9bPv1ptABmO2C2tX1bbC6/u5wut+NwAA58+vPJ0cDkQtX3kIiIQhWTJCIi6hG7A6gzAjWtQG0rUGNs+6l9Gz9NiEw21ZESERH1DpMkIiK6SosFqGgCypvbfla2fJoU1ZvaEiUiIqJQxSSJiChMSaJzqbl9MlRx+WejWXV0RERE6jBJIiIKAzK351wDcLYeKGsAztUD5xvb5v4QERFRe0ySiIhCjDRKOFPflhCdlcSooW10iBVyREREnmGSREQU5CQBOll7+bsGKGdCREREdE2YJBERBRFpk11WD5y4nBCdquX8ISIiIm9jkkREFODNFc7UAUeqgCOVwKm6tnWDiIiIyHeYJBERBZjKZuDw5aToWHVbO25SwwEHdNCpDoOIiPyMSRIRkWKtFuBwZVtidLQKqGpRHRE5/QV/ufK7M1nSXf4ywIBIN19RiHJ7uvO8GMQgDnGIRSwTMCKiAMUkiYhIAVmYdV85sK8CKKkGbOy0EBSjSq4/bbDBBFOvb08SpLjLX/GIv+p3509JqoiIyL+YJBER+Ym05N5b0ZYcSVtuCm+SbDVf/qpEZaeXi0CEliwlIQnJSEaKy5ckUhyNIiLyPiZJREQ+bLogo0R7y4H9FUB1q+qIKBjJiFXj5a/zON/uPCn5k8SpY/Ik/5fyPiIi6h0mSUREXiZtuXecB3ZdBBp6X41F1C0rrKi+/NWRjDJJwpSBDGRe/kpEopI4iYiCDZMkIiIvuNDYlhjtvMDGCxQYnKV8rqNPMr8ps8OXlPIREVF7TJKIiHqpuqUtKdp5HjjXqDoa6g2zxQy73Q6dTge9Ti/dFLTf5Uuvb/t/KDHCiLOXv1xHnCRZkhGnLGRpP9ksgojCHZMkIqIeMNvayug2lwEna2TyPQWrqtoq/HPxP9HS2tKWHDlbfOs+/ZnxlQyEy4hTKUqvnCZzmvKRj7zLX0yaiCjcMEkiIvKwM91HZW0lda1W1dGQN9Q11qG8shxJCUlto0iOto5zDsen3+Gq/vLXYRzW/p+OdC1ZksQpBznaek9ERKGMSRIRUSeM1rakSJKjsnrV0ZCvpCSlwBDBj8OuOJtDHMABbcRNyvKco0zZyNa67BERhRJu1YiIOjhZ21ZOt+sCYLKpjoYosMhoW8Xlrz3Yo63jJImSjDL1QR+kIU11iERE14xJEhGRtFK2tzVgWHuaC70S9XQdpwuXv3Zip7bobfHlL0meuNgtEQUjJklEFNZkHaONpcCmMq5pROQNDWjA/stfsYjVRpckYZKRJhl1IiIKBkySiChsGzHIqJG08JZRJCLyvla04ujlr0hEohCFWsJUhCI2fyCigMYkiYjCht0B7K8A1p4CSmpUR0MUXiyw4NTlLz30WtMHSZj6oq824kREFEiYJBFRyJORom3ngJUngEstqqMhIjvsOHf5awu2aCNMgzBIG2FiSR4RBQImSUQU0gu/Spe6VSeBWqPqaIios4TpzOWvaESjP/prCZO0GSciUoVJEhGF5PpG0oxhzWk2YyAKJiaYtAVs5SsFKVqyNAADkIAE1aERUZhhkkREIaPZDKwrBdafBpotqqMhomtRhzrswA6trbjMX5KESeYvceFaIvIHbmmIKCSSIymp23CmbRSJiEJr8drzl782Y7OWKA3BEOQgR3VoRBTCmCQRUdAyWdtK6lafBFqZHBGFRYe8kstf6UjHcAzXyvE4ukRE3satChEFZbe6TWeA5Sc454goXFWjGpuwCduxHYMxGMMwDElIUh0WEYUIJklEFFTrHG0/Dyw9BlS3qo6GiAKl2cP+y1/SSnwkRqIABarDIqIgxySJiILC3nLg/WPAhUbVkRBRoDp7+SsVqVqyxFI8IuotbjmIKKCdqgXmH277SUTkiVrUaqV40h1vKIZqc5fiEKc6LCIKIkySiCgg1RmBRUeAHeeluxURUc8ZYcQe7ME+7NNaiI/BGM5bIiKPMEkiooBisQGrTgErTwAmm+poiCgU2GHHURzFMRzDQAzEWIxFMpJVh0VEAYxJEhEFjF0XgIVH2JSBiHy35pK0Dz+O4+iP/hiHcUhBiuqwiCgAMUkiIuXONQDzDgHHqlVHQkThkiydwAmcxEltcVpJltKQpjosIgogTJKISJkWC/DeUeCjsrb23kRE/k6WTl3+KkaxlixlIEN1WEQUAJgkEZESO88D8w5zMVgiCgyll7+KUKQlS1nIUh0SESnEJImI/KqqBfjXAeBQpepIiIiuVnb5S5KlG3CDtuYSEYUfJklE5Bc2O7D2NLC0BDCzax0RBThJlGRh2sEYjPEYz3WWiMIMkyQi8rnSOuDt/cDZBtWREBH1bM6StA6XBg+jMRqjMAoG7joRhQW+04nIZ4xW4P2jwPpSLghLRMHLAgs+wSc4giPaqJIsTKuDTnVYRORDTJKIyCeOVAJv7QdquOYREYWIZjRjIzbiIA7iRtyIfOSrDomIfIRJEhF5ffRIFoTddEZ1JEREvlGNaizDMhSiUEuW2NyBKPQwSSIirympBv65r62DHRFRqJPGDudwjs0diEIQkyQiumYWG7D4KLDuNOceEVH4Nne4HtdjOIZzvhJRCGCSRETX5Gw98Pe9wIVG1ZEQEalt7rAFW3Acx3EzbkYGMlSHRETXgEkSEfWK3QGsOAF8UALYOHxERKSpRCUWYzFGYIRWgheJSNUhEVEvMEkioh6rMwJ/2w2U1KiOhIgoMEvwDuAATuM0JmESilGsOiQi6iEmSUTUIwcqgDf3AU1m1ZEQEQW2JjRhFVZpSZIkSwlIUB0SEXmISRIRecRmb2vOsOaUHCPlpGQiIk+VohTncV4rv5PGDnroVYdERN1gkkRE3ZKW3n/dDZyuk/8xQSIi6k1jh63YeqWxQyYyVYdERF3goQwi6tLui8CPNjkuJ0hERHQtqlCF9/CeljBZYVUdDhF1giNJRNTp2kfzDwMbz8j/OHpEROTtxg6yEO10TGe7cKIAxJEkIrpKTSvwX1sclxMkIiLyhVrUaqNKe7FXS5yIKHBwJImI2jlWBfx5lwNNFo4eERH5mh127MAOlKEM0zANSUhSHRIRcSSJiFytPQX8chsTJCIifytHORZiIY7hmOpQiIgjSUTknH/0v/sd2H5ekiMmSEREqjrgbcRGnMEZTMEUxCBGdUhEYYtJElGYk/lHv9tuw7mmCNWhEBHR5XWVKlCBqZiKIhSpDocoLLHcjiiMlVQDP9zABImIKNC0ohUrsAKbsEkbYSIi/+JIElGYWnfKjnmHdXCACRIRUaA6iqO4iIuYiZlIR7rqcIjCBkeSiMKM3QG8vdeCdw/r4eD8IyKigFePeq1VeAlKVIdCFDaYJBGFEbMN+OXHJnx0LlJ1KERE1AM22LABG7TyO/mdiHyL5XZEYaLeCPz8IyMumdgtiYgomMvvqlCFW3ErEpGoOhyikMWRJKIwUFZrw/fWmpggERGFAEmSFmGRtgAtEfkGkySiELf7rBE/3WxHiyNadShEROQlJpi07nef4BM44FAdDlHIYbkdUQhbdrABS07HAzp2sCMiCkW7sRuXcAm34BYuPkvkRRxJIgpRf9tSgyWlSUyQiIhC3Dmc08rvJFmiwDRt2jS88MILfrmd4uJi/PKXv7zmvxXumCQRhRi73YFfrL2EHTVpqkMhIiI/aUITlmAJDuMwgtXTTz8NnU6Hr3zlK1ed97WvfU07Ty7jDaWlpdrt7d27t9vLLl68GDfeeCOSk5ORmJiI4cOH9zjhWbRoEX74wx9e+T8TmcDHJIkohBjNVvxoxSUca81SHQoREfmZHXZsxmZ8jI+134NRYWEh/v3vf6O1tfXKaUajEf/6179QVFTk93jWrl2Lhx9+GPfffz927NiBXbt24cc//jEsFkuPbictLU1LsCh4MEkiChH1TUZ8f3k1ztuzVYdCREQKHcIhrMIqWNCzHflAMG7cOC1RkpEXJ/ldEqSxY8e2u6zJZMJzzz2HrKwsxMTE4KabbsLOnTuvnF9bW4vHH38cmZmZiI2NxcCBA/GPf/xDO69v377aT7lNGVGSMjZ3li5dismTJ+PVV1/F4MGDMWjQINxzzz343e9+d+UyMrolp7mSkSbX23Qtk5Pfz5w5gxdffFH72/Itqqur8eijjyI/Px9xcXEYOXIk3nnnnatislqtePbZZ7WRrYyMDHznO9+Bw9F58466ujp84Qtf0B6HpKQk3HLLLdi3b1+nl6c2TJKIQsDFqgZ8f3UDaiKYIBEREbT24FJ+14xmBJvPfe5zV5IZ8fe//x2f/exnr7rca6+9hoULF+Kf//wndu/ejQEDBmD27NmoqanRzpfk4fDhw1i+fDmOHDmCP/zhD1pSIWRUSKxZswYXL15sl5S5ysnJwaFDh3Dw4EGv3T/5WwUFBfjBD36g/W35do6YXXfddVi2bJn29770pS/hM5/5zJVYneT+GgwG7fRf/epX+MUvfoG//vWvnf69Bx98EJcuXdIeBxkJk0R0xowZVx4nco9JElGQKzlbiZ9sMqM5kiV2RET0qWpU4z28p62rFEyeeOIJbN68WRttke+PP/5YO81Vc3OzlvT893//N2677TYMGzYMf/nLX7QRo7/97W/aZcrKyrSRovHjx2tzgGbOnIk777xTO09GVUR6erqWCEk5nDtf//rXcf3112ujOnIbjzzyiJa0yShWb8nfioiI0Mrv5G/Lt5ARpFdeeQVjxoxBv379tL89Z84czJs3r931ZaTtjTfe0Ea2ZKRMLif/d0ceR0mm5s+frz0OMpr285//HCkpKViwYEGv70M4YJJEFMR2HC7Dr3YYYIpqOzJGRETkSkaSlmIpzuAMgoUkMHPnzsWbb76pjSjJ784RIKeTJ09q84KkFM4pMjISEyZM0EaNxDPPPKPNb5KkQ0adtmzZ0uNY4uPjtZGdEydO4Nvf/jYSEhLw8ssva3+npaUF3mSz2bTmDpKQSSIlf2vlypVasudKmkg4S/TExIkTcfz4ce36HUlZXVNTk5YMyu05v0+fPq09htQ5rpNEFISk9njdJ8exoCwb9uhk1eEQEVEAk7lJMkdpIiZiBEYgWEruZN6NcJ3/0xMywiQjUR9++CFWr16tlZhJlzwZSemp/v37a98yt+db3/qWNjfp3Xff1coA9Xr9VXOCetrYQciomJTPSdc7SZQkQZN5TGazGb0lCVJubi42bNhw1XkymkSd40gSUZCRDfHyLYcxnwkSERF5yAEHtmCL1vlOfg90UmYmyYEkGzLPqCNJWKKiorRSPCe5rDRukNI711Gpp556Cm+//baWfPz5z3/WTpfrCnejL92RsjtprCAlf86/4ZxX5NRda3H5+x3/ttyXu+++WystHD16tFZyV1JSctV1t2/f3u7/27Zt08ropISvI5l/VF5ers1hkjlbrt8dR+eoPSZJREHEbrfjvQ178H55ARxMkIiIqBed71ZiZcB3vpMdfimbk8YL7nb+ZZRFyumk69yKFSu0y33xi1/USuA+//nPa5d5/fXX8f7772ulctJ84YMPPsDQoUO186QjnsxfkutWVFSgvr7ebRzf+973tFI9GYmRErU9e/Zoo1ySkN16663aZaRb3CeffIK33npLK3v77ne/222jB0m0Nm3ahPPnz6Oqqm3OmCQ6MuIlZYFy37/85S9rsXUk5XcvvfQSjh07pnW/+81vfoPnn3/e7d+ReVhSjifd91atWqWtDyW3L6NhEjN1jkkSUZCwWm2Yt2oHVlT3A5ggERHRNXS+k3lKgd75TtpVy3dnfvrTn2rrF0kHOBkxkWRI5vCkpqZeGa35z//8T4waNQpTpkzRki2ZoyRkZOXXv/41/vSnPyEvL08bwXFn6tSpOHXqFJ588kkMGTJEK+GTkRlJOKRxgpCRLumkJ8mUNHlobGzULt8V6WwnCYuMiDmbSMicJ7kfcnvSJlwaOnRsLS7ktmUdKZkXJeWDkiBJJzx3ZO6SlBvK/ZfSQCkTlOYTUoaYnc2OuF3RObpqrE5EAcFsseDdlduw2TgSiGENMdGTo4HJhdd2GyfKTuBP7/wJRXlFMES4n6Ib8djVR7CJQkUiEjEXc5GEzhMRonDFkSSiAGc0mvDWko+wuXUEEyQiIvKaRjRqaynVolZ1KEQBh0kSUQBram7B39/bgJ32sUBsW/kAERGRt7SgRSu9C7a1lIh8jUkSUYCqb2zC3xatw96I65kgERGRzxhhxAf4ABW4ukkAUbhikkQUgBqamvHPxWtxKOoG6GLdrwJORETkLWaYsQzLcB7nVYdCFBCYJBEFYoK0cCUOGsZDF9/W8YaIiMjXrLBiBVbgDM6oDoVIOSZJRAGWIL21cDkO6MdAl5SvOhwiIgozNtiwGqtxEidVh0KkFJMkogBKkP534XLstw+HLrWf6nCIiChM2WHHOqzDMRxTHQqRMkySiAJAY1ML/m/RCuwz94cus201cCIiIlUccGAjNuIgDqoOhUgJJklEAZAgvb1oOfY05UKXO1Z1OERERFdswRbsxV7VYRD5HZMkIsXrIGkJUl0ydEWTVIdDRER0lR3Ygf3YrzoMIr9ikkSkSKvRhHfeW4U9lwzQ9ZupOhwiIqJObcM2HMZh1WEQ+Q2TJCIFzBYLFixbi09ON0I3aK7qcIiIiLq1GZvZzIHCBpMkIj+zWm14f+VGbN5fBv3IBwG9QXVIREREHtmETTiBE6rDIPI5JklEfmS327Fi/Ras234QEaMfgT0iRnVIREREPep6tx7rcRqnVYdC5FNMkoj8xOFwYO3mnVi+cTsiRjwEW1SK6pCIiIh6lSjJOkrncE51KEQ+wySJyE+2fLIfH6zZDP3gO2CJz1MdDhERUa/ZYMMqrEIFKlSHQuQTTJKI/GDXgaNY9OF6OAonw5w6RHU4RERE18wKK1ZgBWpQozoUIq9jkkTkY0eOn8a8JathTh0EU+6NqsMhIiLyGhNMWIZlqEe96lCIvIpJEpEPnbt4Ce8uWY0mfTIsfWerDoeIiMjrWtGKD/EhmtGsOhQir2GSROQjtfUNeOe9FSivbQWG3g+HLkJ1SERERD7RiEat9M4Ci+pQiLyCSRKRD7QaTfj3+6tx4vR5xIx5GFZDvOqQiIiIfKoa1ViLtVr3O6JgxySJyAeLxS5avh57Dx5D0ui70BqTozokIiIivyhDGbZiq+owiK4ZkyQiL6+FtHLjVmzevhepgyaiIXm46pCIiIj86uDlL6JgxiSJyIu27jqAlRu2ITm3H+qyb1YdDhERkRIymiSjSkTBikkSkZccLjmN91ZsQGRsPJr63A6HzqA6JCIiIiVkXpLMT6pClepQiHqFSRKRF1yoqMS8pavRYjTBPuhuWAwJqkMiIiJSSjrdrcRKtganoMQkiegaNTW3YP7StSi/VI3EYbPREpOnOiQiIqKAIAkSW4NTMGKSRHQNbDYb3l+1CYdKTiFn0FjUJI5QHRIREVFAtga3w646FCKPMUkiugYbt+3Gxzv2ITe/AJUZUwAd31JEREQdsTU4BRvu0RH1kowefbj2YyQlxqMxfwasEXGqQyIiIgpYh3AIh3FYdRhEHmGSRNQLFZXVWLhsHUwWCwx9JqI5Jl91SERERAFvC7bgEi6pDoOoW0ySiHqopdWIeUvX4nz5JeT0HY6qpDGqQyIiIgoKMi9pDdbACKPqUIi6xCSJqAfsdjuWrN6Eg0dPoE+fPriYdjMcugjVYREREQWNJjRhHdZpaykRBSomSUQ9sOWT/di8bS/ycjJQmzkZFkOi6pCIiIiCzjmcwy7sUh0GUaeYJBF56My5i/hgzWbExkbDkTUSDbHFqkMiIiIKWruxW+t6RxSImCQReaC5pVVr1FDf0ISM3EJUJF2nOiQiIqKgtx7r0YhG1WEQXYVJElE3HA4Hlq3djKMnz6BvUR4qUifCro9SHRYREVHQM8GkNXKwwaY6FKJ2mCQRdWPn3sP4aPte5GVnoCl5CJqjc1WHREREFDIqUYmP8bHqMIjaYZJE1IULFZVYuuYjREYaEJuajcrEsapDIiIiCjlHcRQlKFEdBtEVTJKIOmE0mrBw2XpUVtUiPzcLF5NvhF0fqTosIiKikPQRPkI1qlWHQaRhkkTUyTyklRu3aeshFRfloi5hKFqjs1WHRUREFLJkXpLMT7LAojoUIiZJRO7sP3IC67fsQlZGKhCbgcrEUapDIiIiCnn1qMc2bFMdBhGTJKKO6uob8cHqTbDb7UhNTcHFlBvh0BlUh0VERBQWjuAI108i5ZgkEbmQxGjZuo9x5nw5ivKzURs/CMaoDNVhERERhZWN2AgjjKrDoDDGJInIxZ6Dx7Bt1wHk52TBHpmIygSW2REREflbK1qxCZtUh0FhjEkS0WVVNXX4YM1mRETokZQYj4rk8XCwmx0REZESpSjFMRxTHQaFKSZJRNJRx2bDsjWbcb78EgrystEYU4CmmALVYREREYW1LdiCRjSqDoPCEJMkIgA79x3Bzn2HUZiXDUREoSJpvOqQiIiIwp60A1+P9XDAoToUCjNMkijsXaqqwYdrP0ZUVCQS4uNQmTga1og41WERERERgHKUYx/2qQ6DwgyTJAprVqsNS1d/hIqqauTnZKI1Mg11cQNVh0VEREQuPsEnqEa16jAojDBJorC2Y+8h7D5wFIV5OdDpI1CePAHQ8W1BREQUSOywYx3WwQab6lAoTHBvkMJWTV0DVm3chujoKMTHxWhrIpki01SHRURERG7UohY7sVN1GBQmmCRRWHI4HFi5YSsuVFQhLycTVn00qhJGqg6LiIiIunAAB1CJStVhUBhgkkRh6VDJKWzbfRB52RmI0OtRlTAKdn2U6rCIiIioC9Ll7iN8pJXfEfkSkyQKOy2tRixf9zHsNjuSkxJgNKSgLq6/6rCIiIjIA1WowiEcUh0GhTgmSRR2Nm7djROnz6EwP1v7/6Wk69isgYiIKMi63TWhSXUYFMK4Z0hhpex8OdZv+QRpqcmIjDSgIaYQLdFtyRIREREFzyKzW7BFdRgUwpgkUVitibRs7cdoaGpGZnoK7NCjMnGs6rCIiIioF0ovfxH5ApMkCqs1kQ4cOY6i/BzodDrUxg+BxZCgOiwiIiLqJRlNklElIm9jkkRhob6hCas/2o6YmGjExkTDqo9BdcJw1WERERHRNZB5STI/icjbmCRRWNiwdRfOX6zUWn6LysTRsOsjVYdFRERE1+ggDmod74i8iUkShbyzFyrw8c59yEhLQUREBEyGJNTH9lUdFhEREXlx7ST5SeQtTJIopNntdqzetB31jc3ISEvWTpOFY9nym4iIKHRUohKHcVh1GBRCuKdIIe3QsVPYc/AYCnIztWYNrZFpaIwtUh0WERERedkO7EALWlSHQSGCSRKFLKPJjFWbtsPhcCAhPk47rSpxtOqwiIiIyAekyx2bOJC3MEmikLVjzyEcP12Ggry2xWKbo7LQHJ2rOiwiIiLykWM4hlrUqg6DQgCTJApJtfUNWLt5BxLiYhEd1dbFjqNIREREoU2aN2zDNtVhUAhgkkQhacOW3bh4qRo5l1t+N0bnozUqU3VYRERE5GNncRbncV51GBTkmCRRyDlfXomtu/YjMz0FEXq91hC0KnGU6rCIiIjIT2Q0iS3B6VowSaKQIk0aZOHY+sYmpKe2tfxujOkDU2Sq6tCIiIjIT6pRjeM4rjoMCmJMkiiklJ69iF37jyA7M11r+a2NIiUMVx0WERER+dlO7IQVVtVhUJBikkQhwzmK1NJqREpSgnZaU3QBzJEpqkMjIiIiP2tGM/Zjv+owKEgxSaKQUXKqDPsOlyA3O0MbRRLVHEUiIiIKW/uwD61oVR0GBSEmSRQSbDYb1m/5BGazBUkJ8dppzVE5MEalqw6NiIiIFOECs9RbTJIoJBwuOY1Dx04hL+fTNt+ci0RERERHcZQLzFKPMUmioGexWrVRJLvdgfi4WO20lsgMtEZnqw6NiIiIFJNW4NuxXXUYFGSYJFHQ23/kBI6dPIP83E9HkaoTRiiNiYiIiAJHGcpQiUrVYVAQCbgkacOGDdqk+7q6OgSq733vexgzZkzIxOHJ7Tz99NO45557EIijSBu37oI+Qo/YmGjtNKMhFc0xeapDIyIiogCyG7tVh0ChmiTJjrIkMF/5yleuOu9rX/uadp5cJlioTHb27duHu+66C1lZWYiJiUFxcTEefvhhXLp0yePbeOWVV7B27dqAT2R8SeYhnSw9j7ysjCunsaMdERERdXQGZ7RFZol8MpJUWFiIf//732ht/bSdotFoxL/+9S8UFRUhEJjNZgSyyspKzJgxA2lpaVi5ciWOHDmCf/zjH8jLy0Nzc7PHt5OQkID09PSw7mj30fa9kG7fMZdHkcwR8WiMKVAdGhEREQUgjiaRz5KkcePGaYnSokWLrpwmv0uCNHbs2HaXNZlMeO65566Mltx0003YuXNnu8t8+OGHGDRoEGJjYzF9+nSUlpZe9Tc3b96Mm2++WbuM/G25TddkQkZhfvjDH+LJJ59EUlISvvSlL2mn/8d//Id223FxcejXrx++853vwGKxaOe9+eab+P73v6+N6MgImHzLaUJK/b7whS8gMzNTu71bbrlFu5w7mzZtQmRkJMrLy9ud/sILL2gxu/Pxxx+jvr4ef/3rX7XHrG/fvtp9f+ONN7TfnfGlpLRfBPW99967sv5Px5Ew+f2f//wn3n///Sv3R0oXu3scXP3pT3/SHl+53EMPPaTF2Bm73Y6f/OQnWrzyvIwePRoLFiyAPx05UYqSU2e0dZGcauMGAbqAqyIlIiKiAHAap1GDGtVhUBDo1d7k5z73OW3kw+nvf/87PvvZz151uddeew0LFy7Udt53796NAQMGYPbs2aipaXtxnj17Fvfddx/uvPNO7N27V0tMvvGNb7S7jZMnT2LOnDm4//77sX//frz77rta0vTss8+2u9zPf/5zbUd9z549WhIgEhMTtWTj8OHD+NWvfoW//OUvWiIipLTt5ZdfxvDhw3Hx4kXtW04TDz74oFb2tnz5cuzatUtLDGXkxxm3qylTpmiJx//+7/9eOU0SkP/7v//THid3cnJyYLVasXjxYjgcDniDlN5JYiOPlfP+TJo0qdvHwenEiROYN28eli5dihUrVmiP41e/+tVO/54kSG+99Rb++Mc/4tChQ3jxxRfxxBNPYOPGjfAHSdI279injSbFxca0naYzoD6uv1/+PhEREQWnPdijOgQK1SRJdoYlUTlz5oz2LSMjcporGen5wx/+gP/+7//GbbfdhmHDhmk75zLq8Le//U27jJzfv39//M///A8GDx6Mxx9//Ko5TbIzLqfLyMzAgQO1Hf9f//rX2g66lPk5yWiPJD1ye/Itvv3tb2uXl5EmScQkkZBEQEgcUq5mMBi0pEW+5TS5Xzt27MD8+fMxfvx47W9KAiajOp2NlHz+859vlzRKoiGxSdLizo033ohvfvObeOyxx5CRkaE9PvI4VVRUoLfkvkj80dHRV+5PVFRUt4+Dk8Qrj6mMTEni95vf/EYrq+w4QuYcIfx//+//acmxJL2SJMrzJq8BGY3yh+Onz+LI8dPtRpHqY/vCrm+7z0RERETunMIp1CFwG4RRYDD05kpShjZ37lxtdEJGQuR32dnvOAIkIyqTJ0++cpqUpU2YMEGbgyPk5w033NDuehMnTmz3fylzkxEkGZlxkr8pIwmnT5/G0KFDtdMkoelIRp0koZJYmpqatNEbKZ/rivw9uWzHuT4yB0tuxx1JECQR2bZtm5YAyeMiCVJ8fHynf+fHP/4xXnrpJaxbtw7bt2/XRmQk8ZDyvZEjR8KbPHkcpFwyPz+/3fMgj/GxY8e0hKvjqFNLSwtuvfXWq+aCdSy59AV5/j/euQ9miwUJ8XFtp0mpXfxgn/9tIiIiCv51k2Q0aTqmqw6FQi1JElJK5ix5+93vfgdfkZ36L3/5y9o8pI5cG0V0TEi2bt2qjUDJvCMZ7UhOTtZGRmTUqru/l5ube2U+j6uOc4ScZM6VjNDIaJLM0ZEyPXfX70gSMSntk29JkCTBkFErKU/U6/VXleK5m0fUnd4+Dt09RmLZsmXtEishI1m+dvrsBRw8ehI5mWlXTmuOzoXZ0HUCTEREwWv5T5Zjz6I9KD9ajqjYKPSb1A/3/ew+5Az+9EDe/0z7H5RsLGl3vSlfnoLH//h4p7drbDJi8TcWY+97e9Fc3YyMvhmY/tx0TP3K1CuXmffSPGx9cyui46Nx70/vxQ2Pf3qAd9f8Xdj61lY8u7T9NAAKbCdwAtfhOiSB+w7k5SRJ5r7IyIE0CJCd746k5E3KvaQUr0+fPld28qVxg5TOCRkFWrJkSbvryWiMK5kPJHNpZD5TT2zZskX7u9/61reunCalga4kPpnT0vHvSYmZlOFJeZqnZD7Vo48+ioKCAu2+u46geUJikes5G1LIaF1jY6P2f2cCKPO2uruNjvfHk8dBlJWV4cKFC1qHPefzIImalEF2JKWTkgzJdaZO/fRDxB+co0gtrUYU5GVdOb02jqNIREShTJKfaV+bhuLri2Gz2vDeN9/Dr2b9Ct87/D0teXG66Ys34a4f3HXl/1FxXZdhz39pPo6tO4bPvf05pBen4/Cqw3jnq+8gJS8Fo+8ajX1L92Hnv3bi+VXP49LxS3jrc29h+OzhSMhIQGt9K9771nt4cc2LPr3v5LvRpKnw734MBY9etwGLiIjQyuUkgZHfO5Id+2eeeQavvvqq1ghALvfFL35RK9OSOTxC1ls6fvy4dhkp65I24s4Oc07SmU129GXUSpIEubx0cOvYuKEjmUskO/EyaiJlZlJuJo0SXEkSJCV7crtVVVXaXJuZM2dqpWay3tCqVau0bnvy9yXJ+OSTTzr9e5IoSgnbj370I7dNLFx98MEH2vwd+VlSUqLddxlBkk5/d999t3YZKUOULnMyd0nid/fYdCT3R0oT5fbk/khS6snjIKT74FNPPaWVG3700UfayJ2UDHYstXM2gpB5TdKsQUa95HalMYfMY5L/+9L58krsO3QcWZmpVzr9mQxJ2kgSERGFrudXPI9JT09C3vA8FI4uxNNvPo2ashqc2dXhAGhcFJJzkq98xybFdnm7p7acwsSnJmLwtMHIKM7AlC9NQcHoApzecVo7v/xIOQZNG4Ti8cWY8OgExCTFoOp0lXbewtcWYuozU5FW9GllAwWP4ziORjSqDoMC1DX1SpakoKs5Pj/96U+1rnSf+cxntBEamcsi6wKlpqZeKZeT7nfS2lo60znn5bgaNWqU1jFNkglpqS0laa+//vqVEY/OyEKtshMvyZQ0I5BEx9n1zklikxExab8tIzfvvPOOtuMtyYo0L5BkR1pnP/LII9roS3Z2dqd/T0ZdZG6SjORIK/KuyEiMJEDSaEJik3lM0khBWoLLYyVkDaW3335bi0XmKEls0ua7K5KEysiPzM+S+yOjeJ48DkJG6qTT4O23345Zs2Zpj/vvf//7Tv+WtFyX25HGGjIiKI+jlN85W5j7yq79R9DQ1ISUpMT2o0gurdGJiCj0ySiOiE9rX26/4/924KWMl/D9Ed/H4v9cDHNL12snStneviX7UHu+VqtWOLb+GCpKKjBs1jDtfEmYznxyBs21zVpCZmm1IHNAJk5sPoGy3WW45blbfHgvyZfssGMvuq7SofClc3irBzVpI2SyUGzHEkLyjvrGJvz37/8XFpsVOZltjTVsukicyLoHDn2k6vCIyI+eHA1MLry22zhRdgJ/eudPKMorgiHCffV5xGNXV0qQetJY6Pd3/R4tdS14bfNrV07f9OdNSO+TrpXKndt/Dov+YxGKJxTjmUXPdHpbFpMFb3/pbWx7axv0Br120POJvzyBiU9+2khq6feWYvvb2xEZG6mV8o2cOxI/vu7H2mjWqa2nsP4367Xyuyf+/IQ20kXBQw89HsNjiENbIyiia56TRJ+SRVcPHDiglcQxQfIdKbOrqqnDgL4FV05riC1mgkREFGbe+do7uHDwAl7d/Gq706VUzil/ZD6Sc5Pxxow3UHmyEpn9M93eliQ4p7edxleXfFVLsI5vOq7dviRaQ2e2ddC983t3at9OS7+/VDsvIjICH/7oQ7x+4HXs/2A//vHkP/CtXZ/OAabgGE06giNaEwcir5XbURuZRyQlajLHqmNbbPIOafe9ddd+xMREtZsDVxfXs4YeREQU3N559h0c+OAAXlr/ElIL2sr3O9P3hrYS8EsnLrk939xq1hpAPPiLBzH6ztEoGFWA6c9Ox/iHx2PVz1e5vY5019vx9g7c9cO7ULKhBAOnDERiZiLGPzReK78zNn66hiMFh8M4rCVLRK44kuQFnrT7pmtz5Hgpys5XoNClo11rZBpMkV1/QBIRUWiQ2QH//vq/sXfxXry04SWtVXd3zu49q/2UESV3bBab9q3Tt5/Xqo/Qw2F3uI3h7S+/jQd+8QBiEmJgt9m16ztvS8hpFFxa0aotMDsAPPBKn+JIEgU8+VDavvuA1rAzOvrTVq4cRSIiCh9SAifzgj7/r88jJjEG9eX12reMBgkpqVv2w2Vac4Wq0iqtGYOUv8lIj4wQOb0+5HXsWbxH+1063w2aOggLX12IYxuOaV3rtry5RZufNPbeqxdH3/zXzdqokYw6if6T++PouqM4te0U1ryxBrnDchGXwrktweggDqoOgQIMR5Io4J05dxFHT55BVsanLVbtugg0xrStv0VERKFv4x82Xlkw1tVT/3hKaw0eERWBI2uOYO0v18LUbEJaYRrG3T8Ot3/79naXrzhWcaUznvjCv7+gdcH7++N/R3NNM9L6pOHuH9+NKV/5dH6TaKhowPIfL8drWz5tFNF3Ql/c+vKt+O3c3yIxKxFP//NpH9178rVLl7+y8GnFCoU3drejgLdw2Tqs2rQNg/v3ubI2Ul1sP5Sn3Kg6NCJShN3tiMjbpNzuFrClO7VhuR0FtNr6Bnyy/wjSU5OvJEiiPq6f0riIiIgotMi8pBa0qA6DAgSTJApoB46cRE1dg5YkOZkjEtEaxeFwIiIi8n47cCLBJIkCltVqw449BxEbE6Ut7ufEUSQiIiLyBbYDJycmSRSwTp45h7ILFe0aNsgEuvrYYqVxERERUei2Az+Jk6rDoADAJIkC1p5Dx7RFZGNjoq+c1hqZCWtEvNK4iIiIKHSxHTgJJkkUkOrqG7Hv0HGkpbRfALAhlm2/iYiIyHcqUam1A6fwxiSJAtKhklNaZ7u01KQrpzmgQ2NMkdK4iIiIKDzmJlF4Y5JEAcdut2PX/iOIioxEhEvDhuaoHNgiYpTGRkRERKHvNE7DAovqMEghJkkUcMrOl+N02QVkpqe2O52ldkREROQPkiDJukkUvpgkUcA5ePQkWlqNiI/7dNTIDj2aYgqUxkVERETh4xiOqQ6BFGKSRAHFaDThk/1HkJyUAJ1Od+X05ug82PVRSmMjIiKi8FGOctSjXnUYpAiTJAoox0+fRWV1LdLT2NWOiIiI1OJoUvhikkQB5dDxU7DbHVrTBie7zoCmmHylcREREVH4OY7jcGhL2VO4YZJEAaOpuQUHj5xESnJC+9Ojc+HQGZTFRUREROGpGc24gAuqwyAFmCRRwCg5VYaaunqkpXy6NpJoimbDBiIiIlI3mkThh0kSBYxDx9pabRoMhnYLyDbF5CmMioiIiMJ9zSQrrKrDID9jkkQBob6hCYePn0Zqh1Gk1qhM2PXRyuIiIiKi8CZrJp3BGdVhkJ8xSaKAcOzkGdTVNyI1ObHd6Y3RbNhAREREarHkLvwwSaKAcODoCej1OkRERLQ7nQvIEhERkWrncA5GGFWHQX7EJImUq6qp05o2pKW2XxvJZEiCxdB+ZImIiIjI3+yw4xTa5k5TeGCSRMpJglTf2ISUpI6tvzmKRERERIGB85LCC5MkUu7oiVKtzE6vb/9y5AKyREREFCjO4zzMMKsOg/yESRIp1dDUjOOnz141imTVR6M1Ml1ZXEREREQdS+7KUKY6DPITJkmk1Kkz57WudilJ7eceNUflADq+PImIiChwlKJUdQjkJ9wLJaWOn247ImMwtO9q1xKdoygiIiIiIvfO4ixssKkOg/yASRIpYzJbcOjYKSQlxl91njaSRERERBRgC8vK3CQKfUySSJkz5y6iurb+qgVkzRGJsBquTpyIiIiIVGPJXXhgkkTKnCw9B7PFiujoqHanN0dnK4uJiIiIqLtW4A44VIdBPsYkiZSw2+04cPQE4uNirjqvhaV2REREFKBa0YpLuKQ6DPIxJkmkxPnySpRfqr6q1E6Oy3AkiYiIiALZaZxWHQL5GJMkUqL07AW0tBoRHxfb7nRjZBrs+mhlcRERERF5UnJHoY1JEilxovSc1vZbp9O1O70liqNIREREFNjqUY9a1KoOg3zI4MsbJ3Kn1WjSmja4bf3N9ZH87vCHf8Dh5X9AY0Vbt57UouEY98jrKBp/m/b/ltpybPv7qzi/dzUsrY1Izh+MsQ99C/0m39/pbe6Z/xOUblmEuvNHEREVi+whk3DD0z9DSsHgK5fZ+teXULL2TRhi4jHhqZ9i4LTHr5x3avN8lKx7C3NeX+rT+05ERHQto0mpSFUdBvkIR5LI785eqEBdQyOSEhPane6ADsbIDGVxhav4jAItSbnvl7tw7xufIG/ULVj147tRc+aQdv76XzyJ+vPHMPs7S/DAbw+g76T7sPa/HkLVyT2d3ubFgxsxbO7XcPd/b8PcH66G3WbBh6/PgsXYrJ1/ZsdSnNj4L9z+g1W44en/wqbffAHG+irtPHNzPXb+77dw01d+56dHgIiIqOe4XlJoY5JESpIki9WG6KjIdqebDMmw69ufRr7XZ8KdKBp/O5LzBiIlfxAmPPljRMYk4NKxbdr5FUe3YPgdX0fWoAlIyumHcQ9/G1HxKag6savT27z9+ysweObTSOszHOl9R2PaC2+iqbLsynVqzx5B7shpyBw4HgOmPoqouCQ0VLRNgt32j9cw9LZnkJBV5KdHgIiIqOfKUQ4bbKrDIB9hkkR+d+xEKaKjr06GWqM4iqSa3WbDiU3/1kZ8sodM1E6TUrlTH70LY2MNHHa7dr7NbNSSHE/J6JCITkzTfkriVHXiE5iaalF5YhesplYk5w1A+aHNqD65GyPufM5H95CIiMg7JEGqQIXqMMhHOCeJ/Kq+oQllFyqQ3KHUTrRGZiqJiYCa0gN479WJWvITGZuAWd9ajNSiYdp5M/9jHtb+18N467F06CIMMETHYdY3F2tJjScksdr6lxeQPXQy0vqM0E4rHDcbA6Y9gcUvXa/NWZr24j9hiI7HR394Rht1kjlShz74DWKSMnDz1/6sjUgREREFmgu4gDzkqQ6DfIBJEvm91K6hsRl9i67eoHAkSR1pxnD/r/bC3FKP0x8vwIY3nsKdP9moJUqf/N93YGquw9wfrdGSltJt72HNfz2Eu376EdKKR3Z725v/+DXUlB3EXT/b3O708Y99T/t22vXO95E/eib0EZHY8+6PtPlPZTs/wIY3ntTmSxEREQXivKTxGK86DPIBltuRX505fxF2h11r/+3Kqo+GxdB+YVnyn4jIKG1kKHPAdZjw1E+0crgDS36FhosnceiD32Lqc39H/ugZ2unXPfpdZA4Yj0PLum+ssPmPz2qJzh0/Xo+EjIJOL1d39iiOr38b1z/xQ1w8sAG5w6cgNjkT/W6SBhG7YW5p9PI9JiIiunaVqIQFFtVhkA8wSSK/cTgcOHK8FLExVy8W28qudgHF4bDDbjHBamrR/q/Tt99U6PQR2mU6v75DS5BKty7GHT9eh6Scvl1e9qPffRkTv/ALrdTPbrdp3fCE3dr202HnxFgiIgo8dthxERdVh0E+wCSJ/Ka6th6Xqmrcz0eK4nwkVXb88z9x8eAmbZ0kmZsk/79wYAMGTHscKQVDkJQ7QEtiLpXs0EaW9i/+H5zbuxrFN95z5TY++NYMHPzgt1f+//EfvoYTG97GLa/8C5GxidpaS/ItDRo6Orrqr4hJztS67ImcYZNxfv86VBzdhgPvv4HUwmGITkjx06NBRETU83lJFHo4J4n85mJFFRqbWpCVcfXCaxxJUqe1/hLWv/EkWmouIio+GenFo3D791eiYOyt2vm3fe9DbH/zG1j5wzthaW3SkqZpL/xTaxvu1FB+EsaGtnWOhDReEB98s30HvKnP/0NrDe7UUluBPfN+jLv/a8uV06TV+Kh7XsaKH8xFbHKW1tSBiIgoUDFJCk06h9S6EPnBqo3bsGj5egzu3+eqRWRLch6EQ8ecnYg88+RoYHLhtd3GibIT+NM7f0JRXhEMEe63PxGPtZ8/SUTUkQ46PIknEY2rpxNQ8GK5HfnNqTPnEdVhAVnnIrJMkIiIiCgYOeDgaFIIYpJEftFqNGntvxPi4q46zxTJ+SZEREQUvJgkhR4mSeQXFy9VobG5BYkJVydJRsPVc5SIiIiIggWTpNDDJIn81rTBbLYg2l25XSSTJCIiIgpetaiFCSbVYZAXMUkivzh7sQJ6vR46ne6q80wGltsRERFR8C8sS6GDSRL5nN1ux8nTZxEfH3vVeRZ9LGwRMUriIiIiIvKWKny6FAYFPyZJ5HNVNXWorW9EgpskiaV2REREFAou4ZLqEMiLmCSRz5VXVqO5xYiEuKuTJCOTJCIiIgoBHEkKLUySyOcqq+u0VQRkTlJHnI9EREREoaAJTWhFq+owyEuYJJHPXSivhD7C/ar1LLcjIiKiUMHmDaGDSRL5vGlD2fmLiIu9ujmDA3qYIxKUxEVERETkbUySQgeTJPKp+sYm1Dc2I95NkmQ2JAA6vgSJiIgoNDBJCh3cQyWfz0dqbjUiLs5NkhSRqCQmIiIiIl9gkhQ6mCSRT1VW18JmtSHSYLjqPLOBSRIRERGFDmncIA0cKPgxSSKfqqishk7n/jyzIcnf4RARERH5FEeTQgOTJPKpM+fLERMT7fY8ltsRERFRqGGSFBqYJJHPtLQatXK7eDeLyAoLy+2IiIgoxHBR2dDAJIl8prq2TkuU3LX/tusiYI2IUxIXERERka/UolZ1COQFTJLIZ2rrG2EyWRAdFXnVeeYIzkciIiKi0NOMZlhhVR0GXSMmSeTTJEno9Ve/zNjZjoiIiEJVHepUh0DXiEkS+UxNbX2n51kiEvwaCxEREZG/1KPzfSAKDkySyGcuXqpGlJtSO2HhfCQiIiIKURxJCn5MksgnbDYbLlXVICYmyu351gj3He+IiIiIgh2TpODHJIl8oqGpWetsFxvtfo0kq54jSURERBSaWG4X/Jgkkc+aNhhN5k5HklhuR0RERKGKSVLwY5JEPlFX3wizxYKoyKvnJDmgg01/9dpJRERERKHAAovWCpyCF5Mk8lmSpJMvnc79KJKb04mIiIhCBeclBTcmSeQT1dL+u5M8iPORiIiIKNSx5C64MUkin6iqrUN0J+2/rZyPRERERCGOI0nBjUkSeZ3D4UBNXYPb+UjCwvbfREREFOKYJAU3JknkdSazBc0txk4XkrXpmSQRERFRaGtAg+oQ6BowSSKva2pugdksne0Mbs+36d23BSciIiIKFexuF9yYJJFvkiRp/93pSJL7BWaJiIiIQoUNNphgUh0G9RKTJPK6puZWLUmKNHAkiYiIiMIXR5OCF5Mk8rrG5hZtjSS93v3Ly6bjSBIRERGFvha0qA6BeolJEvmk3K4rHEkiIiKicMAkKXgxSSKva2hs6nQhWcEkiYiIiMIBy+2CF5Mk8rrq2gZEdtbZTmcAdBF+j4mIiIjI3ziSFLyYJJHXNTQ1I6rTpg2cj0REREThgSNJwYtJEnmVw+HQuttFGNyPFtl1LLUjIiKi8MCRpODFJIm8ymyxwmKxwBDhPknifCQiIiIKF0ySgheTJPIqo8kEq80GQycjSQ7ORyIiIqIwSpIccKgOg3qBSRJ5ldFogtVq63QhWTuTJCIiIgoTdthhhFF1GNQLTJLIq4wmc9tIUifldg7pbkdEREQUJlhyF5yYJJH3kySrtYvGDRxJIiIiovDRilbVIVAvMEkirydJNpsdEXr3Ly0HmCQRERFR+LDAojoE6gUmSeT1xg06HaCTf9xg4wYiIiIKJ0ySghOTJPKqVqNJMqROz2e5HREREYUTJknBiUkSeZXZbJGauk5xJImIiIjCCZOk4MQkibxK5iN1lSUxSSIiIqJwwiQpODFJIq+S9t9dLZnGcjsiIiIKJ0ySghOTJPIqi8UCHTqfk0REREQUTswwqw6BeoFJEnmVyWyBXs8kiYiIiEhwJCk4MUkirzJbJEniy4qIiIhIMEkKTtybJa93t9NxJImIiIhIwyQpODFJIq+yWG3Q6zp/WekcXbV1ICLqXnY8MDpbdRRERJ5hkhScmCSRV5nMZs5JIiKfSY0BXrgRSIhSHQkRkWeYJAUnJknkVRaLlXOSiMgn4iOB528A0mJVR0JE5Dl2twtO3Jslr6+TpNdxJImIvCs6Avj6BCA3UXUkREQ9Y4dddQjUC0ySyM84J4mIesagB74yHuibqjoSIqKec3DfJygxSSKv46aAiLxFxqWfHgMMy1QdCRERhRMmSeRVOm2XpqvzOeRMRJ57dARwfZ7qKIiIKNwwSSLv6mY6kt5h81ckRBTkxidXYmqx6iiIiCgcMUki7+tiLSSdw+rXUIgoOGWZTuIz1yepDoOIiMIUkyTyqu462+mZJBFRN1KMZXh1Vi5iYqJVhwIHF8AmIgpLBtUBUHjRsdyOiLoQb7yAV2ekICkxTnUoGGPWYfxfgZZIB1qj0PYdCZiiAFMkYIxywBwFWAyAOUoHaySufNsiAfvlb4cBcMjPSEDHxbaJiIICkyTyuq6Ou3IkiYg6E228hBdvikZGqvoyuyEWHSaa24otks1t31frecLTGnE54YoEWqIA4+WkS0u4IqF9W+Q7Sqf9tHWSdCFSvplwERH5CpMk8iody+2IqBcMplo8O8GBwpx01aGgrxWYavJNAhJr0yG2VbKljuf0/O/Z4EBL5KcjXPLTeHmUy+SSdLUf5XLAFqlrS7pcRri0Ua4IJl1ERE5MksgHSVJXjRtYbkdE7enNDfjCyFYMKlLf6zvPCsw06qHvRdLibxHQIdEC7ftqncXf+f0y69uSLm2E63Li1THpch3lco5wXRnlMrT9xOXRLpYWElEwY5JEXhUZaYDd3nmSxJEkInKls7Tg8QF1GDuoSHUoyLABc4x6GIIgQfKFKLsOUSYgxeTu3J4/Ji0Gh5ZwOZOtK/O5ohxa4mV2JlwGwBr16XyuKwlXlEtpoSE8nxMiUodJEnlVdFQk7PbOF4zV25kkEdFlNhPuzi/HTaP6qY4EyXZgrlGP6DBNkHwhzqpDnNtNfs8fY6vOoSVZzV2McrWNdF0e5Yr6dD6Xa8LFBhpE5CkmSeRVUZFRsHWVJHEkiYiE3YoZqWcw5/qBasOw2xFnc+AOix5xDu44ByqDQ4dEM7Tvq/X8eTNebqDhnNNldCZdknAZ2ka8LM6kSxKuy6Nd7hpoaKWF3czHJaLgwySJvCpGG0nqvNwuwu62joOIwonDjgkxJ/DgzYOV7lyePHIUdaVlmBmTi6T4GGVxkP/F2HSIaQVSvdBAww4Z5XK0jXK5NNCQ382Rl0sLtaTr07lc2nwuZ9LFBhohLwIRqkOgXmCSRF4VFR3Vdbmdw6ztIEHHdYyJwtVwfQmenjFQcYJ0BEc+3obXh05Cn7h4ZXFQ8JMmH/EWaN9X6/lr3KJrm8ulzedyHeWS0sLItrW52pUWuiZcbKARkAzc3Q5KfNbIq2Kio7pcoV421RF2M2wRPGpLFI6KbSV4Zu4ARESoO7J67vRprF64EHelZaBPnPpFa4lcRTp0SDZB+/bW2lwt0W2lhcZ2SVfbXC7X0kJtlOtyEw3XhEtGuNhAo/eYJAUnPmvkVZEGaUrb9UY0wmGCDUySiMJNjvkkXpxbjEiDuo+eivPnsXz+fEyJiUE/JkgUBrS1uVqAdG810DAArW6SLq200GWUSxItS8cGGi5lhdooV5iUFkZqGSYFGyZJ5FUGD3Z+OC+JKPykGkvx6m152mizKjWVlVg+bx7GOhwYlpSkLA6ioG6g0eO1uTpnurw2lyRdV0oLL3csdM7lupJ0yQiXs4GGS8LVrrQwQBtocCQpOPFZI68yGLovoWGSRBRe4o3n8erMdCTExyqLoaGuTkuQ+jY3Y3xWlrI4iOhT0XYdok1AqslLDTQMbV0LtW+Xka4rbeK1pKtDAw2XBZGdo1za3rEXSws5khScmCSRV2llNN1sV5gkEYWPGGMFXp4Sh/SURGUxtDQ3Y+WCBUirqsLU3FxlcRCRjxtoWKF9o8VLDTQudytsdV0MWUu+2hpoyELI5igdWnUWWKN0yCnIgyXC3vatt8Gss8KisyJSdrcDc5CLusAkiby+mCy6aNwgmCQRhQeDqRpfv1GP/KxUZTGYjEasWrQIEWfOYHZBgbI4iCgIG2iYoX1frWPG4ywjrnR/Y4MigWleDpB8jn2YyatiYqIhKVJXbcCZJBGFPr25Hl8abcKAgkxlMVgtFqxbsgQtR4/i7oIC6AN0vgIRhbgodXMxqfeYJJFXxcZEayV3Vput08sY7Ea/xkRE/qWzNOOJQfUYPSBPWQw2mw2bli9Hxd69eKCoCBFMkIhIleho1RFQLzBJIq8nSdK8wWrtIkmyXbXEORGFCqsJ9xVdwuQRRcpCkLXatq1bh5Nbt+KRoiJEMUEiIpU4khSUmCSRb0aSrDJz0r1IW7NfYyIiP7FZMCujDLOu66s0Qdr98cc4sH69liDF6PkxR0SKcSQpKPHTg7wqNrptJMnS5UhSS7fNHYgoyNhtmJhwEvdNHqA0jEO7d2P7ihV4qKAAiRHdL0lARORzHEkKSkySyKuio6MQFRnZZbmdHnZEcF4SUehwODAq8jienD5I6WKOxw8dwqYlS3BfdjbSPFjYmojILziSFJSYJJFXyQ5SfFxsl+V2IlJGk4goJPR3lODLtw6EXmFpW9mJE1izaBFuT0tDDndIiCiQcCQpKDFJIq9LTIjrstxOGDgviSgk5FlO4Pk5/bQyW1XKz53DigULMD0uDsWxscriICJyiwdughKTJPK6xIT47keS7BxJIgp26aZSvDqnoG0RaUWqKyrw4bx5GK/XY0hiorI4iIg6xZGkoMQkibwuOTEBdnvXjRlYbkcU3BKNZ/HqrRmIi41RFkNDbS2Wz5+PQUYjxqWkKIuDiKhTkZFMkoIUkyTySbldd1huRxS8Yo0X8cq0RKQmJSiLobmxUSuxy6qpwU0ZGcriICLqUoK67SRdGyZJ5HWJ8XFwyFcXbb45kkQUnCJNVXh+UiRyMtSN3JhaW7Fy0SJEnTuHW7OzlcVBRNQtlgEHLSZJ5HVJCfEwRETAauu8eUOUtdGvMRHRtYsw1eGZsVb0zVM3cmOxWLBmyRKYjx/HnXl5SluOExF1iyNJQYtJEnldQnwcoqIiYTZbOr1MhMOMCBvXSiIKFjpLE54e1ojh/XKUxWCzWrFx2TJU79uH+wsLEcEEiYgCHUeSghaTJPLJnKToqCiYukiSRJStwW8xEdE1sBrxYJ9KTBhaqCwEu92OLWvXonT7djxUVIRIJkhEFAyYJAUtJknkddLtKi42usuRJBFlZZJEFPBsFtyWdQ4zxvVVFoLMb9z10Uc4tGEDHi0qQozCRWuJiHqE5XZBi5805HUyRyAtJRkms7nLyzFJIgpwdhtuSjyFuyf2VxrGgZ07sXPVKjxSWIj4CHWL1hIR9RhHkoIWkyTyiYy0lG5HkqKZJBEFLocDY6OO4/FpA5U2Rzi2fz82f/AB7s/NRYrBoCwOIqIek4M6sbGqo6BeYpJEPpGclIBu1pNlhzuiADZIV4Iv3joQeoWlbaXHj2Pt4sW4Iz0d2VyMkYiCDUeRghqTJPJZG3A5Et2VSFsTdI7O24QTkRoF1uP4+ux+iFBY2nbhzBmsWrAAMxMTUcQjsUQUjDgfKagxSSKfSE1O1I5Ad7VWkg4ORFqb/BoXEXUtw3QKr8wpQlRkpLIYKsvLsXz+fEwwGDCIOxlEFKy4/QpqTJLIJ9JSkhAbEw2jsZvmDWwDThQwkoxn8eqsbO29q0pddTWWz5uHYRYLxiQnK4uDiOiasdwuqDFJIp9ISW5LklqNpi4vF22p81tMRNS5OOMFvHpLMlIS45XF0NTQgBXz5yOvvh4T09OVxUFE5BVMkoIakyTyieioSKSnJcNo6jpJirHU+i0mInIvyliJFyZHIystSVkMxpYWrFq4EPHl5ZiRlaUsDiIir2G5XVBjkkQ+k5ed2W25XYy1xm/xENHVDKZafO16O/rkqhu5sZjNWPPee7CeOoW5ublKW44TEXkNR5KCGpMk8pnMtBQ4uu1w1wK9vevRJiLyDb25EZ8d0YIhfbKVxWCzWrF+2TLUHjqE+woKoGeCREShQJZPiItTHQVdAyZJ5DMpyW1HULpLlFhyR+R/OksLHulfjfGD85XFYLfbsXnVKpzbsQMPFRXBwASJiEKp1I7btKDGJIl82uEuKtIAk9nS5eViLCy5I/Irmxl35F3E1NHFykKQgyc7Nm7Esc2b8XCfPojmzgQRhRLORwp6TJLIZ1JTkhCjtQHvrsMdR5KI/MZuxbSUUsyd0E9pGPu2b8fuNWvwcEEB4qUshYgolKSkqI6ArhE/mchnkhLikZgQ120bcJbbEfmJw47xMSfwyJSBSpsjHN23D1uWLcODeXlINhiUxUFE5DNcxiDoMUkin5GdsMK8HDS3GrtdUFZn77okj4iu3VBdCT43Q22CdPrYMaxbvBh3ZWYiMzJSWRxERD6VkaE6ArpGTJLIpwpys2C32bq8jOyuxVi5qCyRL/WxluCrs/sjIiJCWQznSkuxcuFCzE5ORkFMjLI4iIh8SkqI09JUR0HXiEkS+VRmutTk6rQuVl1h8wYi38k2n8RLc/ogSuHIzaULF7Bi/nxMjopC//h4ZXEQEflcaiqg8IAUeQeTJPKprPQ0xMXGoKWbkrtYc5XfYiIKJynGM3h1Vq7WREWV2qoqLJ83DyNtNoxMSlIWBxGRX7DULiQwSSKfSk9LRkJ8XLfzkmLNlX6LiShcxBvP49UZqVoDFVUa6+uxfP58FDU14QaWnxBROGCSFBKYJJFPRRoMyM/NRHNLa9eXs7fAYGvxW1xEoS7aWIGXbo5FRqq6kZuW5masWLAAyRUVmJ6VpSwOIiK/YpIUEpgkkc8V5efAYrF2ezmW3BF5h8FUg2cn6FCQrW7kxmwyYc3ixdCXluK23FxlcRAR+ZV0D2X775DAJIl8Lis9VfvpcDi6vBxL7oiund7cgC+OMmJQkbqRG6vFgvVLl6LxyBHcXVAAvcKW40REfl9Eluu/hQQmSeRzmempiI2O7nZR2TjzJb/FRBSKdJYWPD6gDmMG5imLQTpZbl65Ehd27cKDRUUwMEEionDCUaSQwSSJ/JIkxcfFdjsvKdpaBz0XlSXqHZsJ9xSU46ZRRcpCkNHibevXo+Tjj/FInz6IYoJEROGG85FCBpMk8rmY6ChtUdmm5q6TJB0cLLkj6g27FTNTyzDn+n5KE6Q9W7Zg39q1eKSoCLGymCIRUbhhkhQy+ClGftGvTz7Mlu5HiWItTJKIesRhx41xJ/DAzQOUhnFkzx5sW7ECD+XnI4mLKBJRuGKSFDKYJJFf5GVnQqfTwWqzdXm5OBPnJRH1xAh9CZ66ZZD2/lLl5JEj2LBkCe7JykJ6ZKSyOIiIlJLFsqOiVEdBXsIkifwiNzsDibKobDcld7GWKs5LIvJQP1sJvjJ7APQKS9vOnjqF1QsX4raUFORFRyuLg4hIOY4ihRQmSeQXaSlJSE9NRmNzS7fzkuLMFX6LiyhY5VpO4vk5xdqCzaqUnzuHFfPnY0psLPrGxSmLg4goIDBJCilMksgvpBRoYN9CtHTT4U7Emy76JSaiYJVmKsUrs/K0piiq1FRWagnSOADDEhOVxUFEFDCYJIUUJknkN4V5OXB4sKhsvKncbzERBZsE4zm8NjMDCfGxymJoqKvD8nnz0K+lBdelti0WTUQU9pgkhRQmSeQ3eTmZiIuNQXOLscvLRdkaEWlt8ltcRMEixliOl6fGIzU5QVkMLU1N2ghSelUVpmRmKouDiCjgmjbExKiOgryISRL5TXZGGlKTE9HY1NztZePMHE0ichVpqsJzNxqQl6lu5MZkNGLVokWIPHsWs3JylMVBRBRwCgpUR0BexiSJ/MZgiNDmJTV107xBcF4S0acizPX48hgL+heoK+WwWCxY+/77aCkpwV35+dArbDlORBRw8vNVR0BexiSJ/KpPQR7sdodn85Icdr/FRRSodJZmfGZQA0b2z1UWg81mw6YPP0Tlvn14sLAQEUyQiIg+JdtEJkkhh0kS+VWfghzExcWgqZsudxEOC2IsNX6LiyggWY24r+gSJo4oVBaCHNDYunYtTm3fjoeLihDJBImIqD2Zn8lFZEMOkyTyq5zMdGSmp6K+ofvGDAmmC36JiSgg2SyYnXkOs67rqzRB2rV5Mw5u2IBHCwsRo3DRWiKigMX5SCGJn3jkV3q9HsMH9UNzc6tHrY6JwpLdhskJp3DvpP5Kwzi0axd2rFyJhwsKkBARoTQWIqKAxVK7kMQkifyuuDAXOr0OVputy8vFWOvYCpzCj8OB0ZEleGL6QG0RZlWOHzyITUuX4r6cHKQaDMriICIKaJGRQHa26ijIB5gkkd8V5eUgOTEBDY3dtwJPMHE0icLLAEcJvnTrIG3UVZWyEyewZvFizE1LQw7r7ImIOpebK2UyqqMgH+CzSn6XkpyIwrxs1Dd6MC+JJXcURvItx/HcnH5au3xVLp49ixULFuCW+Hj0iY1VFgcRUVDgfKSQxSSJlBgyoBhmk6Xby8WZKxFhN/olJiKV0k2n8cqcQkRHRSqLoaqiAsvnz8f1ej0GJyQoi4OIKGhwPlLIYpJESvTJz0FkpAEmk7nLy+ngQILxvN/iIlIh0XgWr96aibjYGGUx1NfUYPm8eRhsNGJsSoqyOIiIgkZ8PJCaqjoK8hEmSaREQV42UpMTUceSOwpzscaLeGVaIlKT1I3cNDc2aiNIObW1mJyRoSwOIqKgwlGkkMYkiZSIiY7CkAF9PVovKd5UDp3d6pe4iPwpyliJFyZFISdD3ciNsaUFKxctQuyFC5jJDk1ERJ7jfKSQxiSJlBk8oA/gQLetwPWwId580W9xEflDhKkOz1xnQ3FeurIYLGYz1rz/PizHj+POvDylLceJiIIOR5JCGpMkUmZAnwIkJyV4NJqU2Frml5iI/EFvacJnhzdhWN8cZTHYrFZsWLYM1QcO4L7CQuiZIBEReS49HWAH0JDGJImUkQRpYN9C1NU3dnvZRNM56Ozdd8MjCnjWVjxYXIXrh6gr07Db7fh49Wqc2bEDDxcVIZIJEhFRz3AUKeQxSSKlhg7sC6vVpu20dUXvsCHRxC53FORsZtyefR63jC1WFoLD4cAnH32EI5s24dGiIsRwEUQiop7jfKSQx09HUmpAcQESE+LQ2NTS7WWTWkv9EhORT9htmJJ8Gnfd2F9pGPt37MCuVavwcGEh4iPULVpLRBS0ZNuZo65cmvyDSRIplZmeiqL8HNTUN3R72XjTRUTYuLAsBSGHHeOij+OxqYOUNkc4tn8/Pl62DA/k5iLFYFAWBxFR0I8icRsa8pgkkVKywzhiyACYTBatDKjLy8KBRCMbOFDwGawrwRdmDlSaIJ0uKcG6xYtxZ0YGMqOilMVBRBT0BgxQHQH5AZMkCoiSu/jYGDS3dD9KxJI7CjaF1uN4dnZ/RCgsbbtw5gxWL1iAmUlJKIyJURYHEVHQkxGkPn1UR0F+wCSJlMvPyUROVjpq6uq7vWycpQqR1u5bhhMFgkzTKbw8pwhRkZHKYqi8eBHL58/HDZGRGBgfrywOIqKQUFzMUrswwSSJlNPr9Rg7YjBaWozdltwJjiZRMEg2luHVWTmIjYlWFkNtdTWWz5uHYRYLRicnK4uDiChk9FfbfIf8h0kSBYQhA4oRHx+LpmYPutwZmSRRYIszXsCrt6QgOTFOWQxNDQ1YOX8+8hsaMFEWPSQiomsTHc3W32GESRIFhILcLBQX5KKqpvuSu2hrA2LMVX6Ji6inooyX8NJN0chMS1IWQ2tLC1YuWICE8nLMyM5WFgcRUUjp27et/TeFBSZJFBCk65eU3JnMlm4XlhUpLSf8EhdRTxhMNfja9Q4U5qgbuTGbTFj73nuwnzqF23NzlcVBRBRyWGoXVpgkUcAYPKAYyYnxqG/svjFDUusZ6O1mv8RF5Am9uRGfH9GKIX3UjdzYrFasX7YMdYcO4d7CQugVthwnIgopcXFAXp7qKMiPmCRRwMhMS8HAvoWoru1+YVk9bGzgQAFDZ23Fo/1rMG5wvrIYZAT2o1WrcP6TT/BQUREMTJCIiLynXz8pe1EdBfkRkyQKqJK70cMGwW6zw2azdXt5ltxRQLCZcWfuBUwZrW7dDOkKuWPDBhzbvBmPFBUhih/kRETexVK7sMMkiQLK4AF9kJKciJq6xm4vG2OtQ4y52i9xEbllt2J6ailun9BPaRh7t23DnjVr8EhhIeL03KwTEXlVYiLAJjhhh5+mFFCSExMwYkh/1NV3X3InOJpEyjjsuD7mBB6+eaA2CqrKkb17sfXDD/Fgfj6S2XWJiMj7OIoUlpgkUcAZOaS/ttNpNls8WjNJb+/+ckTeNkxfgs/OUJsgnTp6FOvfew93Z2YiIzJSWRxERCFtwADVEZACTJIo4Azq1we5WRmorKnt9rJ6Bxs4kP8V20rw1dkDEKFw5OZcaSlWLVqEOSkpyI+JURYHEVFIS00F0tJUR0EKMEmigBMTHYXrxwxDY2OLNiG9Oyktx/0SF5HIMZ/Ei7P7INJgUBbDpQsXsGLePNwUFYV+0paWiIh8g6V2YYtJEgWkkUMHIDHBszWTpIFDrKnCL3FReEsxluKVWbmIiYlWFkNtVRU+nDcPo+12jEhKUhYHEVFYYKld2GKSRAFJyu2GDOiDyqo6jy6f1nzU5zFReIs3nsdrM9ORmKBu5Kaxvh4fvvsuipubcT3LP4iIfCszE+DBqLDFJIkCkkyGv27UUO13s6X7xgwJpvOItHbfNpyoN6KNFXh5SizSUxKVxdDS3IwVCxYgtbIS0+SDm4iIfGvQINURkEJMkihgDR1QjJysdFRWdz+aJP3FUpuP+SUuCi8GUw2+PkGH/Cx1IzcmoxFrFi+GvrQUc3JzlcVBRBQ2pGMok6SwxiSJApbM+5igNXBo9qyBQ+sp6O1mv8RG4UFvbsAXRxkxsChLWQxWiwXrP/gATUeO4O6CAugVthwnIgobgwe3JUoUtpgkUUAbMWQAEhLi0NDU3O1l9Q4rF5clr9FZmvH4wFqMGZinLAabzYaPVqzAxd278UBREQxMkIiI/GP4cNURkGJMkiig5edkYnC/Io8bOKQ2lwAOu8/johBnNeLegnLcNLKPshBk9HT7+vU4sWULHikqQhQTJCIi/ygsBJKTVUdBijFJooBv4HD9GDma44DJ3H0Dh0h7CxKNZ/0SG4UouwW3ppdh9vX9lSZIu7dswb5167QEKVbPTTURkd+MGKE6AgoA/OSlgDdsUD8U5GWjorLao8uzHTj1msOOG+NO4f6bBioN4/CePdi+YgUeys9HYkSE0liIiMKKjCAVFKiOggIAkyQKeNFRkZg0fhRaW03aHI3uxFqqEcfFZakXRkaU4KlbBmojmKqcOHwYG5cswb1ZWUjnpGEiIv/PRWJ5MzFJomAxZvggZKanoLLGs7lJ6U0HfR4ThZb+9mP4yqyB0CssbSs7eRJrFi7EbSkpyI2OVhYHEVFYYttvcsEkiYJCcmICJowZjrr6Ro/agcebKxBrrvRLbBT8cs0n8NzsvjAY1JW2lZ87py0WOzUuDn3j4pTFQUQUtiRBiopSHQUFCCZJFDSuGz0USQnxqGto9OjyHE0iT6SZSvHqnHzERKv7YKy+dAnL583DdbKIcmKisjiIiMIaGzaQCyZJFDRyszIwcugAVFbVenT5BNNFxJg9a/ZA4SnReA6vzcxAfFysshgaamu1BGlAayuuS01VFgcRUVhj22/qgEkSBQ2ZTH/D2BGIioxEc0urR9fhaBJ1JsZYjpemxiM1OUFZDC1NTVqJXWZNDW7OzFQWBxFR2OPisdQBkyQKKgOKC9C/uADllTUeXT7BdB7RFs9Gnih8RJqq8PxEA/Iy1Y3cmFpbsWrRIkSdO4dZ2dnK4iAiCnsygiQjSUQumCRRUImIiNDagdttdo8Wl5UmnhxNIlcR5np8ZawV/fIzlMVgsViwdskStJaU4M68PKUtx4mIwh7bfpMbTJIo6IwcOhDFhbm4WOFZ97pE41lEWep9HhcFPp2lGU8ObsCIfjnKYpC1vjYuW4bKffvwQGEhIvjBTESkDtt+UyeYJFFQLi578w1jYTZbYbZ4NpqU0XTAL7FRALMacX/RJdw4XF1JhbSv37pmDU5v346Hi4oQyQSJiEgttv2mTjBJoqA0dvggFBXk4GKFZ93rEo1l7HQXzmwW3JZ1Frde11dpgvTJRx/h4MaNeLSoCDEKF60lIqLL2LCBOsFPaQpKMTHRmHLDWBhNJlis1m4vL8frMxv3+SU2CjB2G25KPIW7Jw5QGsbBTz7BzpUr8XBBARIi1C1aS0RElxUXAykpqqOgAMUkiYLW2BGDUZibhfJLno0QxZvLEWcq93lcFEAcDoyJPI7Hpw1U2hyh5MABfPTBB7g/NxepBoOyOIiI6DL5TBg/XnUUFMCYJFHQiouNwc03jENLSyusVptH18ls3KvtOFN4GIhj+OKtA6FXWNpWevw41i5ejLlpachm3TsRUWDo1w9IS1MdBQUwJkkU1K4bNQR5OVkor/RsNCnWUqPNT6LQV2A5jq/P7g+DQV1p24WyMqxcsAC3JCSgT2yssjiIiKjDKNJ116mOggIckyQKavFxsVqnu6bmFlhtno4m7Qccdp/HRupkmE7h5TmFWidEVaoqKrB83jzcEBGBwQkJyuIgIqIOBg7kXCTqFpMkCnrjRw1FblYGKjwcTYqyNSKl5aTP4yI1koxleHVWtlaOqUp9TQ0+fPddDDWbMYYfxEREgUPKr8eNUx0FBQEmSRT0EhPiMPXGcWhqkrlJ3Xe6E+lNB6FzeHZZCh6xxot4ZXoyUhLjlcXQ1NCA5fPnI7e+HpMyMpTFQUREnayLlJSkOgoKAkySKCRMGDschfnZOF9e5dHlI+2tSGs64vO4yH+ijJV4YVIkstOTlcVgbGnBqoULEXvhAmZmZSmLg4iI3JDlFziKRB5ikkQhMzdp+qTxMJnMMJktHl0nvekwDLZmn8dGvhdhqsVXx9tQnKdu5MZiNmPN++/DeuoU7szLU9pynIiI3Bg6FOAcUfIQkyQKGdeNGor+xQU4X37Jo8vrYUNWw16fx0W+pTc34nPDmzG0OEdZDDarFeuXLUPtwYO4r6AAeiZIRESBRdaoGzNGdRQURJgkUciQTma3TB4Pu82BVqPJo+skGc8g1uRZUkWBR2dtxUP9qjF+SIGyGOx2OzavWoWzO3fiocJCGJggEREFnmHDgLg41VFQEGGSRCFl1NCBGDqwGOcvep74ZDd8wpbgwchmxtycC5g+plhZCA6HAzs3bcLRzZvxSFERohUuWktERJ2IjOQoEvUYP9EppMjCobdMvh6GiAht7SRPxFjr2BI82NitmJp8Gnfc0E9pGPt37MCu1avxSEEB4pkgEREFphEjgBh1y0JQcOKnOoWcIQP6YOTQAbhQXqkd6fdERuN+6O1mn8dGXuCw47roE3h06iClzRGO7t+Pj5ctw4O5uUiWWnciIgo8UVHAqFGqo6AgxCSJQo5er8f0yeMRGxuDuoYmj65jcJi0RIkC3xBdCT4/c6DSBOn0sWNYv3gx7srIQKZ8ABMRUWCSBCk6WnUUFISYJFFI6leUr62dVFFZrU2s90Rqy3FEWep8Hhv1XpG1BF+b3R8RstaFIudLS7W1kGYmJaGA5RtERIFLttEjR6qOgoIUkyQKSTLKMGPy9chMS0FFZY1n14EDOfU7ZTa+z+OjnssyncTLc/ogSibgKlJ58SKWz5+PSZGRGBgfrywOIiLywOjRbU0biHqBSRKFrMz0VMy4aQIaGptgtni2wGycpRLJrWziEGiSjWfw6qxcxMSoK5mora7Gh/PmYYTNhpHJycriICIiD8iiscOHq46CghiTJAppE68bqS0we+6C5y3Bsxr2IMLW6tO4yHPxxgt4bUYqkhLVrW/R1NCA5fPmobChATempSmLg4iIPDRxYtsCskS9xCSJQpqMPMyaOlH73dOW4BEOS9vaSaRctPESXrwpGhmpScpiaG1pwcoFC5BUUYFbsrOVxUFERB4qLAT69lUdBQU5JkkU8kYO6Y+xIwbj3EXPW4InGc8iwXjO57FR5wymWjw7wYHCnHRlMZhNJqxZvBiO06dxe26usjiIiMhD0thn8mTVUVAIYJJEYdES/NYpNyA5MR5VNZ53r8uu/wR6u2dzmci79OYGfGFkKwYVqRu5sVosWP/BB2g4cgT3FBRAr7DlOBEReWjMGCBJXfUBhQ4mSRQWCvOyMeWGsaiqqYfVZvPoOpH2FmQ07vN5bNSeztKCR/vXYuygPGUxSNv4zatW4cKuXXiwsBAGJkhERIFPkiPpaEfkBUySKGxMmTgORXnZOH/R8yYOqS0liDFX+TQucmEz4a68i5gyuo+yEKQkc/v69SjZvBkPFxUhigkSEVFwkDI7NmsgL2GSRGEjKSEes6dNhMViQ3OLZ93rZPc4p3474PBs9Imugd2KGalncNuEfkrD2Lt1K/asXYtHiooQp+cmkogoKBQXtzVsIPIS7gFQWBk3cjCuGzUEZ89XeNzEIcZaj4ymgz6PLaw57JgQcwIP3jxQWwhYlcN79mDr8uV4KD8fSTL5l4iIAp+MHk2apDoKCjFMkiisRERE4PZbJiM9LRkXL1V7fL30psMsu/Oh4foSPD1DbYJ08sgRbFiyBPdkZSGDK7QTEQWPcePaFo8l8iImSRR2crLSceuUG9HY1AyjyezRdXRwIK9uK3R2q8/jCzfFthI8M3uAlsCqcu70aaxetAhzkpORFx2tLA4iIuqh1FRg1CjVUVAIYpJEYWnS+FEYPqg/ys6Ve1x2F2VrRFbjHp/HFk5yzCfx4pxiRCqcaFtx/jxWzJ+PKdHR6BcXpywOIiLqZbMGzh8lH+CrisJSdFQkbp8xCfFxsaiurff4eiktxxFvuujT2MJFqrEUr8zKRUx0lLIYaiorsXzePIxxODCM62oQEQWXAQOAPHXLRVBoY59EClv9+xRg2sRxWLr6IyQnJXg0mqF1u6vbhtOZc2HXq9u5D3bxxvN4dWY6EhPUjdw01NVpCVLf5maMz8pSFgeRN/xh40btu7S6ba7l8NxcvH7HHbhtxIgrl9l68iS+9f772H76NCL0eowpKMDK559HbFTn27LfrV+P/169GuX19RhdUIDfPPIIJvTte+X8l+bNw5tbtyI+Oho/vfdePH7DDVfOm79rF97auhVLn33WZ/ebwpi8bm+8UXUUFMKYJFFYmz55PA6VnELZ+Qr075Pv0XUi7a3Irv8EF1PZSac3oo0VeHlKHNJTEpXF0NLcjJULFiCtqgpTc3OVxUHkLQUpKVqSMjArC1JA/M+tW3H373+PPd/+Nobn5WkJ0pxf/xr/edttWqJj0Oux79w56LtolvLuzp14acEC/PGxx3BD37745dq1mP3rX+PY97+PrKQkLN23D//auROrnn8exy9dwufeeguzhw9HRkIC6ltb8a333sOaF1/06+NAYWT8eIAl0uRDLLejsCbldnNn3KQdVa2tb/T4esnGUiS2nvFpbKHIYKrGczfqkZ+VqiwGk9GIVYsWIeLMGczOyVEWB5E33Tl6NG4fORIDs7MxKDsbP77nHiRER2PbqVPa+S/On4/nbrkF35gzR0uaBufk4KHx4xHdRSfHX6xZgy/edBM+O3kyhuXl4Y+PP464qCj8fcsW7fwj5eWYNmgQxhcX49EJE5AUE4PTVW1dQF9buBDPTJ2KorQ0Pz0CFFbS04Fhw1RHQSGOSRKFveGD++HmG8ag/FI1LFbPu9fl1O+Ewdbs09hCid5cjy+NNmFAQaayGKwWC9YtXYqWo0dxd0FBl0fRiYKVzW7Hv3fuRLPZjIn9+uFSQ4NWYpeVmIhJP/sZsl95BVN//nNsPnGi09swW63YVVaGmUOHXjlNr9dj5pAh2Ho58ZLyu0/OnEFtczN2nTmDVosFAzIztdvdXVamJWVEPnHTTWzWQD7HcjsKe7I2z5xpE3G67DxKz5ajf3G+R+v1RDjMyKv9GGXpMwEdN9Zd0Vma8cSgeoweUKQsBpvNhk3Ll6Nizx481qcPIpggUYg5cP48Jv7sZzBaLNoo0uKvfEUbAXKOJn3vgw/w8/vvx5jCQry1bRtmvPEGDr7+ujb61FFVU5OWbGUnti+LzU5KwtHycu13Ka174oYbcP1PfoLYyEj88+mntblJz/zf/+HNp5/W5kj9Zv16rfzuz088oY1gEV0zmWfn5jVL5G3csyMCkBAfh7tmTdU6rfWk212cpQqZjft8GlvQsxpxX2EFJo9QlyBJm/dt69bh5NateKSoCFFMkCgEDc7Oxt5vfxvbv/ENrdTtqTffxOELF2C/vMzBl2++WSudG1tUhDceeki7vLN0rre+d+edOPGjH+HAd7+Le8eOxU+WL9dGnyIjIvCjDz/E5ldfxRcmT8aT//iHl+4lhbWUFGDCBNVRUJhgkkR02eD+fbRGDlU1dTCZLR5fL635COKNF3waW9CyWTAr4yxmje+nNEHavWULDqxfryVIMSzRoBAVZTBgQFYWruvTBz+5916tHO5X69YhNzlZO39YhyYlQ3NyUFZT4/a2ZPRH5mpWNLafq1nR0ICcy7fXkYwwvb1jB354113YUFKCKQMHIjMxUZv7JOV3jUaj1+4rhSHZdk+fDihcV4/CC/cWiFzMvOl6DBvYD2fOXfR4kVkZk8it2wqDrcXn8QUVuw0TE07ivskDlIZxaPdubF++HA8VFCAxIkJpLET+JCNIJqsVxenpyEtJwbGKinbnl1y6hD6dNFaQhOu6oiKsPXLk09uz27H26FFtnlNHsr388ttv4xcPPICEmBitVM9is2nnOX/KaUS9Nm4ckKluTiuFHyZJRC5iYqJx9+wpSEqIR0WV+yOs7hgcJuTVfQw4uBOgcTgw0lCCJ6cP8mh+l68cP3QIm5YswX3Z2Ujj0UcKYf+5eDE2lZSgtKpKm5sk/5fRnMcnTNDeg6/eeit+vW4dFuzahROXLuE777+vjfx8XibAXzbjF7/Ab9evv/L/l2bOxF82b9baiR+5eBHP/OtfWjOIz066evmDv27erI0aSZc9Mbl/f6w7elSbD/XGmjXaKFYK2zVTb8ladmPGqI6Cwgz3Gog6KC7Mw6ypN2LBB2uRnJiA2Jhoj64XZ65ERtMBVCW27SSEs/6OY/jKrEFaNyxVyk6cwJpFi3B7Whpyoj17DomC1aXGRjz55pu4WF+P5NhYjMrPx8rnnsOtl9skvzBzJoxWq9YKvKa5WSvFW/3CC+jvcmT+ZFWV1rDB6eHrr0dlUxNeX7IE5Q0N2uKzK557Tmve0LEE78fLl2PLa69dOU0WnH351lsx97e/1brqSVMHol6RA1xSZsdSafIzncPTmiKiMGK2WPC3d5Zg76ESDOpX6PHOvryZzqZNR0t0+C5Qmmc5gW/M7YPoqM7XX/G18nPnsOTttzElMhJDOnTnIiKiIDJ5MjB8uOooKAwxLSdyIyoyEvfMnorsjDScvXDJ4+tJYVle3ZawnZ+UbirFq3MKlCZI1RUV+HDePIzX65kgEREFs4ICJkikDJMkok7kZmdo85Mcdjtq6xs8vp7BbkJ+7UfQOdomK4eLRONZvHprBuJiY5TF0FBbi+Xz52OQ0Yhx0iqWiIiCU0wMMG2a6igojDFJIurCuJFDMHXidaiorO1RW/BYSzWy63cgXMQYy/HKtESkJiUoi6G5sRErFixAVk0NbsrIUBYHERFdG20eiCRIbPZBCjFJIuqCdIW67ZZJGDGoH0rLLnjcFlyktJ5GSvMxhLpIUxVemGRAToa6kRtTaytWLlqEqHPncCtXYiciCmq6ESOAInULkBMJJklE3ZDudvfNvQWZGak4e6H9OiPdyW7YjVhTz64TTCJMdfjKWCv65qkbubFYLFizZAnMx4/jzrw8pS3HiYjoGqWnAzfcoDoKIiZJRJ7Iz8nEXbdOgc1mR11D+xXou6KDA/l1m2GwNiPU6CxNeGpoI0b0y1EWg81qxcZly1C9bx/uLyxEBBMkIqKg5ZB23zNmAFz4mwIAkyQiD40fPRRTbhyL8opqmHswP0kaORTUboLOYUXIsBrxYJ9K3DCsUFkIdrsdW9auRen27XioqAiRTJCIiIKaThYqZtMdChBMkog8JGVct98yGcNkftLZns1PirHWIidUGjnYLLgt6xxmjOurLAR57Hd99BEObdiAR4uKEMNFBomIglv//sCQIaqjILqCexZEPSDtre+fewsy0lNRdr68R9dNbi1FWtNhBDW7DTclnsLdE/srDePAzp3YuWoVHiksRDzLMoiIgppD1rS7+WbVYRC1wySJqIcKcrNw323ToYMOldW1PbpuZuNeJLaWISg5HBgbdRyPTxuotDnCsf378fGyZbg/NxcpUr9ORERByxERAd2ttwJRUapDIWqHSRJRL4wZPghzpk9EbV0jmppbPb6epBa5dVsQa65EsBmEY/jCzIHQKyxtKz1+HGvfew9z09KQzQ9UIqKgJkXruunTAa5tRwGISRJRL8hIyoybJmDidSO1tuAWi+dNGfSwI79mEyKtnnfJU63Achxfn9MfBoO60rYLZWVYtWABZiYkoCg2VlkcRETkHbpx44B+/VSHQeQWkySiXpKE4d7bpmPogGKcKjuvdVvz+LoOEwprNiDCbkSgyzCdwiu3FSEqMlJZDJXl5Vg+bx4mGAwYlJCgLA4iIvIOR3ExcN11qsMg6hSTJKJrkJgQh4fuuhU5GekoO9+zRWOjbI3Ir/kIOocNgSrJWIZXZ2VrC+qqUlddrSVIQy0WjElOVhYHERF5hy0lpa3Mjks3UABjkkTkhYVm75s7HRERelyq6lkjhzhLJXLrtmlNEQJNnPECXpmejJTEeGUxNDU0YMX8+cirr8ckWYWdiIiCmi0yEhG33QYorE4g8gSTJCIvGDV0IG6bPgn1DY1obGru0XWTjGeQ2bgPgSTKWIkXJkcjO13dyI2xpQWrFi5EfHk5ZmRlKYuDiIi8Q4rSI+bMAaTlN1GAY5JE5CXTJ43H5AljcP5iJYxGU4+um958GCnNxxAIIky1+Nr1dvTJVTdyYzGbsea992A9dQpzc3OVthwnIiLvsE+aBOTmqg6DyCNMkoi82MhB1k8aO3IISs9ehNXqecc7kd2wC0ktp6GS3tyIz41owZA+2cpisFmtWL9sGWoPHcJ9BQXQM0EiIgp6pn79YBgxQnUYRB5jkkTkRdLg4JG7bsXAfkU4eaZnHe+0NZTqtyHBeNanMXb69y0teKR/NcYPzocq8nhtXrUK53bswENFRTAwQSIiCnrGtDRE33KL6jCIeoRJEpGXpSQn4rF7ZyMvOxOnzlyAowdNGXRwIK/2Y8SZyuFXNjPm5l7E1NHFUEUepx0bN+LY5s14uE8fRDNBIiIKeqboaMTccQegcCFyot7gK5bIByRBevTuWUhKiNMWm+0JWWy2oHYTYsxV8Au7FdNSSnHHDWoX9Nu3fTt2r1mDhwsKEM8PUyKioGfV6RB1551ATIzqUIh6jHsiRD4iJXcP3DFD+72iqqZH19U7rNpis9GWOviUw47xMcfxyJSBSpsjHN23D1uXLcODeXlINhiUxUFERN4hNRS6GTOgS0tTHQpRrzBJIvKhcSOH4I6ZN6OpsQV19Y09um6Ew4zCmnWItPbsej0xVFeCz80YpDRBOn3sGNYtXow7MzORyXUziIhCgmX0aET0U1uhQHQtmCQR+ZAkH9MnXYfpk8ejorIaTc2tPbq+wW7UEiWDrcXrsfWxluCrs/sjIiICqpwrLdXWQpqdnIwClmMQEYUEY34+om64QXUYRNeESRKRj+n1etw562ZMun40zl2sQEursUfXj7I1o6h6DQy2ni1S25Us00m8NKcPohSO3Fy6cAEr5s/HpMhI9I+PVxYHERF5T2tKCmJkwViiIMckicgPJBl58I6ZuH70MJw5Vw6Tydyz69uaUFS91iuJUorxDF6dlYuYmGioUltVheXz52OkzYaRycnK4iAiIu9piotD7L33AgorFIi8hUkSkT/XULp7FsYMH4RTZRdgtlh6kSitgcHa+0Qp3nger85IRVJiHFRprK/XEqSixkbcwAm9REQhodFgQNwDDwCcW0ohgkkSkR8lxMfh8XvnYPigfjhVeh5Wq7XnpXc1kig19fhvRxsr8NLNschITYIqLc3NWLFgAZIrKjA9K0tZHERE5D0NOh2i778fes4tpRDCJInIz5KTEvD4fXMwoG8hTpSeh81m63Gi1KdmDSJ7kCgZTDV4dgJQkK1u5MZsMmHN4sXQlZbittxcZXEQEZH3NNrt0M2diyiWTlOIYZJEpEBGWgo+c//t6FOQi5Ol52G323t0/Uhbi1Z650l7cL25AV8c2YpBRdlQxWqxYP3SpWg8cgT3FBRAr7DlOBEReUeTzYbW6dORmJenOhQir2OSRKRITlY6PnP/bcjNzsCpM+fhcMjSe56LtEuitLbLRElnacHjA+owZlA+VJEEcPPKlbiwaxceLCqCgQkSEVHQa7HZcGncOGQNHqw6FCKfYJJEpFBhXjaeuP82pKemaCNKvUuU1iDKUnf1mVYT7s4vx02jiqCK3J/t69ej5OOP8UifPohigkREFPSMNhtOFBej74QJqkMh8hkmSUSK9SvKx5MP3I701GRtRKnHpXf2VvSpXoMYc9WnJ9qtmJlWhjnX94XKBGnPli3Yt3YtHikqQqyemxsiomBntttxMDMTw2fN0hZMJwpV3GshCgDSxOHJB+ciIzWlV4lShMOMopp1iGo+BzjsuDHuBB64eYDSD7Aje/Zg24oVeDA/H0lcM4OIKOhZ7XbsS07GmLvvRgS36xTimCQRBYgBxQX4zIO3IzM9tVeJUnNjPcx7/4XxEQfw1C2DlCZIJ48cwYYlS3BPVhbSuWYGEVHQs0l1QHw8xtx/PwzcrlMYYJJEFED69ynQRpQkUTpZes7jRKml1Yjz5ZWYOmE0np41HHqFpW1nT53C6oULcVtKCvKio5XFQURE3mF3OLA7KgqjHngAkVFRqsMh8gsmSUQBOEfpqQfvQHZWOk56MKJkMplx5lw5bhw3EvfdPh2RBgNUKT93Divmz8eU2Fj0jYtTFgcREXnPnogIDH/oIUTHxqoOhchvmCQRBaC+RXl46oG5yMlM73JEyWKx4nTZBYwdMRgP3TkTMdHqjvDVVFZqCdI4AMMSE5XFQURE3rMXwOCHHkJcfLzqUIj8ikkSUYAqLszDUw/ORU5WBk6UnoPNZmt3vtVm0xKoIQOL8dg9sxEfp+4IX0NdHZbPm4d+LS24LjVVWRxEROS9DqW7bDb0e+ghJCQlqQ6HyO+YJBEFsD4Fufjsw3egMDcbx0+fhdVq1U6XkaVTpefQr08+nrjvNiQnJSiLsaWpCSsXLEB6VRWmZGYqi4OIiLzXpOFji0VLkJJSUlSHQ6QEkySiAFeYl4PPPXIXBvYtwonT52AyW7Tud7nZmXj8vtu0Jg+qmIxGrFq0CIayMszKyVEWBxEReYfFbsc6oxFDHnwQqenpqsMhUoZJElEQyMlKx2cfvhPDB/fD0ROlSEtNxhP334aC3CxlMVksFqx9/320lJTgrvx86LmoIBFRUDPabFjR0qJ1scvIzlYdDpFSOocUnRJRUKhvbMLGrbsxuH8f7VsVmR+1YdkynN22DY/36YNIJkhEREGtyWrF8qYmTH74YeT1Uff5QhQomCQRUY/IJuPj1atxZMMGfKaoCDEK12QiIqJrV2ux4IP6ekx96CEUDRigOhyigMC9GyLqWbejzZuxdc0a3JmfzwSJiCjIVZhMWFRTg0n33ssEicgF93CIyGOHdu3CpuXLEZuQgG16Peo4EE1EFLTOGo1YVFWFm+6+G/2HDlUdDlFAYZJERB45fvAg1i5ZAn1EBNIyM9ECYLXdjktMlIiIgk5JUxOW1NRg+n33YfCoUarDIQo4nJNERN0qO3ECS995B8aWFuQUFkLn0qghAsAkvR6FbN5ARBQU9tXXY5vFgln334++gwerDocoIDFJIqIuXTx7Fkv/7/9QX1ODvOLidgmSk5wyXqfDQM5RIiIKaFuqq3HYYMBtDz6I/OJi1eEQBSyD6gCIKHBVVVRg+fz5qKmqQkHfvm4TJCFHWnY6HGi12zGKiRIRUcCRY+JrLl3CxaQk3P3QQ8jMzVUdElFAY5JERG7JyNHyefNQce4cCvv1g96D5Oegw4EWux0TdDouLktEFCDsDgc+uHABLbm5uPPBB5Ganq46JKKAxySJiK5iMhq1EaSzp06hQBKkCJl55JlTkig5HJis1yOaiRIRkVJGhwOLy8oQ1b8/7njgASQkJakOiSgosC6GiK5itVjQUFurjR71ZkSoHMAKux01nPJIRKRMtc2Gv508icThw3H7I48wQSLqATZuICK3LpSVYcWCBag4exb5ffvCEBnZ49uQ8ScpvevLeUpERH513GTC+6WlGDl5Mm654w5ERkWpDokoqDBJIqJOVV+6hBXz5+PM8eNaZ7uo6Ohe3c5gnQ5jOU+JiMgv84+2NDVhS3k5bpg+HTfNmoUIA2dXEPUUkyQi6lJjfT1WLVyIYwcOILugALFxcb26nSwAN+n1iGGiRETks/lHy6qqcLqxETfNno0JU6d61HSHiK7GJImIutXa0oK177+PAzt3Ij07u9d17ZJe3azXI52JEhGRV1U7HFh4/rzWYXT6nXdi1IQJnS7bQETdY5JERB6xWCzYuGwZdm3ejMTUVKSkpfXqduSY5vU6Hfrz6CYRkVectNu1+UdRsbGYec89GDJ6tOqQiIIekyQi8pjNZsP29euxZfVqGKKitMUIe3ukcqBOh3E6HSJ4pJOIqFdsDgc+sVqx/vRppKalYdb996Pv4MGqwyIKCUySiKhHZJNx8JNPsPHDD9HU2Ij84mJE9GAdJVeZl+cpxTJRIiLqkVaHA2tbW7H/zBltO3zrPfcgr08f1WERhQwmSUTUK+dKS7HmvfdwvrQUeUVFiI6N7dXtxF6ep5TBRImIyCOVDgc+rK3FhUuXMHTMGNxy111ISklRHRZRSGGSRES9JgvOrlu6FEf27kVqRgaSUlN7dTsyO+k6nQ4DOU+JiKhLJXY7Pjx7FlabTeteN3HmTET2Yh07IuoakyQiuiYWsxlb1qzBzk2btLU4svLyej1Pqb9Oh/Gcp0RE5Hb+0XarFZtOndJGjabNnYuhY8eygx2RjzBJIqJrJpuRw3v2YMOyZdq6Svl9+vR68UIZi5qk1yOZH/xERJp6hwMbWlpw8MwZFPbrh1vvvRe5hYWqwyIKaUySiMhrLpSVYc3ixTh3+jRyiooQ08t5SlJ0N0anw2CdjkdJiShsyS7aMYcDG6urUVNdjRHXXaetgdTbteqIyHNMkojIq2Qkaf3SpTi0ezdS0tOR3Mv1lEQWgBv1eiQwUSKiMNPkcGCb3Y59Z89CtoA3TJ+OG6ZNg4Hzj4j8gkkSEflk4dnt69Zh+4YN0On1yM7P7/WIkOFyUwcuPktE4bQ47A6zGaWlpVpTnGl33IHBI0dyZJ3Ij5gkEZFPyKbl6L59WL9sGRpqapBXXAxDL+cpiXwAE7imEhGF+NpHO+x2nGhuRvm5cygeOBAz77lHO9BERP7FJImIfEo+6GU9pTMnTyKnoACxcXG9vq1oANfr9ShiokREIabM4cBOux3lVVVorKvDiPHjMf2OOxCfmKg6NKKwxCSJiHyuqaEBGz74AAd37dLWUpK5Stei+HKr8CgmS0QU5MwOBz5x/P/27gM2yvv+4/j3bLxtvBdmz2AghO38oYQQ0qSkapWoI0rTpFXVoSgd6d5TXepUW1Wt2qpDrdRW3TtVwshgpMwGTABjsA0GbDzB++znr8+PO/fBGGLw2Xe23y/p0d09Pu4en+3j+dz39/v+PKvs7bXzp09bXFyc3X7XXbZq/fqb7hIKYPgISQBGRbCnx17Yvt12bdlifZ7nho/Ex8ff9OOlhJo6FBOUAIxRZ0PNGVq6ulx30LyCAlc9mrtoEfOPgCgjJAEYNXq7qTh82J7517/cJ6YFJSWWmp4+rMecFwjYskDAJnFCAWCMCHqe7fc8O9bXZ02h4XVzS0tdg4b8oqJoHx4AQhKAaGhtbrbnnnzSDu3ZYwlJSZZfXOyGmNysjFBVKZ+gBCDG1YeqR43d3Xa2utp9ULR6wwZbsXatJSQmRvvwAIQQkgBERV9fn5Xv32/P//vf1lBX55o6JA+jqYPi0cJAwJYEAhZPWAIQY3o9z170PDviedbc2OgqSOped8fmzVYyc2a0Dw/AAIQkAFHVWF/vht+pXbg63+UVFQ1rLH6mma3U2kwEJQAx4ryaM/T1WUMwaOeqq10FXZUjLQ6blKIZlgBiDSEJQNT1BoP23//8x3Y89ZS1NjVZ0bRplpScPKzHnB6aq5RGWAIQJW2eZwc8z6o8z807ajh/3qbOmmXrN2+2GXPn0pwBiGGEJAAxo6621rb/85+uuUNGZqZl5+cP6yRCvfNKAwE3DI/GDgBGc2idhtUd9jzrDrX21nvZbWVlrr33cBvWABh5hCQAMaWnu9v27dhhu7dutfZLl6x4+vRhT2ZOM7PlcXE2jaAEYITVeJ7t6+uzNlWSLl60urNnraikxNa/6lWugx3VI2BsICQBiEm1VVWuqnTy6FHLysuzrJycYT+mGuuuiIuzTE5SAERYi+fZ3r4+OxdqTFN35oz19vba4pUrbd0rX+mq4wDGDkISgJjV1dlp/9m+3fY8+6x1d3W5uUqTEhKG9ZiKR/NDXfASCUsAhqnb8+yQ59lRzzOdUHW0tdn5M2dcExqFo1uWLh3WEgcAooOQBCDmVR0/7qpKp0+etJyCApuclTXsx1RbiKWBgM0OBBj+AuCG6fSp0vPsoOdZZ+h2/dmz1tXRYaXLltm6e++17NzcaB8mgJtESAIwJrS3tdmup5+2/Tt3uiEshSUlEVl4MTc0BC+PoARgiC6EhtY1hG5r/qQaz2ho8NpNm9wQu/h4tY4BMFYRkgCMrU9uX3rJdj79tNVUVlpaRoarLEViKMssdZ4KBCyFsATgGjpClSNVkKSnp8fNPZIFS5bY/919t+UVFkb5KAFEAiEJwJicq3Rozx574ZlnrKmuznKLiiIyKXqSmZurpDlL8YQlAL6W3sc8z170PAuGGjM01tXZpdZWt+7R7Rs32pzSUuYeAeMIIQnAmNXU0GAvbN9u5Xv3usYOBSUlw16ENtwyfFEg4KpLhCVg4urzPDsZWu/oUmjfxZYWtyhsVm6urXrFK+zW1astKSUlykcKINIISQDGNL2F1Zw4Ybu2bnVD8RKTky2/uDgi8wEIS8DErRyFw5HWOxJ9EHPu9GlLTEy0RStW2Oo77rDsvLwoHymAkUJIAjAuBHt6rHz/fnth27b+CdSZOTkR6VyXGgpL6oRHWALGdzjSfKNyXzjq6+21+nPnXNe6mfPnu6F10+fOpSsmMM4RkgCMK5ojsPe55+zArl2u45SG4KWkKuYMnx6lNBCwOYQlYNyFoxOhcNQe2qfTo9amJmu6cMHyi4ps9YYNVrp8uSUMc602AGMDIQnAuHS2psYNwTt+6JDFxcdbQXHxsBeiDUvxhaVJhCVgzIcjDavr8O3vbG+387W1lpqWZkvLymzF2rURaQ4DYOwgJAEYt7Se0vHDh23Xli1WW1VlGVlZlpOfH7FhMgpLCwMBm0tYAsaUoK9y5A9HwWDQtfTWELu5ixdb2Z132pTp06N4pACihZAEYNzTp8IHdu+2vc8+ay1NTZZXVGTpkydH7PGTQ5UlwhIQ++GoIhSOOn37dSrUVF9vF5ubrXj6dCvbuNHmLV7MgrDABEZIAjBhqG3v7m3b7MiBA9bT3e3CUmp6ekTDkipL8whLQEzp8Tw77nn20iDhSB+cNF+4YJOzs23lunVueF2k5jECGLsISQAmFL3lVVVU2L4dO+zkSy+NSFhKMrNbQpWlJMISEDXdvnDUNTAcNTZac0ODpWdm2qJly+y2sjLLLSyM4tECiCWEJAATUl9fn1tfad/OnVZ55IgLS7lFRZYWwbAUZ2YzQpWlPMISMGqaPc+OhdY66r1GOFIjhkXLl9uta9ZYHuEIwACEJAATWngxWoWlE+Xll8NSYaGlZWRE9HmyzVxYUmhKIDABEdfneXbazI719VndIH/nCkYKSJOzsmzxihW2ZPVqyy0oiNLRAoh1hCQACIelyko7sHOnVZSXW1dXlzuBimSDB5lk5hal1VC8LMISMGydoWYMFb41jvwVY1c5amy0TIWjVavs1lWrXJdLALgeQhIA+Ogt8fTJk7Z/xw6rOHLEujo7XVhSZSlSrcPD8kPVpWksTgvcsPpQMKryPOsbJBypcqTFYNWQQcFoyapVlp2XF6WjBTDWEJIAYBB6azxz6pSrLGmtpc4RDEtq9DAnVF1KJywB19QVmmekNY5aBvm6C0cXLrhwlJmba7euXm2LV6607NzcKBwtgLGMkAQALxeWqqouh6VDh6yzo8NyQsPwIh2WZIqqS3FxVqzGDwQmwDkfqhrVDFI1CoejpgsX7GJTk2X5wpGuA8DNICQBwBDorfJsdbXt37nTjikstbdbdn6+65A1EmEpzcxVllRhSiYsYYLONaoMVY0uXuM+vb29rnJ0saXFBaKla9a4pgyZOTmjfLQAxhtCEgDcaFiqqbGDu3a5YXiXWlstNSPDzXVISEiI+PMpHqk58fRAwKYSmDABhtOdDlWMzurv7Vr36+y0xro66+7quhyOysps8fLlbv4RAEQCIQkAblLD+fMuKB3et88unDvnKkpZeXkjMm9J9IgFocCkZg8EJoyXilE4GJ27TjDS6cqllhY3rC4uPt6mTJ/uhtTNW7Qo4i37AYCQBADDpE+1Tx47ZuX79llVRYV1tLW5YXj6hDt+kpp+24gFJoUlbSkEJowhHaFgVO15bk2j652I9AaDLhipaqswNGfhQitdtsxmzJ07Yn9fAEBIAoAI0dvp+TNn3JylI/v3W2N9vU1KSHBrsqSkaZbRyCnwDclLJTAhBrUPCEYvRx826G9IIUl/Q6XLl9stS5e6LpMjUakFAD9CEgCMgPa2Nqs8csQO7d3rWolr7oTmS2RmZ7uhQiMp3zckj8CEaGoLDaPTVj+E+6sRQ4vWN2putqTkZJsyY4YtWbnS5pSWWkpq6igcMQBcRkgCgBGk1sS1VVX20sGDdvS//3Unf4nJye6TcZ0EjrQ8X2BKIzBhFFzyBaMLQ7i/TkNUNdKQumBPjxumqorRvMWL3byjuLi4UThqALgSIQkARonaFFccPmwv7tnjhuVpGJEWvNT8pdE4EcxSp7xAwG0anpdIaEIEdIeqRFrLSFvTEP9dMBh0VSP9XSSnptrUmTPdXKPZt9xiqenpI3zUAHB9hCQAGGUKR9UnTrjqUkV5uZuQrrlLGo6niemj9cl59oDQlEBowhD0DBKKhnoioeF0F5ub3abTD7XOD1eNiqZOpWoEIGYQkgAgijTESB3x1Epcw/LaL11yHbs0dylt8uRRO2lUPNLymwWh0KR5TYQm+ENRXSgUNd5AKAp/KKBhpqoYmee5yunM+fNt1oIFbmOuEYBYREgCgBigt2IFpprKSjck70xVlbVdvOgqTBlZWZY+ioFJFI9yfZUmzW2aRGiaMKFIc4kUiBSMGm4wFIWH0oUrRhYI2OTMzP5QNHXWLPf7DACxjJAEADFGb8vNjY1WXVFhJ44csdMnT7rApAqThuSNdmCSuAGhSdcJTeNDcECl6GZCkXucnh5XMdKCr2rRrd/V2QsWuKrRtNmzmWcEYEwhJAFADNNbdIsC04kT/YFJc5hcYApXmEa4pfi1qBaQHQi4uU3hy2SCU0z/Ll0ys2ZtCuLa1FBkGI/Zo2DU1OR+J+MCAcvMybHZCxfarHnzbOrs2QylAzBmEZIAYAxRYHJD8srL3aU7OY2Pd5/aZ0QxMIXplLg/NOmk2cxUP9AJNEZPVygANQ+47I3AY/d0d7tgpOpmIC7OsnJybM7Cha5ipA516lQHAGMdIQkAxiidqFZXVroKU82JEy4wiU5S1fRBn+LHQrewuFDVKUtDsMwsMxBw7cjTCE/D1ut51jpIGOqI5HP09rqGIgpFXR0dLohn5+ba3NJSm6GK0cyZlpSSEsFnBIDoIyQBwDiguSBnTp2yszU1bi6T5jRpgU7NDUlJS3OtxXWp27EiHJ4yVIEKBFwVKmXA9fgYOt5ohSAFnnYFH8+7fBm63RIKSJH+T3xgKNLvTGpGhuUWFNjMefPcAq8lM2daYlJShJ8ZAGIHIQkAxpm+vj5rqq93C9aePX3aqo4ft5amJutsb798wpue7kKTKk6xFJoGkxQOTroMhaeB18fqorhdgwSggWGoaxSOo0+hqK3NVSL7Q1F6ugtFqhQVlpRY4ZQprstirP++jFef/exn7U9/+pMdOHBgxB/nLW95izU3N7v7ARPZpGgfAAAgsjTELrew0G2ly5e7ykBjODRVV7smEBqqV1db218lSM/IcEOmYu0kuCu0aQiZ1tjp57uuWVjhypPCk/5jmxTa79/69wUCV+8b5D4DO8BpPs/ALei/7bvPFftDW8+AABSJ+UHDCUWqFPmDc0FxsasUKRQVTJni5rnF2u/DcCkA/PznP7cvf/nL9tGPfrR/vwLB/fff75pbDNXMmTPtfe97n9uu5+DBg/apT33Kdu3aZa2trVZUVGRr1qyx7373u1ZQoGWcX94HP/hBe/e7333F90GQAUYWIQkAxrn4+HjLLypy2+IVK9zing11dS401YaH5zU0WFdnpwtY4UpTLIamwfSGOrS5Lm1DOckd4olwuAVGtMJMpCgkd4RDkSpFCpNpaZZXWOgqRUWhUKTOdGPh5z1cycnJ9tWvftXe+c53Wna22oyMnPr6ervrrrvs1a9+tT355JOWlZVlp06dsr/85S/W1tY25MdJT093G4DRE/0ZvQCAUaX24TopXrJqld3zwAP21ieesIcee8zue/BBW1pWZskpKW5OU01FhQtQmuvUWFfn5qmoCjFRhCtAY4WqIAq6GlqpAKyKYfXx41Z76pQLSRo+t3bTJrv/0Uftkfe+1x5+/HFbf++9Nn/JEsvKzZ0QAUk2bdrkqjmqJl3P73//e1u0aJElJSW5qtE3vvGN/q9t2LDBqqqq7IknnnCv27Veu+eff95aWlrsxz/+sS1btsxmzZpld955p33rW99y1+VnP/uZC09+qhD5H1PD5G677bb+66qG/fnPf+5/7m3btrmvfeQjH7H58+dbamqqzZ4921Ww1KZ9oB/+8Ic2bdo0d783vOEN7hivN3xXr5WONyUlxZYuXWq/+93vrvvaAeMBlSQAmOAmJSRY0dSpbrt19Wp3UqVQ1NTQcHluU22t1Z054+asXDh3zp2Mq8OZuuep2qRQpceYKCfZsUALt6oqpDlEutRtUTMF/TxUJdLPM6egwLXozsnPn1BB6OUqq1/60pfsoYcesve85z02derUq+6zd+9eFx4USN74xjfajh077LHHHrPc3Fw31O0Pf/iDCwvveMc77O1vf/s1n0thLBgM2h//+Ed73eteF5HXX0Pvjhw54obu/fSnP3X7cnJy3GVGRoYLXVOmTLEXX3zRHZv2ffjDH+7/9xUVFfbb3/7W/vrXv7rHeNvb3ua+t1/96leDPp8C0i9/+Uv7wQ9+YPPmzbNnnnnGHn74YcvPz7c77rhj2N8PEKsISQCAKyQkJFyerF9S0r9PQ/RUXWq6cMFt9WfPuk56bZcuuflN4ZN0hSUFp6TkZMJTBOhTfFWHNHdIgai7q8uFVJ3oq/GGhkZqyJzmE2m4nIKQNr32vO7XpvlHqsx85jOfsZ/85CdXff2b3/ymGyanSoyoOlNeXm5f+9rXXEhSKNHPQAFEQehaysrK7OMf/7gLZO9617ts9erVtnHjRnvkkUessLDwpo5dw+5U0enq6rrquT/5yU/2X1f1S4Hq17/+9RUhqbOz037xi19YSejvW3Oj7rvvPlcpG/h4eg4Fyqeeespuv/12t08Vqueee85VowhJGM8ISQCAIQ3R03Atbf4TeM1z0QK3GuKly/pz51yAUmOAcHjSqboWHZ2UmOgCWEJiogtP4ctYWMspmvOF9BppgdbwpbseDLowpNcuHDjVdlvBNTsvz61TpFCkjnMT+fUbDs1LUmBRkBhIlZrXvva1V+xbu3atffvb33Y/MwWkofriF79o73//+23Lli22e/duV5FR8FBFZsmSJRZJv/nNb+w73/mOndC6aZcuud+jyZPVaP9/pquFu+8DEIUf/S0fPXr0qpCkqlN7e7vdfffdV+zv7u52wweB8YyQBAC4KTo5z8jMdNvU0PyKwcKTwtLF1lZrDd1WRUTzm8KBwHwVDxeeBgQp3dbwvrFUGVHA0QlqUN/jgBCkk2x9J27YosJj+PtMTHRD49RVbnJWlrsMV4a0X19H5Kxfv97uuece+9jHPuaqQyNJw/Re//rXu00BSQHj61//uptbpN+BgV31BptH9HJ27txpb3rTm+xzn/uc+74yMzNdFck/l+pGKWjJ3//+9yuClWiuFjCeEZIAAKMSnkQngwoKCkmqNnX4Np2QKUhpYVzNf+ru7LS21lZ3wqiGEQpJrrqiyepxce55+i8DAXd5xb4hfk2hzuvr+9+l5129T5cvs19bwPPMU5jzPBd+wgFIJ5R5BQWWqQCUne26y4W3VN+l7o/R85WvfMUNu1uwYMEV+xcuXOiaLvjptobdhatIiYmJLvDeKP27OXPm9He309yeixcvuttpaWlu38uthzTYc2ve1IwZM+wTn/hE/z41lxiourraamtr3bwlUWty/S0MfA2ktLTU/e7q3zC0DhMNIQkAMGoUStRcQJsqJNeiOVAd7e0uPPkDlS5ViVJw0vwc3U+XrkrT0+MqN26omkJLMOjWLgoHmIHhRmFI18NVqquCVGifP1ipqtU/VHDSpP4KULjypaCjeULh4BPeNFyOYXGxR8PdVH3REDW/D3zgA7Zq1Sr7whe+4Bo3qErzve99z77//e9fMedHQ+YefPDByyE4L++qx//b3/7mqjm6jwKWArUaJvzjH//ob7qgNZPUZU5zl9RIQkPy1HzhevTcaimuIXKqUqlqpKYKCjN6Ph27qj9qGDFYC/RHH33UVbLUuEHPqSYVg82t0pwrDUdUFz/9zaxbt851wlNg1DA+PQ4wXhGSAAAxOQcqffJkt90onczpU3aFJAUmXVeYCs//UVXK7Q8G3X31XAo8Cku6VKVA+/q30P6xNuQPQ/P5z3/ezeXxW758uesA9+lPf9oFpeLiYnc//7A83dZaS6oKqcHBYAvRqhKjAKTQVVNT48KUwoxagr/5zW9291ETCHWP+9CHPmQ/+tGPXMMIddVT57xrUdc6tf1euXKlq8Bu3brVXvOa17gw8/jjj7vjUTMGNZ7QY/nNnTvXHnjgAdu8ebM1Nja6NZz84W8gff+qdqnLXWVlpWtXrtdHoQ4YzwLejSwvDQAAAADjHLV/AAAAAPAhJAEAAACADyEJAAAAAHwISQAAAADgQ0gCAAAAAB9CEgAAAAD4EJIAAAAAwIeQBAAAAAA+hCQAAAAA8CEkAQAAAIAPIQkAAAAAfAhJAAAAAOBDSAIAAAAAH0ISAAAAAPgQkgAAAADAh5AEAAAAAD6EJAAAAADwISQBAAAAgA8hCQAAAAB8CEkAAAAA4ENIAgAAAAAfQhIAAAAA+BCSAAAAAMCHkAQAAAAAPoQkAAAAAPAhJAEAAACADyEJAAAAAHwISQAAAABg//P/g01xPxhGOS4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== SUMMARY ===\n", "Total JD records: 841\n", "Total CR records: 690\n", "Total similarity pairs: 1480\n", "Match rate: 0.00%\n", "Neutral rate: 0.00%\n", "Mismatch rate: 0.00%\n"]}], "source": ["\n", "# <PERSON><PERSON><PERSON> tra phân bố nhãn\n", "class_counts = df_similarity['suitability'].value_counts()\n", "print(\"\\nPhân bố nhãn Suitability:\")\n", "print(class_counts)\n", "print(f\"\\nTổng số cặp JD-CR: {len(df_similarity)}\")\n", "\n", "# Th<PERSON>ng kê điểm similarity\n", "print(f\"\\nThống kê Primary Skills Similarity:\")\n", "print(f\"Mean: {df_similarity['primary_skills_sim'].mean():.4f}\")\n", "print(f\"Median: {df_similarity['primary_skills_sim'].median():.4f}\")\n", "print(f\"Min: {df_similarity['primary_skills_sim'].min():.4f}\")\n", "print(f\"Max: {df_similarity['primary_skills_sim'].max():.4f}\")\n", "\n", "# Tạo explode động cho biểu đồ\n", "labels = class_counts.index\n", "sizes = class_counts.values\n", "colors = ['#66b3ff', '#ff9999', '#99ff99'][:len(labels)]\n", "explode = [0.1 if i == 0 else 0 for i in range(len(labels))]\n", "\n", "# Vẽ biểu đồ tròn\n", "plt.figure(figsize=(10, 8))\n", "plt.pie(sizes, explode=explode, labels=labels, colors=colors, \n", "        autopct='%1.1f%%', shadow=True, startangle=90)\n", "plt.title('Class Distribution for Suitability (Based on Primary Skills Only)', fontsize=14)\n", "plt.axis('equal')\n", "plt.savefig('class_distribution_suitability.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\n=== SUMMARY ===\")\n", "print(f\"Total JD records: {len(df_jd)}\")\n", "print(f\"Total CR records: {len(df_cr)}\")\n", "print(f\"Total similarity pairs: {len(df_similarity)}\")\n", "print(f\"Match rate: {(class_counts.get('Match', 0) / len(df_similarity) * 100):.2f}%\")\n", "print(f\"Neutral rate: {(class_counts.get('Neutral', 0) / len(df_similarity) * 100):.2f}%\")\n", "print(f\"Mismatch rate: {(class_counts.get('Mismatch', 0) / len(df_similarity) * 100):.2f}%\")"]}, {"cell_type": "code", "execution_count": 48, "id": "726f047a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["        jd_id         cr_category  primary_skills_sim  secondary_skills_sim  \\\n", "629    JOB_54            Advocate            1.000000              0.000000   \n", "639    JOB_54       SAP Developer            1.000000              0.000000   \n", "636    JOB_54      Civil Engineer            1.000000              0.000000   \n", "630    JOB_54            Advocate            1.000000              0.000000   \n", "631    JOB_54            Advocate            1.000000              0.000000   \n", "...       ...                 ...                 ...                   ...   \n", "1459  JOB_934     DevOps Engineer            0.038462              0.222222   \n", "1267  JOB_555        Data Science            0.037037              0.000000   \n", "1422  JOB_934        Data Science            0.035714              0.000000   \n", "1461  JOB_934     DevOps Engineer            0.034483              0.000000   \n", "1452  JOB_934  Operations Manager            0.031250              0.000000   \n", "\n", "      total_similarity          suitability  \n", "629           1.000000  Moderately Suitable  \n", "639           1.000000  Moderately Suitable  \n", "636           1.000000  Moderately Suitable  \n", "630           1.000000  Moderately Suitable  \n", "631           1.000000  Moderately Suitable  \n", "...                ...                  ...  \n", "1459          0.839631  Moderately Suitable  \n", "1267          2.121783        Most Suitable  \n", "1422          0.764528  Moderately Suitable  \n", "1461          0.714483  Moderately Suitable  \n", "1452          1.715461        Most Suitable  \n", "\n", "[1480 rows x 6 columns]\n"]}], "source": ["# Lấy 20 dòng đầu tiên có điểm primary_skills_sim cao nhất\n", "top_20_similarities = df_similarity.sort_values(by='primary_skills_sim', ascending=False).head(10000)\n", "\n", "# In ra c<PERSON>c c<PERSON>t cần thiết\n", "print(top_20_similarities[['jd_id', 'cr_category', 'primary_skills_sim','secondary_skills_sim', 'total_similarity', 'suitability']])\n"]}, {"cell_type": "code", "execution_count": 49, "id": "4b0ff503", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jd_id</th>\n", "      <th>cr_id</th>\n", "      <th>cr_category</th>\n", "      <th>jd_name</th>\n", "      <th>primary_skills_sim</th>\n", "      <th>secondary_skills_sim</th>\n", "      <th>adjectives_sim</th>\n", "      <th>adj_weight</th>\n", "      <th>total_similarity</th>\n", "      <th>suitability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>JOB_0</td>\n", "      <td>CANDIDATE_0</td>\n", "      <td>Data Science</td>\n", "      <td>MLops Engineer</td>\n", "      <td>0.148148</td>\n", "      <td>0.285714</td>\n", "      <td>0.020408</td>\n", "      <td>43</td>\n", "      <td>1.311413</td>\n", "      <td>Most Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>JOB_0</td>\n", "      <td>CANDIDATE_1</td>\n", "      <td>Data Science</td>\n", "      <td>MLops Engineer</td>\n", "      <td>0.166667</td>\n", "      <td>0.250000</td>\n", "      <td>0.000000</td>\n", "      <td>7</td>\n", "      <td>0.416667</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>JOB_0</td>\n", "      <td>CANDIDATE_2</td>\n", "      <td>Data Science</td>\n", "      <td>MLops Engineer</td>\n", "      <td>0.176471</td>\n", "      <td>0.222222</td>\n", "      <td>0.000000</td>\n", "      <td>16</td>\n", "      <td>0.398693</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>JOB_0</td>\n", "      <td>CANDIDATE_3</td>\n", "      <td>Data Science</td>\n", "      <td>MLops Engineer</td>\n", "      <td>0.055556</td>\n", "      <td>0.166667</td>\n", "      <td>0.000000</td>\n", "      <td>43</td>\n", "      <td>0.222222</td>\n", "      <td>Not Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>JOB_0</td>\n", "      <td>CANDIDATE_4</td>\n", "      <td>Data Science</td>\n", "      <td>MLops Engineer</td>\n", "      <td>0.200000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1</td>\n", "      <td>0.200000</td>\n", "      <td>Not Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1475</th>\n", "      <td>JOB_934</td>\n", "      <td>CANDIDATE_746</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Senior AI Developer (Python)</td>\n", "      <td>0.083333</td>\n", "      <td>0.200000</td>\n", "      <td>0.037736</td>\n", "      <td>38</td>\n", "      <td>1.717296</td>\n", "      <td>Most Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1476</th>\n", "      <td>JOB_934</td>\n", "      <td>CANDIDATE_747</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Senior AI Developer (Python)</td>\n", "      <td>0.208333</td>\n", "      <td>0.200000</td>\n", "      <td>0.034483</td>\n", "      <td>13</td>\n", "      <td>0.856609</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1477</th>\n", "      <td>JOB_934</td>\n", "      <td>CANDIDATE_748</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Senior AI Developer (Python)</td>\n", "      <td>0.136364</td>\n", "      <td>0.000000</td>\n", "      <td>0.020833</td>\n", "      <td>32</td>\n", "      <td>0.803030</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1478</th>\n", "      <td>JOB_934</td>\n", "      <td>CANDIDATE_787</td>\n", "      <td>ETL Developer</td>\n", "      <td>Senior AI Developer (Python)</td>\n", "      <td>0.136364</td>\n", "      <td>0.000000</td>\n", "      <td>0.019608</td>\n", "      <td>35</td>\n", "      <td>0.822638</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1479</th>\n", "      <td>JOB_934</td>\n", "      <td>CANDIDATE_824</td>\n", "      <td>DotNet Developer</td>\n", "      <td>Senior AI Developer (Python)</td>\n", "      <td>0.095238</td>\n", "      <td>0.000000</td>\n", "      <td>0.047619</td>\n", "      <td>5</td>\n", "      <td>0.333333</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1480 rows × 10 columns</p>\n", "</div>"], "text/plain": ["        jd_id          cr_id       cr_category                       jd_name  \\\n", "0       JOB_0    CANDIDATE_0      Data Science                MLops Engineer   \n", "1       JOB_0    CANDIDATE_1      Data Science                MLops Engineer   \n", "2       JOB_0    CANDIDATE_2      Data Science                MLops Engineer   \n", "3       JOB_0    CANDIDATE_3      Data Science                MLops Engineer   \n", "4       JOB_0    CANDIDATE_4      Data Science                MLops Engineer   \n", "...       ...            ...               ...                           ...   \n", "1475  JOB_934  CANDIDATE_746            <PERSON><PERSON>  Senior AI Developer (Python)   \n", "1476  JOB_934  CANDIDATE_747            <PERSON><PERSON>  Senior AI Developer (Python)   \n", "1477  JOB_934  CANDIDATE_748            <PERSON><PERSON>  Senior AI Developer (Python)   \n", "1478  JOB_934  CANDIDATE_787     ETL Developer  Senior AI Developer (Python)   \n", "1479  JOB_934  CANDIDATE_824  DotNet Developer  Senior AI Developer (Python)   \n", "\n", "      primary_skills_sim  secondary_skills_sim  adjectives_sim  adj_weight  \\\n", "0               0.148148              0.285714        0.020408          43   \n", "1               0.166667              0.250000        0.000000           7   \n", "2               0.176471              0.222222        0.000000          16   \n", "3               0.055556              0.166667        0.000000          43   \n", "4               0.200000              0.000000        0.000000           1   \n", "...                  ...                   ...             ...         ...   \n", "1475            0.083333              0.200000        0.037736          38   \n", "1476            0.208333              0.200000        0.034483          13   \n", "1477            0.136364              0.000000        0.020833          32   \n", "1478            0.136364              0.000000        0.019608          35   \n", "1479            0.095238              0.000000        0.047619           5   \n", "\n", "      total_similarity          suitability  \n", "0             1.311413        Most Suitable  \n", "1             0.416667  Moderately Suitable  \n", "2             0.398693  Moderately Suitable  \n", "3             0.222222         Not Suitable  \n", "4             0.200000         Not Suitable  \n", "...                ...                  ...  \n", "1475          1.717296        Most Suitable  \n", "1476          0.856609  Moderately Suitable  \n", "1477          0.803030  Moderately Suitable  \n", "1478          0.822638  Moderately Suitable  \n", "1479          0.333333  Moderately Suitable  \n", "\n", "[1480 rows x 10 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df_similarity)"]}, {"cell_type": "code", "execution_count": 50, "id": "864dfead", "metadata": {}, "outputs": [], "source": ["df_similarity['suitability'] = df_similarity['suitability'].map({\n", "    'Not Suitable': 0,\n", "    'Moderately Suitable': 1,\n", "    'Most Suitable': 2\n", "})\n"]}, {"cell_type": "code", "execution_count": null, "id": "695442b7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 51, "id": "e49f6a80", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["corr = df_similarity[['primary_skills_sim', 'secondary_skills_sim', 'adjectives_sim', 'adj_weight', 'total_similarity','suitability']].corr()\n", "\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(corr, annot=True, cmap='coolwarm', fmt=\".2f\")\n", "plt.title(\"Heatmap tương quan giữa các thuộc t<PERSON>\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 59, "id": "6d04c8f7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Giả sử df_similarity đã có các cột: ['jd_id', 'cr_id', 'adjectives_sim']\n", "jd_ids = ['JOB_0', 'JOB_1']\n", "results = []\n", "\n", "for jd_id in jd_ids:\n", "    subset = df_similarity[df_similarity['jd_id'] == jd_id][['cr_id', 'primary_skills_sim']]\n", "    subset_sorted = subset.sort_values(by='primary_skills_sim')\n", "\n", "    # Lấy: thấ<PERSON> nh<PERSON>t, trung bình, cao nhất\n", "    lowest = subset_sorted.iloc[0]\n", "    median = subset_sorted.iloc[len(subset_sorted) // 2]\n", "    highest = subset_sorted.iloc[-1]\n", "\n", "    top3 = pd.DataFrame([lowest, median, highest])\n", "    top3['jd_id'] = jd_id\n", "    results.append(top3)\n", "\n", "# <PERSON><PERSON><PERSON> h<PERSON> lại\n", "df_plot = pd.concat(results, ignore_index=True)\n", "\n", "# Vẽ biểu đồ cho từng JD\n", "for jd_id in jd_ids:\n", "    df_sub = df_plot[df_plot['jd_id'] == jd_id]\n", "\n", "    plt.figure(figsize=(8, 5))\n", "    plt.bar(df_sub['cr_id'], df_sub['primary_skills_sim'], color=['#d9534f', '#f0ad4e', '#5cb85c'])\n", "    plt.title(f'JD {jd_id} — <PERSON><PERSON><PERSON> Similarity on Adjectives (3 CRs)')\n", "    plt.xlabel('CR ID')\n", "    plt.ylabel('<PERSON><PERSON><PERSON> Similarity')\n", "    for i, v in enumerate(df_sub['primary_skills_sim']):\n", "        plt.text(i, v + 0.01, f\"{v:.2f}\", ha='center')\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 62, "id": "d743ca65", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "jd_ids = ['JOB_0', 'JOB_1']\n", "results = []\n", "\n", "for jd_id in jd_ids:\n", "    # <PERSON><PERSON><PERSON> c<PERSON>c <PERSON> có điểm secondary_skills_sim > 0\n", "    subset = df_similarity[\n", "        (df_similarity['jd_id'] == jd_id) & \n", "        (df_similarity['secondary_skills_sim'] > 0)\n", "    ][['cr_id', 'secondary_skills_sim']]\n", "\n", "    if len(subset) < 3:\n", "        print(f\"⚠️ JD {jd_id} có ít hơn 3 CRs với secondary_skills_sim > 0. Bỏ qua.\")\n", "        continue\n", "\n", "    subset_sorted = subset.sort_values(by='secondary_skills_sim')\n", "\n", "    # Lấy: thấ<PERSON> nh<PERSON>t, trung bình, cao nhất\n", "    lowest = subset_sorted.iloc[0]\n", "    median = subset_sorted.iloc[len(subset_sorted) // 2]\n", "    highest = subset_sorted.iloc[-1]\n", "\n", "    top3 = pd.DataFrame([lowest, median, highest])\n", "    top3['jd_id'] = jd_id\n", "    results.append(top3)\n", "\n", "# <PERSON><PERSON><PERSON> h<PERSON> lại\n", "df_plot = pd.concat(results, ignore_index=True)\n", "\n", "# Vẽ biểu đồ cho từng JD\n", "for jd_id in df_plot['jd_id'].unique():\n", "    df_sub = df_plot[df_plot['jd_id'] == jd_id]\n", "\n", "    plt.figure(figsize=(8, 5))\n", "    plt.bar(df_sub['cr_id'], df_sub['secondary_skills_sim'], color=['#d9534f', '#f0ad4e', '#5cb85c'])\n", "    plt.title(f'JD {jd_id} — <PERSON><PERSON><PERSON> Similarity on Secondary Skills (3 CRs)')\n", "    plt.xlabel('CR ID')\n", "    plt.ylabel('<PERSON><PERSON><PERSON> Similarity')\n", "    for i, v in enumerate(df_sub['secondary_skills_sim']):\n", "        plt.text(i, v + 0.01, f\"{v:.2f}\", ha='center')\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 63, "id": "e4a6ccc9", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAxYAAAHpCAYAAAAf5apCAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAU8NJREFUeJzt3QncTPX////XZV/iIrJmjUi2Ilt90iKSQlpQIfmqVCil8BHRIkTqQ/moVPwSH+VDi5RIKcpeaZEWS9lVhELM//Z83/5nPjNzzbXMda651sf9djtcc+bMzDlnzjnzfr2X10kIBAIBAwAAAAAf8vl5MQAAAAAIgQUAAAAA3wgsAAAAAPhGYAEAAADANwILAAAAAL4RWAAAAADwjcACAAAAgG8EFgAAAAB8I7AAAAAA4BuBBQAAAADfCCwAAADysClTplj16tWtSJEi1rx5c1u1alWKy8+dO9fq1q3rlm/QoIEtXLgw7PmEhISo0/jx4+O8JchqBBYAAAB51Jw5c2zQoEE2cuRIW7dunTVq1MjatWtne/bsibr8ihUrrHv37tanTx9bv369de7c2U0bN24MLrNz586wafr06S6wuOaaazJxy5AVEgKBQCBLPhkAAABZSi0U5513nk2ePNk9PnnypFWpUsX69+9vQ4YMSbJ8165d7fDhw/bWW28F57Vo0cIaN25sU6dOjfoZCjz++OMPW7JkSRy3BNkBLRYAAAB50LFjx2zt2rXWpk2b4Lx8+fK5xytXroz6Gs0PXV7UwpHc8rt377a3337btXAg9yOwAAAAyIP27dtnJ06csPLly4fN1+Ndu3ZFfY3mx7L8yy+/bCVKlLAuXbpk4JojuyKwAAAAQFxofMWNN97oBnoj9yuQ1SsAAACAzFe2bFnLnz+/664USo8rVKgQ9TWan9blly9fbps2bXIDxJE30GIBAACQBxUqVMiaNGkSNqhag7f1uGXLllFfo/mRg7AXL14cdfkXXnjBvb8yTSFvoMUCAAAgj1Kq2V69elnTpk2tWbNmNmnSJJf1qXfv3u75nj17WuXKlW3MmDHu8cCBA61169Y2YcIE69Chg82ePdvWrFlj06ZNC3vfgwcPuvtdaDnkHQQWAAAAeZTSx+7du9dGjBjhBmArbeyiRYuCA7S3bdvmMkV5WrVqZbNmzbLhw4fbsGHDrHbt2jZ//nyrX79+2Psq4NAdDXTPC+Qd3McCAAAAgG+MsQAAAADgG4EFAAAAAN8ILAAAAAD4RmABAAAAwDcCCwAAAAC+EVgAAAAA8I3AAgAAAIBvBBYAAAAAfCOwAAAAAOAbgQUAAAAA3wgsAAAAAPhGYAEAAADANwILAAAAAL4RWABABlq2bJklJCS4/zNK9erV7eabb7aMpHV86KGHgo9feuklN2/Lli0Z+jkXXXSRm5B3j9/MomNX6/7EE0/EvI06v3SepXSO+DVu3DirW7eunTx50nKDRYsW2SmnnGJ79+7N6lVBNkJggTzFKzytWbPGPdaPhh57U7Fixaxq1ap21VVX2YsvvmhHjx5N1/uG+uSTT+zqq6+28uXLW+HChd2P12233Wbbtm1Lsmzk+uTLl88qVqxoV155pX366afp2uZvvvnGLr/8cvcDcOqpp1qPHj2y9IcgrT/+ecGXX35p1157rVWrVs2KFClilStXtssuu8z+9a9/WW61Y8cOd5xv2LDBcqo333zTWrdubeXKlXPXjJo1a9r111/vClqIj48//tjat2/vzhGdK951etasWZYTHDx40MaOHWsPPPCAu6577rnnHjv33HPdtVnH0llnneXOj0OHDsX03qNGjbJGjRq563zRokWtfv367rN0voUGT6G/L/o9OvPMM23EiBH2119/xbxN+l2pVauWjRkzJubXIvcqkNUrAGQHzz77rLsgK5D45Zdf7N1337VbbrnFJk2aZG+99ZZVqVIlXe+rAuLAgQNdwaN///4uSFBB//nnn7c5c+bYwoULrVWrVsmuj2q2tm/fbs8995xdeOGFtmrVKmvcuHGaP//nn392r0tMTLTHHnvM/VipQK8Crd6rUKFC6dou+LdixQq7+OKLXQGpb9++VqFCBfddK4B86qmn3PHi2bRpU1hhJCP8+eefVqBA/H8C3nvvvbDHKuioEKQAO5ZjObvQ+TN48GAXWAwdOtQVBr///nt7//33bfbs2a6whYw1d+5c69q1qztedD0tXbq0/fTTT/bRRx+5a+MNN9wQ0/vpmqjjPzOvf9OnT7e///7bunfvHjZ/9erV9o9//MN69+7tAqb169fb448/7o4nbV9q5/2PP/5obdq0cRVV1113nd16661uu7744gt74YUX7L///a999913weUVTOj3Rw4cOGALFiywhx9+2H744Qd75ZVXYt4uVZLdd9997pwuUaJEzK9H7kNgAZi5WuOyZcsGH6sGRxfZnj17uot1eloL1FJx99132wUXXOBqMlUA8fTr18/OP/9897lfffWV+6FMaX06d+7saqD0AxtLYUzBxOHDh23t2rWuACvNmjVzteJqZdGPEGITCARc7Z5qBf149NFHXcCngkWpUqXCntuzZ0/YYxUGMpoKMfF05MgRd8znpuBVBUMVwnT+RAZM0b43mLv+FC9e3Nd7qAa/Xr167joceTylZ5+rsB7v4z+SWsA7duyY5HPVEhPpjDPOcIV1Vf60aNEixeOxS5cutnv3btetS781kdcYtZKEUmXCTTfdFHx8xx13uMqtV1991SZOnOha1mNxzTXXuEoQ/TapMg6gKxSQjBtvvNH+7//+zz777DNbvHhxzK9XAUTNzS+//HJYUOH9cKi/7c6dO+3f//53qu+l2myJtYb59ddfd92ovKBCVLul5u///Oc/ll38+uuv7oe0QYMGrqWmZMmSrtvD559/nmRZFepV0NA26EdarUD6cVWNm0ctPar11/tpmdNOO83VJId2VdMP/SWXXOK6s6jgroKLWooiqWZd+1CtWE2bNnUBhfedqUVIQZ8KTnofdWtIa/c5re/ZZ5+dJKgQvVdKYyy8rncqlAwYMMBtn95HtYfHjh2z33//3QXFClg13X///S4girX/uGozO3ToYJUqVXL7SMetjusTJ06ELacxFAp8FcCqNljH+7Bhw5KMsVDh57zzznN/q4bW65Kh7Rk5cqQVLFgwajc9BcDavtS6ayxdutTV/ur70PKdOnVyLYTRuhuqlUH7VMspwNP6KBhKyb59+1y3E1UKRBP5velY0Hapu4j2n1o+9V1EO0b+3//7fy7o177Td6b9GBm8PPPMM+6Y0XvpO7nzzjvddx3tu/j6669di5jeT92HdL2JlNbjd/ny5a6CRdcRbzu0rGr9Q2l/6vzVsX3FFVe4GmxdR/1+t3o/HTfRgtTIfR5Jx71Xiz9v3jxf40j++OMPV1mk81H7QZ+tIHPdunUpvk6tK2pB0LU3LbyxHpHfbbTru66R//znP5MEFaLrqIKLlGg/6LXaT2r9CKUWuCZNmrjvUe+l66muq6G0Dxo2bOiuFYAQWAAp0HgEiVY7mRIVUJYsWeIKOTVq1Ii6jJr29eOkrlbRCtoqxKg2Tk3j6iqjArL6caeVunTp9SoMR1IBRu+bXegHbf78+a4Ar1ozdTVRdy11NwntI6wCrZZRs7t+8CZMmOC6RqhJf+PGjcHl+vTp4woAKgCpxm7IkCFu/4W2PCmI0NgGFYD1PlpWtXdTpkxJsn7qiqQuDCpE6IdVrUYqVF166aUu4Ljrrrvcj7sKYCo4poU+WwXx0PWOlWoKN2/e7PaHakOnTZtmDz74oOt7rn2lFisVGsaPH28zZ86M+f1V4FdBcdCgQW67tc/Vmqf9GWn//v0uGNS+URdCFWojqf/46NGj3d8q7GmdNKkQrXNNNbDqIhhKgdJrr73makZTqmVW15F27dq5Y17Bg9ZZ3c0UBEQbkK5zSQVF9Q/X39pW7ceUqBClwFJjLHSOpkTBrb4TdZ3S96FukSrEP/nkk+7cD6XP1far8K39o8c6HhUoebRNCiQUUOh41f5QgNu2bVs7fvx42Pv99ttvLpBWn3stqwHD6m//zjvvBJeJ5fhVbbSuaWpp1XZoP+t/Ba+R9B3qee0rbbvW0+93q3NF11MFQrHQOaBgZ8aMGa5LkCog/Lj99tvddUPrqyBPlSE6HiKD10g6DkVjKaLRvtH1Xtc6/dYMHz7cFeZ1nU7JG2+8EfY7lV7e+RHacq7KNF3zNE/XUHXPUtCqlvhIui542wgoSgXyjBdffFHVtoHVq1e7xyNHjnSP9+7dG3X53377zT1/9dVXx/S+GzZscI8HDhyY4usaNmwYOPXUU4OPvfWJnEqVKhVYtGhRTNuqddFrZ8yYkeS5wYMHu+f++uuvQGb76aef3GePHz8+OE/rceLEiSTLFS5cODB69OjgvOnTp7vXTpw4Mcn7njx50v2/dOlSt8yAAQOSXUaOHDmS5Pl27doFatasGTavWrVq7v0i9/+kSZPc/P/85z/BeYcPHw7UqlXLzf/ggw9S3A/vvfdeIH/+/G5q2bJl4P777w+8++67gWPHjiVZVuvQq1evJMeb1jd0m/Q+CQkJgdtvvz047++//w6cfvrpgdatW4e9p16v4y3yPbXfU9pHt912W6BYsWJhx47eW6+dOnVqkuX1XOhne8elPi+S1r958+Zh8+bNm5em/dm4ceNAuXLlAvv37w/O+/zzzwP58uUL9OzZM8k5dsstt4S9Xud4mTJlAqkZMWKEe33x4sUD7du3Dzz66KOBtWvXJllu5syZ7rOXL18eNl/7SK//5JNP3OPNmze75fT5keeA993u2bMnUKhQoUDbtm3Dlpk8ebJ7L50Xkd9F6Hl/9OjRQIUKFQLXXHNNuo7faMfBmDFj3LG2devW4Dwdo3rtkCFDMvS7feGFF9xy2gcXX3xx4MEHH3T7Ndo1w7u2HD9+PNC1a9dA0aJF3XkVSp8X+blad51nKZ0jiYmJgTvvvDMQq+HDh7v3+uOPP6I+v3LlyrDrfZ06dVLdJ3LOOee4dUorbaOOW/3eafr+++8DTzzxhPse69evH3Yt0W9XyZIl3fUjNY899phb7927d6d5XZB70WIBpEC1taKazVh4y6c2mE3Pq2tFtCZu1Rip9kpddtTtR7VksdQKed0UovXP92oHI7syZBWtozdIUbWMqv3Wvq9Tp05YNwPtF409CR3YHNqk7y2jv9X9IrllJHSMhFo8VGOoFhK1nuhxKLU6qRY2lAbeqxuWxsN41O0kreNW1PqxcuVKV6ut7gzqqqLPULcVryYyNWqZCd2m5s2buy4Nmu/Jnz+/a7WK7OaQFqH7SMe09pFa4VR7/e233yb5DtWdyA/VgKvrYWi3No11Uu29vpvkqEuhskypdlrZdTzqoqH9rO8qWu1zKG2Xjrto52MotSYoE9E555zjavtV068aW9VGh9Zcq5ZfLTRqLdB+8yZ1v5MPPvjA/a+WOrVuqCUocqCu992qNUa1+2qFC11GLZnqovL222+HvU7nTmg/enUDUu136DEQy/EbehxozIS2Q/3ydaxFa/lUy0ZGfbeivvsap6Yac3X/U3c8fV+1a9eOek3UvlLXLbUGazvVqpMR1GVL2xDaipoWOq7UjdX7PYmkbpi63utYUIuRuqalJSuUjtVYB0zr+1PXSU3qoqdWF7XqqStT6LVE26pl09IN2Gvp0HEBEFgAKfAu7rFevL3lUwtI9Hy091bXEPXHVaFIhSV1A9By0QrUyfEKA9H6THv9mVMagKz+0Lt27UrXFNkHPzUqWKmLiAoKKqAqeNAPn/olhxbyVShRsJHSWBMto+4ioQXMaNSkr33s9cfX53njAqIFFpG2bt3qfphDf4xF65dW6jeuft/quqKBmsoypGNChT31kU9N6NgZ0VgBicxipvn6jFgpsYBSJev1KsBqH3kF1sh9pIDI70Btr3ugl51Gn6HCofrpR+7nyO8iuX2vwr0KPCokpbTvvMJRWvaTuoio25CWVfCvrEQqYKvLk3duqYua9p9XiPMmVRKEDjrW8apgQYXLWLdP+1sZ57znPaeffnqS/aXtC922WI5fZRzygjYVjrUdXjAQeRzo3NTnZ9R361HQrUBO4w6ULUndwrQN6hoZOYBb3dtUSFc3q4y8h4qCf3Vd1PmlQE3d09ITsEfSuaVrkcYEqdvRvffe6/6ONsYs8nWxVnqpUknBgiZVWun80P6L/C1Qt1Adq+reqO/TC+6i8cZvpeV7RO5HYAGkwOv/rh/gWGh5/cCqYJwcFfjVdz+lAoVHP+aqjVbtfWQBKTmqjfRqcyNpngoJKWUbUqFX75GeSWlTY6GxAOoTr4BKg1hVgNAPnwaqxuNmUirMqX+5Cpwa06EaX32eBqRK5Gf6zQCVGhUQtb+1H9SHW33mVeOdGrVGpHV+5ODt1KgAp8KjCjfq969xBdpHXpaZeOwjFX5VUPQKnyoY6jwJrX3PKMntu1j2kwp2Cv61vr169XLHlWq0vf2jwa5eIS5yUsEtXjJi2zyqJNA26hzROA0V2LX+GpMS7TgIbX2Mx3erVhW1VkyePNmNRVCwFDp2xAtCVGGgQCA992dIjsbiKJDQ+BJVXmjskq5RkZ8fqUyZMm4cRVqDAG8siAZPp0StYQrQYrne6thQEKPJq7RSZZASP4TSGBm1Aqr1VK2qamFTkKHjPJIXsIZmMkTeRbpZIAXegNfIbjCp0Y+aBq9q8KVq1TT4MJKyMumHVT+2aaEfJq8VJS3pG1WDrJrFaDftS8v9MFQASG9XKS+LVVqpkKH9pbzrkYXb0B8rZSVSwU0Fbw10jUbLKDDR4NrkWi1USNa+149maM211z0lLfSdKvBUYS20pk7Boh/eYPtoAWFmUsYcdeFQi4oCvtAMN36kVqupLjOqrVUaXh2D6nKkwltKvPMr2r5Xly0dQ35Tnqble1MGOO9703GooEwBbErbrOVUOFcLVXLnZOj2qYUitMuPvo+0ZhtKz/GrJAq6D4K2LXSwdnoy5aXnu03PuaIUrerqpmurukRp4HZG3bNFFScKCjWppl9d4JR5SYXulAIA0Xel7nmp0bVJx0Rka1AktZApTawqY9Tamd7tUYWKuvgpuUVoeltVeOgzNGl9tM1KGKAEEaGVbdour5UZoMUCSIb6UetGQi1btnSFg1ipNk0/2qoViiyg60KsvrS6qEfWFEWjQrL6EqvAnlp6xVAal6HuBqE1WqqhUkFBP7gpUb9br2Yr1inWHPGqRYusTVWNvTJbRW6PWhlUWxnJe72W0d/RMvx4y3g1uqGfqR9xdQ1IK6XTVF9rBUUejT1QZqa0UBATrQbZGw8QS5eqeIi2j1SQVTYcP7wCfnKpNFVAUyFFLSMffvhhmmq0dR6pUK7Cb+j7quCsrkr6rjKCvl+Ni4nGq7X2vjfVbuv41Q3cIul64LU8KlOUavjVKhRZ++/te51TKuQ9/fTTYd+HAnEdt0oJHKu0Hr/RjgP9HZl2NC3S891616xoUjpXtM9U46/uO8qa5LflUy03kQV9XYvVcpFaimn9hkhkJY+O1ciMXuLdwC5aRr9Q6jKpVjEFNtGOS7WQaAxQatTFVi1ByvzkUaVCKB2jXlAUub3KbudtI0CLBfD/15iru5EKTt6dt9UHX+ka09IlJRrV8irdorr46IKsAEMFINWgqrChHzr9MEbeHC90ffQDrh9/FSDU3Dx16tSY+rFqzIDWX60BSsuq1g413+vHyO9A24ykmkUVrLROGhSqWlLVaIbWzno1nkodqX2qVhd1iVABTYNbVZum2lBtqwoSKoSpn7vSbmpfq0+8nlNqTQ3m9GrjFNhpv+g7UUEhrS0FGjirAEfrpB9Wfbdq4Yq8Z0lKP+YqyGkMg2o0dewpeFRKTuWxz+rvR9+Djk11fdC9MnTcafvS050msoZeY1p0LGvckAINdfPzxrGoJapbt25u36pQG3mn4uTouFbBVQUcDV5X4V1dVjQ+JLX7daSVvi/tF9Xq6rhSX3sVDtU9SMeXggTVwouOQbVKquZcQaQCdRVOdf5rvndfFNX8qvDnDUhWNxh1J1KtvgqtGi+gmmDVSCtY1ueqa4paFhTkqQtderqKpfX41bHp3bBN10Z1/1KChPSM2Unvd6vzWseHzleti3fOq+VR26/50ej7UGWBtlHrnZZ7BiVHhXSNNVBhXr8Luj5rHfQ9KaVvSnQd071FtHzoTeTUKqhzS++p8WW6Bug4Uiuhjo3UvlftTy2rIEq/NwpmdZxpvsb3qHJM53Bq97JQVy1db3Q8KQGBxl3oHk6q0FKyAW23Wt51PimA1/Metdqoy6/GvABOVqelAjKTl6503bp1UdO7FilSxKXmvPLKK92yaU3HGvm+oT766KNAp06dAmXLlg0ULFgwULVq1UDfvn0DW7ZsSbJstHSzSg+oVI2haSFjsXHjRpemUilClbb2xhtvDOzatSuQVX788cckKWO1n++9995AxYoVXXrI888/36VgjExV6qW+/Oc//xmoUaOG259Ko3nttdcGfvjhh+AySpGolJN169Z1KSpPO+00lxo0NC3oG2+84dL96juvXr16YOzYscHvMTTlqlJQdujQIeq2KNVmx44d3b7V96sUjUpLm5YUmu+8845Leap1POWUU9x6KtVn//79k6RtTC7drJfe2JNc+mQvzWSs6WaVErVFixbuO6lUqVIwJW7k9uk7Ovvss6NuZ7TvcMGCBYF69eoFChQoEDX17KpVq9x8HbexeP/9992xo/VVqsyrrroq8PXXX6dpH0Xb/khKYfrcc88FOnfu7L4TpUPWd6+0nzrelNY1lFIH67jSvtGypUuXDjRp0iQwatSowIEDB8KW1bGn9/GW0z5bvHhx2DJKL6vjRcd9+fLlA/369XMpsUMl911ES6ea1uNX+7BNmzbuONVyun4plW/kdxftOIuUnu/21VdfDXTr1i1wxhlnuO9W56yOH10HDh48mGIqa3nmmWfc/Pvuuy/d6Wb13SpNd6NGjQIlSpRw26m/9d5poeud9l9o6l6le1UqZKW49rZL350+89ChQ2nePzoGlAa5QYMG7rvU+yh97NChQwM7d+5M0/ej66dSX3vXmddee819R0rhrGuTfreUajr0/eTZZ591nxn6PSBvS9A/xFjIK1SLrZp73XVXNV/Z/X1zI9VuqcZPzf2haVEBj8YmqGZUrVN+b/6F7CWvfrfqRqWWCw0oz03XPbXQKfOWsvoBwhgL5ClqtlbXi2iDqbPj++ZG2leSlmxYyJvULU1dTfzeKRnZT179btUlT+Pq1GUvHpnusoLGr6i7aXoHjiN3YowF8gT1CVZ/VvXbV9/RjMoQEq/3TY3uMZHSvSI0fiC1+zhkNg0uVF9z1dhpsKX61QOh1Gde2ZE0gFhjYeKdyQmZh+/WXLpeTbmFxvuk5UZ+yFvoCoU8QQP/NPhOA2UnTZqUYT9q8Xrf1Ghwb+RNsULp/gMKeLITDV7XQHINUNUgQA1WBCKP6927d7v0zhpIHOuNKZF98d0CeUO2CCymTJnimgd1kxb1vVahQ3e1TI4KJ8qjvGXLFlc4Ueq60HSCyWXNUU3p4MGD47INQGZSxqqU7jGhTCBNmjTJ1HUCAAB5W5YHFkqtqFRwSj2orhGq9VXgoFR60fL1Kx2j0qopBZ9SVCqdmgIL3ZFY6dxEAUpkfnENltLA2sj0lQAAAAByQWChYEJ5qL0bXmlQk3KDK8f7kCFDkizftWtXl8NaN/3yKKe4skwoOEkul7W6qyR3kx0AAAAAOXjwtm4GoxvzhGYU0N0ddbOX5O5uqvm6OVYo9dnUDYqiUZ/Ot99+292RNTm6i2TonSQV3OjGMLppTCw3IwMAAAByE7VBqIJeN+1UOT3bBhb79u1zmW3Kly8fNl+PdXfSaNTNKdrykd2fPAooNEgspdR26lalO5oCAAAASGr79u3uTux5Ot3s9OnT7cYbb7QiRYoku4xaTEJbQXQjm6pVq7odWLJkyUxaUwAAACB7OXjwoBumkJZsblkaWJQtW9by58/vuiuF0uMKFSpEfY3mp3X55cuXu0HgGiCeksKFC7spkoIKAgsAAADkdQlpGB6QpXfe1k28lBIzdFC1xjfoccuWLaO+RvMjB2EvXrw46vIvvPCCe3+lsAUAAAAQP1neFUpdkHr16mVNmzZ1965Qulllferdu7d7XqloK1eu7MZByMCBA93NvyZMmGAdOnSw2bNn25o1a9zdPCObbZS2VssBAAAAyOWBhdLH7t2710aMGOEGYCtt7KJFi4IDtLdt2xY2Ar1Vq1bu3hXDhw+3YcOGuRvkKSOUdw8LjwIOjWLv3r17pm8TAAAAkNdk+X0ssiO1diQmJrpB3IyxAAAAQF51MIZycZaOsQAAAACQOxBYAAAAAPCNwAIAAACAbwQWAAAAAHwjsAAAAADgG4EFAAAAAN8ILAAAAAD4RmABAAAAwDcCCwAAAAC+EVgAAAAA8I3AAgAAAIBvBfy/BQAAQNbp8W6PrF4FIK5mtptpOQEtFgAAAAB8I7AAAAAA4BuBBQAAAADfCCwAAAAA+EZgAQAAAMA3AgsAAAAAvhFYAAAAAPCNwAIAAACAbwQWAAAAAHwjsAAAAADgG4EFAAAAAN8ILAAAAAD4RmABAAAAwDcCCwAAAAC+EVgAAAAA8I3AAgAAAIBvBBYAAAAAfCOwAAAAAOAbgQUAAAAA3wgsAAAAAPhGYAEAAADANwILAAAAAL4RWAAAAADwjcACAAAAgG8EFgAAAAB8I7AAAAAA4BuBBQAAAADfCCwAAAAA+EZgAQAAAMA3AgsAAAAAvhFYAAAAAMj5gcWUKVOsevXqVqRIEWvevLmtWrUqxeXnzp1rdevWdcs3aNDAFi5cmGSZb775xjp27GiJiYlWvHhxO++882zbtm1x3AoAAAAgb8vSwGLOnDk2aNAgGzlypK1bt84aNWpk7dq1sz179kRdfsWKFda9e3fr06ePrV+/3jp37uymjRs3Bpf54Ycf7IILLnDBx7Jly+yLL76wBx980AUiAAAAAOIjIRAIBCyLqIVCrQmTJ092j0+ePGlVqlSx/v3725AhQ5Is37VrVzt8+LC99dZbwXktWrSwxo0b29SpU93jbt26WcGCBW3mzJnpXq+DBw+61o4DBw5YyZIl0/0+AAAg/nq82yOrVwGIq5nt0l+u9SuWcnGWtVgcO3bM1q5da23atPnfyuTL5x6vXLky6ms0P3R5UQuHt7wCk7ffftvOPPNMN79cuXIueJk/f36K63L06FG300InAAAAAGmXZYHFvn377MSJE1a+fPmw+Xq8a9euqK/R/JSWVxeqQ4cO2eOPP26XX365vffee3b11Vdbly5d7MMPP0x2XcaMGeMiMW9SqwkAAACAHDR4OyOpxUI6depk99xzj+sipS5VV155ZbCrVDRDhw51zTvetH379kxcawAAACDnK5BVH1y2bFnLnz+/7d69O2y+HleoUCHqazQ/peX1ngUKFLB69eqFLXPWWWfZxx9/nOy6FC5c2E0AAAAAcliLRaFChaxJkya2ZMmSsBYHPW7ZsmXU12h+6PKyePHi4PJ6Tw0G37RpU9gy3333nVWrVi0u2wEAyLoU4zfffLMlJCSETeoKCwDIY12hlGr2ueees5dfftnde6Jfv34u61Pv3r3d8z179nTdlDwDBw60RYsW2YQJE+zbb7+******************************++//95lnHrzzTftjjvuyJJtBADEL8W4KJDYuXNncHr11VczaYsAANkm3ayo4D9+/Hg3AFtjIp5++mlXiyUXXXSRq9l66aWXwmqvhg8fblu2bLHatWvbuHHj7Iorrgh7z+nTp7sB2T///LPVqVPHRo0a5cZdpBXpZgEgZ6QYV4vF77//nmr2P+RupJtFbjczh6SbzfLAIjsisACAjE8xXqxYMXvttddcq4OnV69eLjBYsGBBktdUrVrVtXDcfffdwXlq7VAQ8fnnnwcDCz1WV9jSpUvbJZdcYo888oiVKVMmk7YM2QGBBXK7mTkksMhVWaEAANlTPFKMe92gZsyY4cbfjR071qUWb9++vfssAEAeyQoFAIBf3bp1C/6twd0NGza0M844w5YtW2aXXnpplq4bAOQ1tFgAAHJkivFoatas6T5LyTsAAJmLwAIAkCNTjEejpB379++3ihUrZuDaAwDSgsACAJAjU4wfOnTIpRj/9NNPXaZABSHKAFirVi2XxhYAkLkYYwEAyBRKH7t3714bMWJEMMW4AgdvgPa2bdssX77/****************************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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "jd_ids = ['JOB_0', 'JOB_1']\n", "results = []\n", "\n", "for jd_id in jd_ids:\n", "    # <PERSON><PERSON><PERSON> các <PERSON> có điểm adjectives_sim > 0\n", "    subset = df_similarity[\n", "        (df_similarity['jd_id'] == jd_id) & \n", "        (df_similarity['adjectives_sim'] > 0)\n", "    ][['cr_id', 'adjectives_sim']]\n", "\n", "    if len(subset) < 3:\n", "        print(f\"⚠️ JD {jd_id} có ít hơn 3 CRs với adjectives_sim > 0. Bỏ qua.\")\n", "        continue\n", "\n", "    subset_sorted = subset.sort_values(by='adjectives_sim')\n", "\n", "    # Lấy: thấ<PERSON> nh<PERSON>t, trung bình, cao nhất\n", "    lowest = subset_sorted.iloc[0]\n", "    median = subset_sorted.iloc[len(subset_sorted) // 2]\n", "    highest = subset_sorted.iloc[-1]\n", "\n", "    top3 = pd.DataFrame([lowest, median, highest])\n", "    top3['jd_id'] = jd_id\n", "    results.append(top3)\n", "\n", "# <PERSON><PERSON><PERSON> h<PERSON> lại\n", "df_plot = pd.concat(results, ignore_index=True)\n", "\n", "# Vẽ biểu đồ cho từng JD\n", "for jd_id in df_plot['jd_id'].unique():\n", "    df_sub = df_plot[df_plot['jd_id'] == jd_id]\n", "\n", "    plt.figure(figsize=(8, 5))\n", "    plt.bar(df_sub['cr_id'], df_sub['adjectives_sim'], color=['#d9534f', '#f0ad4e', '#5cb85c'])\n", "    plt.title(f'JD {jd_id} — <PERSON><PERSON><PERSON> Similarity on Secondary Skills (3 CRs)')\n", "    plt.xlabel('CR ID')\n", "    plt.ylabel('<PERSON><PERSON><PERSON> Similarity')\n", "    for i, v in enumerate(df_sub['adjectives_sim']):\n", "        plt.text(i, v + 0.01, f\"{v:.2f}\", ha='center')\n", "    plt.tight_layout()\n", "    plt.show()\n"]}, {"cell_type": "code", "execution_count": 52, "id": "dfa06814", "metadata": {}, "outputs": [], "source": ["df_similarity.to_csv('../csv/jd_cr_similarity.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}