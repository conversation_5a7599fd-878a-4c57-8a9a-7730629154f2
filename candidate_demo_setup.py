#!/usr/bin/env python3
"""
Candidate Demo Setup
Tạo demo data để candidates có thể test job recommendation system
"""

import os
import json
from datetime import datetime
from pathlib import Path

def create_demo_folders():
    """Tạo các folder cần thiết cho candidate demo"""
    folders = [
        "candidate_profiles",
        "candidate_results"
    ]
    
    for folder in folders:
        os.makedirs(folder, exist_ok=True)
        print(f"✅ Created folder: {folder}")

def create_sample_candidate_profiles():
    """Tạo sample candidate profiles"""
    
    # Candidate 1: Experienced Python Developer
    python_dev = {
        "candidate_info": {
            "candidate_id": "CAND_2024_001",
            "name": "<PERSON><PERSON><PERSON>",
            "email": "<EMAIL>",
            "phone": "+84 901 234 567",
            "location": "Ho Chi Minh City",
            "current_position": "Senior Python Developer",
            "linkedin": "linkedin.com/in/nguyen-van-a"
        },
        "skills": {
            "programming_languages": ["Python", "JavaScript", "SQL"],
            "frameworks": ["Django", "Flask", "React"],
            "databases": ["PostgreSQL", "MySQL", "Redis"],
            "tools": ["Git", "Docker", "AWS", "Jenkins"],
            "soft_skills": ["Team Leadership", "Problem Solving", "Communication"]
        },
        "experience": {
            "total_years": 5,
            "current_company": "TechCorp Vietnam",
            "current_role": "Senior Python Developer",
            "previous_roles": [
                {
                    "company": "StartupXYZ",
                    "position": "Python Developer",
                    "duration": "2 years",
                    "key_achievements": ["Built REST APIs", "Database optimization", "Team mentoring"]
                }
            ]
        },
        "education": {
            "level": 2,
            "degree": "Bachelor of Computer Science",
            "university": "HCMUT",
            "graduation_year": 2019,
            "gpa": 3.6
        },
        "preferences": {
            "desired_positions": ["Senior Python Developer", "Tech Lead", "Full-stack Developer"],
            "preferred_locations": ["Ho Chi Minh City", "Remote"],
            "salary_expectation": "35-50M VND",
            "work_style": "Hybrid",
            "company_size": ["Scale-up", "Enterprise"],
            "industries": ["Fintech", "E-commerce", "SaaS"]
        },
        "job_search_settings": {
            "minimum_confidence": 0.7,
            "max_jobs_to_show": 15,
            "exclude_current_company": True,
            "include_stretch_goals": True
        }
    }
    
    # Candidate 2: Junior Data Scientist
    data_scientist = {
        "candidate_info": {
            "candidate_id": "CAND_2024_002",
            "name": "Tran Thi B",
            "email": "<EMAIL>",
            "phone": "+84 ***********",
            "location": "Hanoi",
            "current_position": "Data Analyst",
            "linkedin": "linkedin.com/in/tran-thi-b"
        },
        "skills": {
            "programming_languages": ["Python", "R", "SQL"],
            "frameworks": ["Pandas", "Scikit-learn", "TensorFlow"],
            "databases": ["PostgreSQL", "MongoDB"],
            "tools": ["Jupyter", "Git", "Tableau", "Excel"],
            "soft_skills": ["Analytical Thinking", "Data Visualization", "Presentation"]
        },
        "experience": {
            "total_years": 2,
            "current_company": "Analytics Plus",
            "current_role": "Data Analyst",
            "previous_roles": [
                {
                    "company": "Market Research Co",
                    "position": "Junior Analyst",
                    "duration": "1 year",
                    "key_achievements": ["Customer segmentation", "Sales forecasting", "Dashboard creation"]
                }
            ]
        },
        "education": {
            "level": 3,
            "degree": "Master of Data Science",
            "university": "VNU",
            "graduation_year": 2022,
            "gpa": 3.8
        },
        "preferences": {
            "desired_positions": ["Data Scientist", "ML Engineer", "Senior Data Analyst"],
            "preferred_locations": ["Hanoi", "Remote", "Ho Chi Minh City"],
            "salary_expectation": "25-35M VND",
            "work_style": "Remote",
            "company_size": ["Startup", "Scale-up"],
            "industries": ["Healthcare", "Fintech", "E-commerce"]
        },
        "job_search_settings": {
            "minimum_confidence": 0.6,
            "max_jobs_to_show": 20,
            "exclude_current_company": True,
            "include_stretch_goals": True
        }
    }
    
    # Candidate 3: Career Changer (from Java to Python)
    career_changer = {
        "candidate_info": {
            "candidate_id": "CAND_2024_003",
            "name": "Le Van C",
            "email": "<EMAIL>",
            "phone": "+84 ***********",
            "location": "Da Nang",
            "current_position": "Java Developer",
            "linkedin": "linkedin.com/in/le-van-c"
        },
        "skills": {
            "programming_languages": ["Java", "Python", "JavaScript"],
            "frameworks": ["Spring", "Django", "React"],
            "databases": ["MySQL", "PostgreSQL", "Oracle"],
            "tools": ["Git", "Maven", "Docker", "IntelliJ"],
            "soft_skills": ["Problem Solving", "Team Collaboration", "Learning Agility"]
        },
        "experience": {
            "total_years": 4,
            "current_company": "Enterprise Solutions",
            "current_role": "Java Developer",
            "previous_roles": [
                {
                    "company": "Software House",
                    "position": "Junior Java Developer",
                    "duration": "2 years",
                    "key_achievements": ["Enterprise applications", "Microservices", "Code optimization"]
                }
            ]
        },
        "education": {
            "level": 2,
            "degree": "Bachelor of Software Engineering",
            "university": "DUT",
            "graduation_year": 2020,
            "gpa": 3.4
        },
        "preferences": {
            "desired_positions": ["Python Developer", "Full-stack Developer", "Backend Developer"],
            "preferred_locations": ["Da Nang", "Remote", "Ho Chi Minh City"],
            "salary_expectation": "25-40M VND",
            "work_style": "Hybrid",
            "company_size": ["Scale-up", "Startup"],
            "industries": ["Technology", "Fintech", "Gaming"]
        },
        "job_search_settings": {
            "minimum_confidence": 0.65,
            "max_jobs_to_show": 12,
            "exclude_current_company": True,
            "include_stretch_goals": True
        }
    }
    
    # Save candidate profiles
    profiles = [
        (python_dev, "nguyen_van_a_senior_python_dev.json"),
        (data_scientist, "tran_thi_b_data_scientist.json"),
        (career_changer, "le_van_c_career_changer.json")
    ]
    
    for profile, filename in profiles:
        filepath = f"candidate_profiles/{filename}"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(profile, f, indent=2, ensure_ascii=False)
    
    print("✅ Created sample candidate profiles:")
    for _, filename in profiles:
        print(f"   📄 candidate_profiles/{filename}")

def create_additional_job_postings():
    """Tạo thêm job postings để có nhiều options cho candidates"""
    
    # Job 3: Tech Lead Position
    tech_lead_job = {
        "job_info": {
            "job_id": "JOB_2024_TECH_003",
            "job_title": "Tech Lead - Python",
            "company": "InnovateTech",
            "department": "Engineering",
            "location": "Ho Chi Minh City",
            "employment_type": "Full-time",
            "salary_range": "45-60M VND",
            "posting_date": datetime.now().strftime("%Y-%m-%d")
        },
        "requirements": {
            "required_skills": [
                "Python", "Django", "Team Leadership", "System Architecture", "PostgreSQL"
            ],
            "preferred_skills": [
                "Microservices", "AWS", "Docker", "Kubernetes", "Agile"
            ],
            "required_experience": 5,
            "required_education": 2,
            "language_requirements": ["English - Advanced"],
            "certifications": ["AWS Solutions Architect (preferred)"]
        },
        "job_description": """
We are seeking a Tech Lead to guide our Python development team.
You will be responsible for technical decisions, team mentoring, and system architecture.

Key Responsibilities:
- Lead a team of 5-8 Python developers
- Design system architecture and technical solutions
- Code reviews and technical mentoring
- Collaborate with product and design teams
- Drive technical excellence and best practices

Requirements:
- 5+ years of Python development experience
- 2+ years of team leadership experience
- Strong knowledge of system design
- Experience with microservices architecture
- Excellent English communication skills
        """,
        "company_culture": {
            "work_style": "Hybrid",
            "team_size": "20-30 people",
            "company_stage": "Scale-up",
            "benefits": ["Stock options", "Health insurance", "Learning budget", "Flexible hours"]
        },
        "screening_preferences": {
            "minimum_confidence": 0.75,
            "max_candidates_to_review": 10,
            "priority_factors": ["experience_match", "skill_similarity"],
            "exclude_overqualified": False
        }
    }
    
    # Job 4: Remote ML Engineer
    ml_engineer_job = {
        "job_info": {
            "job_id": "JOB_2024_ML_004",
            "job_title": "Machine Learning Engineer",
            "company": "AI Solutions Co",
            "department": "AI Research",
            "location": "Remote",
            "employment_type": "Full-time",
            "salary_range": "40-55M VND",
            "posting_date": datetime.now().strftime("%Y-%m-%d")
        },
        "requirements": {
            "required_skills": [
                "Python", "Machine Learning", "TensorFlow", "PyTorch", "SQL"
            ],
            "preferred_skills": [
                "MLOps", "Kubernetes", "AWS", "Docker", "Spark"
            ],
            "required_experience": 3,
            "required_education": 3,
            "language_requirements": ["English - Advanced"],
            "certifications": ["Google Cloud ML Engineer (preferred)"]
        },
        "job_description": """
Join our AI team to build and deploy machine learning models at scale.
You will work on cutting-edge ML projects with real-world impact.

Key Responsibilities:
- Develop and deploy ML models in production
- Build ML pipelines and infrastructure
- Collaborate with data scientists and engineers
- Optimize model performance and scalability
- Research and implement new ML techniques

Requirements:
- 3+ years of ML engineering experience
- Strong Python and ML framework knowledge
- Experience with model deployment and MLOps
- Master's degree in relevant field preferred
- Excellent English for international collaboration
        """,
        "company_culture": {
            "work_style": "Remote",
            "team_size": "15-20 people",
            "company_stage": "Startup",
            "benefits": ["Stock options", "Health insurance", "Conference budget", "Home office setup"]
        },
        "screening_preferences": {
            "minimum_confidence": 0.8,
            "max_candidates_to_review": 8,
            "priority_factors": ["skill_similarity", "education_match"],
            "exclude_overqualified": False
        }
    }
    
    # Save additional job postings
    jobs = [
        (tech_lead_job, "tech_lead_python_job.json"),
        (ml_engineer_job, "ml_engineer_remote_job.json")
    ]
    
    for job, filename in jobs:
        filepath = f"job_requirements/{filename}"
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(job, f, indent=2, ensure_ascii=False)
    
    print("✅ Created additional job postings:")
    for _, filename in jobs:
        print(f"   📄 job_requirements/{filename}")

def create_candidate_demo_instructions():
    """Tạo file hướng dẫn demo cho candidates"""
    
    instructions = """
# 🎯 JOB RECOMMENDATION SYSTEM - CANDIDATE DEMO GUIDE

## 🚀 QUICK DEMO STEPS FOR CANDIDATES

### 1. Start the job recommendation system:
```bash
python job_recommendation_system.py
```

### 2. Try job search for experienced Python developer:
- Select option 2 (Find job recommendations)
- Choose profile: nguyen_van_a_senior_python_dev.json
- Review personalized job recommendations!

### 3. Try job search for data scientist:
- Select option 2 again
- Choose profile: tran_thi_b_data_scientist.json
- See different recommendations based on skills!

### 4. Try career changer scenario:
- Select option 2 again
- Choose profile: le_van_c_career_changer.json
- Explore transition opportunities!

## 📊 EXPECTED RESULTS

### For Senior Python Developer (Nguyen Van A):
- **Perfect matches**: Tech Lead, Senior Python positions
- **High salary compatibility**: 35-50M expectation vs market
- **Leadership opportunities**: Tech Lead roles highlighted

### For Data Scientist (Tran Thi B):
- **Best matches**: ML Engineer, Data Scientist roles
- **Growth potential**: Stretch opportunities identified
- **Remote options**: Remote-friendly positions prioritized

### For Career Changer (Le Van C):
- **Transition opportunities**: Python roles for Java developers
- **Skill development**: Learning paths suggested
- **Location flexibility**: Da Nang and remote options

## 🎯 WHAT TO OBSERVE

1. **Personalized Ranking**: How AI ranks jobs based on individual profiles
2. **Skill Matching**: Relevance of candidate skills to job requirements
3. **Career Growth**: Stretch opportunities and development paths
4. **Salary Compatibility**: Market rate vs expectations analysis
5. **Location Preferences**: Geographic and remote work matching

## 💡 TIPS FOR TESTING

- Try adjusting `minimum_confidence` in profiles (0.6, 0.7, 0.8)
- Modify `salary_expectation` to see impact on recommendations
- Change `preferred_locations` to test location matching
- Update skills to see how it affects job rankings

## 🚀 ADVANCED TESTING

- Create your own candidate profile using the template
- Add new skills and see recommendation changes
- Test with different experience levels
- Explore various industry preferences

Enjoy discovering your perfect job matches! 🎯
"""
    
    with open("CANDIDATE_DEMO_INSTRUCTIONS.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Created candidate demo instructions: CANDIDATE_DEMO_INSTRUCTIONS.md")

def main():
    """Setup complete candidate demo environment"""
    print("🎯 SETTING UP JOB RECOMMENDATION SYSTEM DEMO")
    print("="*55)
    
    # Create folders
    create_demo_folders()
    print()
    
    # Create sample candidate profiles
    create_sample_candidate_profiles()
    print()
    
    # Create additional job postings
    create_additional_job_postings()
    print()
    
    # Create demo instructions
    create_candidate_demo_instructions()
    print()
    
    print("🎉 CANDIDATE DEMO SETUP COMPLETE!")
    print("="*35)
    print("📋 What was created:")
    print("   📁 candidate_profiles/ - Sample candidate profiles")
    print("   📁 candidate_results/ - Results will be saved here")
    print("   📁 job_requirements/ - Additional job postings")
    print("   📄 CANDIDATE_DEMO_INSTRUCTIONS.md - How to run demo")
    print()
    print("🚀 Next steps:")
    print("   1. Run: python job_recommendation_system.py")
    print("   2. Follow CANDIDATE_DEMO_INSTRUCTIONS.md")
    print("   3. Test with different candidate profiles!")
    print()
    print("💡 Tip: Start with Nguyen Van A (experienced dev) for best demo results!")

if __name__ == "__main__":
    main()
