# 🏢 HR RECRUITMENT SYSTEM - USER GUIDE

## 📋 OVERVIEW
Hệ thống tuyển dụng tự động sử dụng AI để screening và ranking ứng viên dựa trên job requirements.

## 🚀 QUICK START

### Step 1: Cài đặt và chạy system
```bash
python hr_recruitment_system.py
```

### Step 2: Workflow cơ bản
1. **Tạo Job Requirements** → Định nghĩa yêu cầu công việc
2. **Upload CVs** → Đưa CV vào folder
3. **Auto Screening** → Hệ thống tự động phân tích
4. **Review Results** → Xem kết quả và recommendations

## 📝 DETAILED WORKFLOW

### 🎯 1. TẠO JOB REQUIREMENTS

#### A. Chọn Option 1 trong menu
```
📋 MAIN MENU:
1. Create job requirement template  ← Chọn này
2. Screen candidates for a position
3. View previous screening results
4. Exit
```

#### B. Edit file template được tạo
File: `job_requirements/job_requirement_template.json`

**<PERSON><PERSON><PERSON> thông tin cần điền:**

```json
{
  "job_info": {
    "job_id": "JOB_2024_001",           // ID duy nhất cho job
    "job_title": "Senior Python Developer",  // Tên position
    "department": "Engineering",         // Phòng ban
    "location": "Ho Chi Minh City",     // Địa điểm làm việc
    "salary_range": "25-35M VND"        // Mức lương
  },
  "requirements": {
    "required_skills": [                // Skills bắt buộc
      "Python", "Django", "PostgreSQL"
    ],
    "preferred_skills": [               // Skills ưu tiên
      "React", "AWS", "Docker"
    ],
    "required_experience": 3,           // Số năm kinh nghiệm
    "required_education": 2             // 1=THPT, 2=Đại học, 3=Thạc sĩ, 4=Tiến sĩ
  },
  "screening_preferences": {
    "minimum_confidence": 0.7,          // Chỉ show candidates >= 70% confidence
    "max_candidates_to_review": 20      // Tối đa 20 candidates để review
  }
}
```

### 📁 2. CHUẨN BỊ CV FILES

#### A. Tạo folder chứa CVs
```
candidate_uploads/
├── nguyen_van_a_python_developer.pdf
├── tran_thi_b_java_engineer.docx
├── le_van_c_fullstack_dev.pdf
└── ...
```

#### B. Supported file formats
- ✅ PDF files (.pdf)
- ✅ Word documents (.doc, .docx)
- ✅ Text files (.txt)

#### C. File naming tips
- Đặt tên file có ý nghĩa: `[Tên]_[Skills]_[Position].pdf`
- Ví dụ: `nguyen_van_a_python_django_senior.pdf`

### 🔍 3. CHẠY SCREENING PROCESS

#### A. Chọn Option 2 trong menu
```
📋 MAIN MENU:
1. Create job requirement template
2. Screen candidates for a position  ← Chọn này
3. View previous screening results
4. Exit
```

#### B. Follow guided steps
```
Step 1: Load job requirements
Available job requirement files:
  1. job_requirement_template.json
Select job file (1-1): 1

Step 2: Load candidate CVs
Enter path to candidate CV folder: candidate_uploads

Step 3: Screening candidates...
✅ Processed 15 candidate files
🔍 SCREENING 15 CANDIDATES
📋 Position: Senior Python Developer
✅ Screening completed: 8 qualified candidates found

Step 4: Generating HR report...
📊 HR REPORT GENERATED
📁 Detailed results: hr_results/JOB_2024_001_screening_results_20241217_143022.csv
📁 Summary report: hr_results/JOB_2024_001_hr_report_20241217_143022.json
```

### 📊 4. XEM KẾT QUẢ SCREENING

#### A. Summary Report
```
📊 SCREENING SUMMARY
Position: Senior Python Developer
Department: Engineering
Location: Ho Chi Minh City
Salary Range: 25-35M VND

📈 RESULTS:
Total Candidates Screened: 15
Highly Suitable: 3 candidates
Moderately Suitable: 5 candidates
Not Suitable: 7 candidates
Average Confidence: 72.5%

🎯 TOP 5 CANDIDATES:
Rank Name                 Confidence   Prediction         Key Skills
1    Nguyen Van A         89.2%        Highly Suitable    Python, Django, PostgreSQL (+2 more)
2    Tran Thi B          85.7%        Highly Suitable    Python, Flask, AWS (+3 more)
3    Le Van C            78.3%        Moderately Suitable JavaScript, React, Node.js (+1 more)
```

#### B. Detailed Candidate Information
```
============================================================
CANDIDATE #1: Nguyen Van A
============================================================
📧 Email: <EMAIL>
📱 Phone: +84 xxx xxx xxx
📄 CV File: nguyen_van_a_python_developer.pdf
🎯 Skills: Python, Django, PostgreSQL, Docker, AWS
💼 Experience: 4 years
🎓 Education Level: 2 (Bachelor's)
📍 Location: Ho Chi Minh City

🤖 AI ASSESSMENT:
   Prediction: Highly Suitable
   Confidence: 89.2%
   Probabilities:
     • Not Suitable: 5.2%
     • Moderately Suitable: 5.6%
     • Highly Suitable: 89.2%

✅ MATCH REASONS:
   🎯 Excellent skill match (85.7%)
   💼 Meets experience requirement (4 years)
   🎓 Education requirement satisfied
   📍 Perfect location match
   ⭐ High confidence match

📊 DETAILED SCORES:
   • Skill Similarity: 85.7%
   • Experience Match: 133.3%
   • Education Match: 100.0%
   • Location Compatibility: 100.0%
```

#### C. HR Recommendations
```
💡 RECOMMENDATIONS:
🎯 PRIORITY INTERVIEWS: 3 high-confidence candidates
   • Nguyen Van A - Highly Suitable (89.2%)
   • Tran Thi B - Highly Suitable (85.7%)
   • Le Van C - Moderately Suitable (78.3%)
```

## 📁 5. OUTPUT FILES

### A. CSV File (Detailed Results)
File: `hr_results/JOB_2024_001_screening_results_20241217_143022.csv`

**Columns include:**
- candidate_id, name, email, phone
- skills, experience_years, education_level
- prediction_score, prediction_label, confidence
- skill_similarity, experience_match, education_match
- match_reasons, red_flags

### B. JSON File (Summary Report)
File: `hr_results/JOB_2024_001_hr_report_20241217_143022.json`

**Contains:**
- Job information
- Screening summary statistics
- Top candidates details
- HR recommendations

## 🎯 6. INTERPRETATION GUIDE

### A. Prediction Labels
- **Highly Suitable (2)**: 🟢 Definitely interview
- **Moderately Suitable (1)**: 🟡 Consider for interview
- **Not Suitable (0)**: 🔴 Skip or keep for future positions

### B. Confidence Levels
- **90%+**: 🔥 Extremely reliable prediction
- **80-90%**: ⭐ High confidence
- **70-80%**: ✅ Good confidence
- **60-70%**: ⚠️ Medium confidence - manual review recommended
- **<60%**: ❌ Low confidence - manual review required

### C. Key Metrics
- **Skill Similarity**: % overlap between candidate skills and job requirements
- **Experience Match**: How well candidate experience matches requirements
- **Education Match**: Education level compatibility
- **Location Compatibility**: Geographic fit

## ⚠️ 7. RED FLAGS TO WATCH

### Common Red Flags:
- ⚠️ **Under-experienced**: Candidate có ít kinh nghiệm hơn yêu cầu
- ⚠️ **Low skill match**: Skill overlap thấp - cần training nhiều
- ⚠️ **Education mismatch**: Trình độ học vấn không đạt yêu cầu
- ⚠️ **Location mismatch**: Cần relocation
- 💰 **Overqualified**: Có thể expect lương cao hơn

## 🔧 8. TROUBLESHOOTING

### Common Issues:

#### A. "No candidates found"
**Causes:**
- Folder path không đúng
- Không có file CV trong folder
- File format không được support

**Solutions:**
- Check đường dẫn folder
- Ensure files có extension .pdf, .doc, .docx, .txt
- Check file permissions

#### B. "Low confidence predictions"
**Causes:**
- Job requirements quá specific
- CV quality thấp
- Skill mismatch lớn

**Solutions:**
- Review và adjust job requirements
- Expand skill requirements
- Lower minimum_confidence threshold

#### C. "No qualified candidates"
**Causes:**
- Requirements quá cao
- Candidate pool không phù hợp
- Minimum confidence quá cao

**Solutions:**
- Lower experience requirements
- Expand skill criteria
- Reduce minimum_confidence to 0.6
- Increase max_candidates_to_review

## 📞 9. SUPPORT

### For Technical Issues:
- Check model files exist: `xgboost_model.pkl`
- Ensure all dependencies installed
- Review error messages in console

### For Business Questions:
- Review HR recommendations in report
- Analyze red flags for each candidate
- Consider adjusting screening preferences

---

**🎯 Remember**: This AI system is a **screening tool** to help HR prioritize candidates. Final hiring decisions should always include human judgment and proper interviews!
