# Import required libraries
import pandas as pd
import numpy as np
import re
import string
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Text processing libraries
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from nltk.stem import PorterStemmer

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')
    
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

print("✅ Libraries imported successfully")
print(f"📅 Processing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Load raw datasets
print("📊 Loading raw datasets...")

try:
    # Load resume data
    raw_resumes = pd.read_csv('../data/raw/UpdatedResumeDataSet.csv')
    print(f"✅ Loaded {len(raw_resumes):,} resume records")
    
    # Load job data
    raw_jobs = pd.read_csv('../data/raw/itviec_jobs_undetected.csv')
    print(f"✅ Loaded {len(raw_jobs):,} job records")
    
except FileNotFoundError as e:
    print(f"❌ Error loading data: {e}")
    print("Please ensure the raw data files are in the correct location")

# Display data structure
print("\n📋 Resume Data Structure:")
print(f"Columns: {list(raw_resumes.columns)}")
print(f"Shape: {raw_resumes.shape}")
print(f"Categories: {raw_resumes['Category'].nunique()} unique")

print("\n📋 Job Data Structure:")
print(f"Columns: {list(raw_jobs.columns)}")
print(f"Shape: {raw_jobs.shape}")

# Show sample data
print("\n📄 Sample Resume Categories:")
print(raw_resumes['Category'].value_counts().head(10))

print("\n📄 Sample Job Titles:")
print(raw_jobs['title'].head(10).tolist())

# Text cleaning functions
def clean_text(text):
    """
    Comprehensive text cleaning function
    """
    if pd.isna(text) or text == '':
        return ''
    
    # Convert to string and lowercase
    text = str(text).lower()
    
    # Remove HTML tags
    text = re.sub(r'<[^>]+>', ' ', text)
    
    # Remove URLs
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', ' ', text)
    
    # Remove email addresses
    text = re.sub(r'\S+@\S+', ' ', text)
    
    # Remove phone numbers
    text = re.sub(r'[\+]?[1-9]?[0-9]{7,15}', ' ', text)
    
    # Remove special characters but keep spaces
    text = re.sub(r'[^a-zA-Z0-9\s]', ' ', text)
    
    # Remove extra whitespaces
    text = re.sub(r'\s+', ' ', text)
    
    # Remove leading/trailing spaces
    text = text.strip()
    
    return text

def extract_skills(text):
    """
    Extract technical skills from text
    """
    if pd.isna(text) or text == '':
        return []
    
    # Common technical skills (expanded list)
    tech_skills = [
        # Programming Languages
        'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin',
        'typescript', 'scala', 'r', 'matlab', 'perl', 'shell', 'bash',
        
        # Web Technologies
        'html', 'css', 'react', 'angular', 'vue', 'nodejs', 'express', 'django', 'flask', 'spring',
        'laravel', 'symfony', 'rails', 'asp.net', 'jquery', 'bootstrap',
        
        # Databases
        'mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sql server', 'sqlite', 'cassandra',
        'elasticsearch', 'neo4j', 'dynamodb',
        
        # Cloud & DevOps
        'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'gitlab', 'github', 'terraform',
        'ansible', 'chef', 'puppet', 'vagrant',
        
        # Data Science & AI
        'machine learning', 'deep learning', 'artificial intelligence', 'data science', 'nlp',
        'tensorflow', 'pytorch', 'keras', 'scikit-learn', 'pandas', 'numpy', 'matplotlib',
        'seaborn', 'jupyter', 'tableau', 'power bi',
        
        # Mobile Development
        'android', 'ios', 'react native', 'flutter', 'xamarin', 'ionic',
        
        # Other Technologies
        'git', 'linux', 'windows', 'macos', 'agile', 'scrum', 'rest api', 'graphql', 'microservices',
        'blockchain', 'iot', 'ar', 'vr'
    ]
    
    text_lower = str(text).lower()
    found_skills = []
    
    for skill in tech_skills:
        if skill in text_lower:
            found_skills.append(skill)
    
    return list(set(found_skills))  # Remove duplicates

def extract_experience_years(text):
    """
    Extract years of experience from text
    """
    if pd.isna(text):
        return 0
    
    text = str(text).lower()
    
    # Pattern to find experience mentions
    patterns = [
        r'(\d+)\+?\s*years?\s*(?:of\s*)?experience',
        r'experience\s*(?:of\s*)?(\d+)\+?\s*years?',
        r'(\d+)\+?\s*years?\s*in',
        r'(\d+)\+?\s*yrs?\s*(?:of\s*)?experience',
    ]
    
    years = []
    for pattern in patterns:
        matches = re.findall(pattern, text)
        years.extend([int(match) for match in matches])
    
    return max(years) if years else 0

print("✅ Text cleaning functions defined")

# Process resume data
print("🔄 Processing resume data...")

# Create clean resume dataframe
clean_resumes = raw_resumes.copy()

# Clean resume text
print("📝 Cleaning resume text...")
clean_resumes['clean_text'] = clean_resumes['Resume'].apply(clean_text)

# Extract skills
print("🎯 Extracting skills...")
clean_resumes['extracted_skills'] = clean_resumes['Resume'].apply(extract_skills)
clean_resumes['skills_count'] = clean_resumes['extracted_skills'].apply(len)
clean_resumes['skills_text'] = clean_resumes['extracted_skills'].apply(lambda x: ', '.join(x))

# Extract experience
print("💼 Extracting experience...")
clean_resumes['experience_years'] = clean_resumes['Resume'].apply(extract_experience_years)

# Calculate text statistics
print("📊 Calculating text statistics...")
clean_resumes['text_length'] = clean_resumes['clean_text'].str.len()
clean_resumes['word_count'] = clean_resumes['clean_text'].str.split().str.len()

# Standardize category names
clean_resumes['category_clean'] = clean_resumes['Category'].str.strip().str.title()

# Remove rows with empty text
initial_count = len(clean_resumes)
clean_resumes = clean_resumes[clean_resumes['clean_text'].str.len() > 10]
final_count = len(clean_resumes)

print(f"✅ Resume processing completed")
print(f"📊 Records: {initial_count:,} → {final_count:,} (removed {initial_count-final_count:,} empty records)")
print(f"📋 Categories: {clean_resumes['category_clean'].nunique()} unique")
print(f"🎯 Average skills per resume: {clean_resumes['skills_count'].mean():.1f}")
print(f"💼 Average experience: {clean_resumes['experience_years'].mean():.1f} years")

# Show sample processed data
print("\n📄 Sample processed resume:")
sample_idx = clean_resumes['skills_count'].idxmax()
sample = clean_resumes.loc[sample_idx]
print(f"Category: {sample['category_clean']}")
print(f"Skills: {sample['skills_text'][:100]}...")
print(f"Experience: {sample['experience_years']} years")
print(f"Text length: {sample['text_length']} characters")

# Process job data
print("🔄 Processing job data...")

# Create clean job dataframe
clean_jobs = raw_jobs.copy()

# Combine job text fields
print("📝 Combining job text fields...")
clean_jobs['combined_text'] = (
    clean_jobs['title'].fillna('') + ' ' +
    clean_jobs['description'].fillna('') + ' ' +
    clean_jobs['requirements'].fillna('') + ' ' +
    clean_jobs['skills'].fillna('')
)

# Clean job text
print("🧹 Cleaning job text...")
clean_jobs['clean_text'] = clean_jobs['combined_text'].apply(clean_text)

# Extract skills from jobs
print("🎯 Extracting required skills...")
clean_jobs['required_skills'] = clean_jobs['combined_text'].apply(extract_skills)
clean_jobs['skills_count'] = clean_jobs['required_skills'].apply(len)
clean_jobs['skills_text'] = clean_jobs['required_skills'].apply(lambda x: ', '.join(x))

# Extract experience requirements
print("💼 Extracting experience requirements...")
clean_jobs['required_experience'] = clean_jobs['combined_text'].apply(extract_experience_years)

# Clean and standardize other fields
clean_jobs['title_clean'] = clean_jobs['title'].fillna('').str.strip().str.title()
clean_jobs['company_clean'] = clean_jobs['company'].fillna('').str.strip().str.title()
clean_jobs['location_clean'] = clean_jobs['location'].fillna('').str.strip().str.title()

# Process salary information
def clean_salary(salary_text):
    if pd.isna(salary_text) or salary_text == '':
        return 'Not specified'
    return str(salary_text).strip()

clean_jobs['salary_clean'] = clean_jobs['salary'].apply(clean_salary)

# Calculate text statistics
print("📊 Calculating text statistics...")
clean_jobs['text_length'] = clean_jobs['clean_text'].str.len()
clean_jobs['word_count'] = clean_jobs['clean_text'].str.split().str.len()

# Remove jobs with insufficient information
initial_count = len(clean_jobs)
clean_jobs = clean_jobs[
    (clean_jobs['clean_text'].str.len() > 20) &
    (clean_jobs['title_clean'] != '') &
    (clean_jobs['company_clean'] != '')
]
final_count = len(clean_jobs)

print(f"✅ Job processing completed")
print(f"📊 Records: {initial_count:,} → {final_count:,} (removed {initial_count-final_count:,} incomplete records)")
print(f"🏢 Companies: {clean_jobs['company_clean'].nunique()} unique")
print(f"📍 Locations: {clean_jobs['location_clean'].nunique()} unique")
print(f"🎯 Average skills per job: {clean_jobs['skills_count'].mean():.1f}")
print(f"💼 Average required experience: {clean_jobs['required_experience'].mean():.1f} years")

# Show sample processed data
print("\n📄 Sample processed job:")
sample_idx = clean_jobs['skills_count'].idxmax()
sample = clean_jobs.loc[sample_idx]
print(f"Title: {sample['title_clean']}")
print(f"Company: {sample['company_clean']}")
print(f"Location: {sample['location_clean']}")
print(f"Required Skills: {sample['skills_text'][:100]}...")
print(f"Required Experience: {sample['required_experience']} years")
print(f"Text length: {sample['text_length']} characters")

# Data quality analysis
print("🔍 Analyzing data quality...")

# Resume data quality
print("\n👤 RESUME DATA QUALITY:")
print(f"📊 Total records: {len(clean_resumes):,}")
print(f"📋 Categories: {clean_resumes['category_clean'].nunique()}")
print(f"📝 Average text length: {clean_resumes['text_length'].mean():.0f} characters")
print(f"🎯 Records with skills: {(clean_resumes['skills_count'] > 0).sum():,} ({(clean_resumes['skills_count'] > 0).mean()*100:.1f}%)")
print(f"💼 Records with experience: {(clean_resumes['experience_years'] > 0).sum():,} ({(clean_resumes['experience_years'] > 0).mean()*100:.1f}%)")

print("\n📈 Resume Categories Distribution:")
category_dist = clean_resumes['category_clean'].value_counts().head(10)
for cat, count in category_dist.items():
    print(f"  • {cat}: {count:,} ({count/len(clean_resumes)*100:.1f}%)")

# Job data quality
print("\n🏢 JOB DATA QUALITY:")
print(f"📊 Total records: {len(clean_jobs):,}")
print(f"🏢 Companies: {clean_jobs['company_clean'].nunique()}")
print(f"📍 Locations: {clean_jobs['location_clean'].nunique()}")
print(f"📝 Average text length: {clean_jobs['text_length'].mean():.0f} characters")
print(f"🎯 Jobs with skills: {(clean_jobs['skills_count'] > 0).sum():,} ({(clean_jobs['skills_count'] > 0).mean()*100:.1f}%)")
print(f"💼 Jobs with experience req: {(clean_jobs['required_experience'] > 0).sum():,} ({(clean_jobs['required_experience'] > 0).mean()*100:.1f}%)")

print("\n📈 Top Job Locations:")
location_dist = clean_jobs['location_clean'].value_counts().head(10)
for loc, count in location_dist.items():
    print(f"  • {loc}: {count:,} ({count/len(clean_jobs)*100:.1f}%)")

print("\n📈 Top Companies:")
company_dist = clean_jobs['company_clean'].value_counts().head(10)
for comp, count in company_dist.items():
    print(f"  • {comp}: {count:,} ({count/len(clean_jobs)*100:.1f}%)")

# Skills analysis
print("\n🎯 SKILLS ANALYSIS:")
from collections import Counter

# Flatten all skills
all_resume_skills = [skill for skills_list in clean_resumes['extracted_skills'] for skill in skills_list]
all_job_skills = [skill for skills_list in clean_jobs['required_skills'] for skill in skills_list]

resume_skill_counts = Counter(all_resume_skills)
job_skill_counts = Counter(all_job_skills)

print(f"📊 Unique skills in resumes: {len(resume_skill_counts)}")
print(f"📊 Unique skills in jobs: {len(job_skill_counts)}")

print("\n🔥 Top 10 Skills in Resumes:")
for skill, count in resume_skill_counts.most_common(10):
    print(f"  • {skill}: {count:,}")

print("\n🔥 Top 10 Skills in Jobs:")
for skill, count in job_skill_counts.most_common(10):
    print(f"  • {skill}: {count:,}")

print("\n✅ Data quality analysis completed")

# Prepare final datasets for export
print("📤 Preparing data for export...")

# Select columns for final resume dataset
final_resumes = clean_resumes[[
    'Category',                    # Original category
    'category_clean',             # Cleaned category
    'Resume',                     # Original resume text
    'clean_text',                 # Cleaned resume text
    'extracted_skills',           # List of extracted skills
    'skills_text',                # Skills as comma-separated text
    'skills_count',               # Number of skills
    'experience_years',           # Years of experience
    'text_length',                # Text length
    'word_count'                  # Word count
]].copy()

# Select columns for final job dataset
final_jobs = clean_jobs[[
    'id',                         # Job ID
    'title',                      # Original title
    'title_clean',                # Cleaned title
    'company',                    # Original company
    'company_clean',              # Cleaned company
    'location',                   # Original location
    'location_clean',             # Cleaned location
    'salary',                     # Original salary
    'salary_clean',               # Cleaned salary
    'work_type',                  # Work type
    'description',                # Original description
    'requirements',               # Original requirements
    'skills',                     # Original skills
    'combined_text',              # Combined text
    'clean_text',                 # Cleaned combined text
    'required_skills',            # List of required skills
    'skills_text',                # Skills as comma-separated text
    'skills_count',               # Number of required skills
    'required_experience',        # Required years of experience
    'text_length',                # Text length
    'word_count'                  # Word count
]].copy()

# Add metadata
processing_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
final_resumes['processed_date'] = processing_date
final_jobs['processed_date'] = processing_date

# Export to CSV
print("💾 Exporting clean datasets...")

# Export resumes
resume_file = '../data/clean_resumes.csv'
final_resumes.to_csv(resume_file, index=False, encoding='utf-8')
print(f"✅ Exported {len(final_resumes):,} clean resumes to: {resume_file}")

# Export jobs
job_file = '../data/clean_jobs.csv'
final_jobs.to_csv(job_file, index=False, encoding='utf-8')
print(f"✅ Exported {len(final_jobs):,} clean jobs to: {job_file}")

# Create summary report
summary_report = {
    'processing_info': {
        'processed_date': processing_date,
        'source_files': {
            'resumes': '../data/raw/UpdatedResumeDataSet.csv',
            'jobs': '../data/raw/itviec_jobs_undetected.csv'
        },
        'output_files': {
            'clean_resumes': resume_file,
            'clean_jobs': job_file
        }
    },
    'data_statistics': {
        'resumes': {
            'total_records': len(final_resumes),
            'categories': final_resumes['category_clean'].nunique(),
            'avg_text_length': final_resumes['text_length'].mean(),
            'avg_skills': final_resumes['skills_count'].mean(),
            'avg_experience': final_resumes['experience_years'].mean()
        },
        'jobs': {
            'total_records': len(final_jobs),
            'companies': final_jobs['company_clean'].nunique(),
            'locations': final_jobs['location_clean'].nunique(),
            'avg_text_length': final_jobs['text_length'].mean(),
            'avg_skills': final_jobs['skills_count'].mean(),
            'avg_required_experience': final_jobs['required_experience'].mean()
        }
    }
}

# Save summary report
summary_file = '../data/preprocessing_summary.json'
import json
with open(summary_file, 'w', encoding='utf-8') as f:
    json.dump(summary_report, f, indent=2, ensure_ascii=False, default=str)

print(f"✅ Summary report saved to: {summary_file}")

print("\n🎉 DATA PREPROCESSING COMPLETED SUCCESSFULLY!")
print("="*60)
print(f"📊 Final Output:")
print(f"   • Clean Resumes: {len(final_resumes):,} records")
print(f"   • Clean Jobs: {len(final_jobs):,} records")
print(f"   • Processing Summary: {summary_file}")
print(f"\n🚀 Ready for ML training and analysis!")