{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 🧹 Data Preprocessing Pipeline\n", "## Clean and Prepare CR (Candidate Resumes) and JD (Job Descriptions)\n", "\n", "**Objective**: Transform raw data into clean, standardized format for ML training\n", "\n", "**Input**: \n", "- `data/raw/UpdatedResumeDataSet.csv` (Raw resume data)\n", "- `data/raw/itviec_jobs_undetected.csv` (Raw job data)\n", "\n", "**Output**: \n", "- `data/clean_resumes.csv` (Processed candidate resumes)\n", "- `data/clean_jobs.csv` (Processed job descriptions)"]}, {"cell_type": "markdown", "id": "setup", "metadata": {}, "source": ["## 🔧 Setup and Configuration"]}, {"cell_type": "code", "execution_count": 1, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully\n", "📅 Processing started at: 2025-06-17 22:04:50\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "import string\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Text processing libraries\n", "import nltk\n", "from nltk.corpus import stopwords\n", "from nltk.tokenize import word_tokenize\n", "from nltk.stem import PorterStemmer\n", "\n", "# Download required NLTK data\n", "try:\n", "    nltk.data.find('tokenizers/punkt')\n", "except LookupError:\n", "    nltk.download('punkt')\n", "    \n", "try:\n", "    nltk.data.find('corpora/stopwords')\n", "except LookupError:\n", "    nltk.download('stopwords')\n", "\n", "print(\"✅ Libraries imported successfully\")\n", "print(f\"📅 Processing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "id": "load-data", "metadata": {}, "source": ["## 📂 Load Raw Data"]}, {"cell_type": "code", "execution_count": 2, "id": "load-raw-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Loading raw datasets...\n", "✅ Loaded 962 resume records\n", "✅ Loaded 995 job records\n", "\n", "📋 Resume Data Structure:\n", "Columns: ['Category', 'Resume']\n", "Shape: (962, 2)\n", "Categories: 25 unique\n", "\n", "📋 Job Data Structure:\n", "Columns: ['id', 'title', 'company', 'location', 'salary', 'work_type', 'description', 'requirements', 'skills']\n", "Shape: (995, 9)\n", "\n", "📄 Sample Resume Categories:\n", "Category\n", "Java Developer        84\n", "Testing               70\n", "DevOps Engineer       55\n", "Python Developer      48\n", "Web Designing         45\n", "HR                    44\n", "<PERSON><PERSON>                42\n", "Blockchain            40\n", "ETL Developer         40\n", "Operations Manager    40\n", "Name: count, dtype: int64\n", "\n", "📄 Sample Job Titles:\n", "['MLops Engineer', 'Senior DevOps Engineer (Cloud, AWS)', 'VTS - <PERSON><PERSON><PERSON><PERSON> (Agile/ Azure)', 'VTS - <PERSON><PERSON> - Presales Engineer', 'VHT - Embedded Software Engineer (Linux, C++)', 'Quality Assurance Manager (QAM)', 'Platform Manager (CDN ecosystems)', 'Bridge Project Manager (BrSE/ IT Communicator)', 'Senior Process Quality Assurance (PQA, QA QC)', 'Hybrid - Ruby On Rails Developer (Ruby, SQL)']\n"]}], "source": ["# Load raw datasets\n", "print(\"📊 Loading raw datasets...\")\n", "\n", "try:\n", "    # Load resume data\n", "    raw_resumes = pd.read_csv('../data/raw/UpdatedResumeDataSet.csv')\n", "    print(f\"✅ Loaded {len(raw_resumes):,} resume records\")\n", "    \n", "    # Load job data\n", "    raw_jobs = pd.read_csv('../data/raw/itviec_jobs_undetected.csv')\n", "    print(f\"✅ Loaded {len(raw_jobs):,} job records\")\n", "    \n", "except FileNotFoundError as e:\n", "    print(f\"❌ Error loading data: {e}\")\n", "    print(\"Please ensure the raw data files are in the correct location\")\n", "\n", "# Display data structure\n", "print(\"\\n📋 Resume Data Structure:\")\n", "print(f\"Columns: {list(raw_resumes.columns)}\")\n", "print(f\"Shape: {raw_resumes.shape}\")\n", "print(f\"Categories: {raw_resumes['Category'].nunique()} unique\")\n", "\n", "print(\"\\n📋 Job Data Structure:\")\n", "print(f\"Columns: {list(raw_jobs.columns)}\")\n", "print(f\"Shape: {raw_jobs.shape}\")\n", "\n", "# Show sample data\n", "print(\"\\n📄 Sample Resume Categories:\")\n", "print(raw_resumes['Category'].value_counts().head(10))\n", "\n", "print(\"\\n📄 Sample Job Titles:\")\n", "print(raw_jobs['title'].head(10).tolist())"]}, {"cell_type": "markdown", "id": "text-cleaning", "metadata": {}, "source": ["## 🧼 Text Cleaning Functions"]}, {"cell_type": "code", "execution_count": 3, "id": "cleaning-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Text cleaning functions defined\n"]}], "source": ["# Text cleaning functions\n", "def clean_text(text):\n", "    \"\"\"\n", "    Comprehensive text cleaning function\n", "    \"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return ''\n", "    \n", "    # Convert to string and lowercase\n", "    text = str(text).lower()\n", "    \n", "    # Remove HTML tags\n", "    text = re.sub(r'<[^>]+>', ' ', text)\n", "    \n", "    # Remove URLs\n", "    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', ' ', text)\n", "    \n", "    # Remove email addresses\n", "    text = re.sub(r'\\S+@\\S+', ' ', text)\n", "    \n", "    # Remove phone numbers\n", "    text = re.sub(r'[\\+]?[1-9]?[0-9]{7,15}', ' ', text)\n", "    \n", "    # Remove special characters but keep spaces\n", "    text = re.sub(r'[^a-zA-Z0-9\\s]', ' ', text)\n", "    \n", "    # Remove extra whitespaces\n", "    text = re.sub(r'\\s+', ' ', text)\n", "    \n", "    # Remove leading/trailing spaces\n", "    text = text.strip()\n", "    \n", "    return text\n", "\n", "def extract_skills(text):\n", "    \"\"\"\n", "    Extract technical skills from text\n", "    \"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return []\n", "    \n", "    # Common technical skills (expanded list)\n", "    tech_skills = [\n", "        # Programming Languages\n", "        'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin',\n", "        'typescript', 'scala', 'r', 'matlab', 'perl', 'shell', 'bash',\n", "        \n", "        # Web Technologies\n", "        'html', 'css', 'react', 'angular', 'vue', 'nodejs', 'express', 'django', 'flask', 'spring',\n", "        'laravel', 'symfony', 'rails', 'asp.net', 'jquery', 'bootstrap',\n", "        \n", "        # Databases\n", "        'mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sql server', 'sqlite', 'cassandra',\n", "        'elasticsearch', 'neo4j', 'dynamodb',\n", "        \n", "        # Cloud & DevOps\n", "        'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'gitlab', 'github', 'terraform',\n", "        'ansible', 'chef', 'puppet', 'vagrant',\n", "        \n", "        # Data Science & AI\n", "        'machine learning', 'deep learning', 'artificial intelligence', 'data science', 'nlp',\n", "        'tensorflow', 'pytorch', 'keras', 'scikit-learn', 'pandas', 'numpy', 'matplotlib',\n", "        'seaborn', 'jupyter', 'tableau', 'power bi',\n", "        \n", "        # Mobile Development\n", "        'android', 'ios', 'react native', 'flutter', 'xamarin', 'ionic',\n", "        \n", "        # Other Technologies\n", "        'git', 'linux', 'windows', 'macos', 'agile', 'scrum', 'rest api', 'graphql', 'microservices',\n", "        'blockchain', 'iot', 'ar', 'vr'\n", "    ]\n", "    \n", "    text_lower = str(text).lower()\n", "    found_skills = []\n", "    \n", "    for skill in tech_skills:\n", "        if skill in text_lower:\n", "            found_skills.append(skill)\n", "    \n", "    return list(set(found_skills))  # Remove duplicates\n", "\n", "def extract_experience_years(text):\n", "    \"\"\"\n", "    Extract years of experience from text\n", "    \"\"\"\n", "    if pd.isna(text):\n", "        return 0\n", "    \n", "    text = str(text).lower()\n", "    \n", "    # Pattern to find experience mentions\n", "    patterns = [\n", "        r'(\\d+)\\+?\\s*years?\\s*(?:of\\s*)?experience',\n", "        r'experience\\s*(?:of\\s*)?(\\d+)\\+?\\s*years?',\n", "        r'(\\d+)\\+?\\s*years?\\s*in',\n", "        r'(\\d+)\\+?\\s*yrs?\\s*(?:of\\s*)?experience',\n", "    ]\n", "    \n", "    years = []\n", "    for pattern in patterns:\n", "        matches = re.findall(pattern, text)\n", "        years.extend([int(match) for match in matches])\n", "    \n", "    return max(years) if years else 0\n", "\n", "print(\"✅ Text cleaning functions defined\")"]}, {"cell_type": "markdown", "id": "process-resumes", "metadata": {}, "source": ["## 👤 Process Resume Data (CR)"]}, {"cell_type": "code", "execution_count": 4, "id": "clean-resumes", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Processing resume data...\n", "📝 Cleaning resume text...\n", "🎯 Extracting skills...\n", "💼 Extracting experience...\n", "📊 Calculating text statistics...\n", "✅ Resume processing completed\n", "📊 Records: 962 → 962 (removed 0 empty records)\n", "📋 Categories: 25 unique\n", "🎯 Average skills per resume: 7.0\n", "💼 Average experience: 0.4 years\n", "\n", "📄 Sample processed resume:\n", "Category: Devops Engineer\n", "Skills: git, r, github, tableau, matlab, azure, javascript, jquery, aws, scala, angular, shell, html, css, i...\n", "Experience: 0 years\n", "Text length: 7668 characters\n"]}], "source": ["# Process resume data\n", "print(\"🔄 Processing resume data...\")\n", "\n", "# Create clean resume dataframe\n", "clean_resumes = raw_resumes.copy()\n", "\n", "# Clean resume text\n", "print(\"📝 Cleaning resume text...\")\n", "clean_resumes['clean_text'] = clean_resumes['Resume'].apply(clean_text)\n", "\n", "# Extract skills\n", "print(\"🎯 Extracting skills...\")\n", "clean_resumes['extracted_skills'] = clean_resumes['Resume'].apply(extract_skills)\n", "clean_resumes['skills_count'] = clean_resumes['extracted_skills'].apply(len)\n", "clean_resumes['skills_text'] = clean_resumes['extracted_skills'].apply(lambda x: ', '.join(x))\n", "\n", "# Extract experience\n", "print(\"💼 Extracting experience...\")\n", "clean_resumes['experience_years'] = clean_resumes['Resume'].apply(extract_experience_years)\n", "\n", "# Calculate text statistics\n", "print(\"📊 Calculating text statistics...\")\n", "clean_resumes['text_length'] = clean_resumes['clean_text'].str.len()\n", "clean_resumes['word_count'] = clean_resumes['clean_text'].str.split().str.len()\n", "\n", "# Standardize category names\n", "clean_resumes['category_clean'] = clean_resumes['Category'].str.strip().str.title()\n", "\n", "# Remove rows with empty text\n", "initial_count = len(clean_resumes)\n", "clean_resumes = clean_resumes[clean_resumes['clean_text'].str.len() > 10]\n", "final_count = len(clean_resumes)\n", "\n", "print(f\"✅ Resume processing completed\")\n", "print(f\"📊 Records: {initial_count:,} → {final_count:,} (removed {initial_count-final_count:,} empty records)\")\n", "print(f\"📋 Categories: {clean_resumes['category_clean'].nunique()} unique\")\n", "print(f\"🎯 Average skills per resume: {clean_resumes['skills_count'].mean():.1f}\")\n", "print(f\"💼 Average experience: {clean_resumes['experience_years'].mean():.1f} years\")\n", "\n", "# Show sample processed data\n", "print(\"\\n📄 Sample processed resume:\")\n", "sample_idx = clean_resumes['skills_count'].idxmax()\n", "sample = clean_resumes.loc[sample_idx]\n", "print(f\"Category: {sample['category_clean']}\")\n", "print(f\"Skills: {sample['skills_text'][:100]}...\")\n", "print(f\"Experience: {sample['experience_years']} years\")\n", "print(f\"Text length: {sample['text_length']} characters\")"]}, {"cell_type": "markdown", "id": "process-jobs", "metadata": {}, "source": ["## 🏢 Process Job Data (JD)"]}, {"cell_type": "code", "execution_count": 5, "id": "clean-jobs", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Processing job data...\n", "📝 Combining job text fields...\n", "🧹 Cleaning job text...\n", "🎯 Extracting required skills...\n", "💼 Extracting experience requirements...\n", "📊 Calculating text statistics...\n", "✅ Job processing completed\n", "📊 Records: 995 → 995 (removed 0 incomplete records)\n", "🏢 Companies: 510 unique\n", "📍 Locations: 20 unique\n", "🎯 Average skills per job: 7.3\n", "💼 Average required experience: 0.4 years\n", "\n", "📄 Sample processed job:\n", "Title: Senior <PERSON> (Python)\n", "Company: Arsen Kawaijuku Tech Vietnam Co.,Ltd\n", "Location: Ho <PERSON>\n", "Required Skills: r, mysql, postgresql, react, azure, javascript, aws, scala, angular, mongodb, html, vr, css, typescr...\n", "Required Experience: 0 years\n", "Text length: 4631 characters\n"]}], "source": ["# Process job data\n", "print(\"🔄 Processing job data...\")\n", "\n", "# Create clean job dataframe\n", "clean_jobs = raw_jobs.copy()\n", "\n", "# Combine job text fields\n", "print(\"📝 Combining job text fields...\")\n", "clean_jobs['combined_text'] = (\n", "    clean_jobs['title'].fillna('') + ' ' +\n", "    clean_jobs['description'].fillna('') + ' ' +\n", "    clean_jobs['requirements'].fillna('') + ' ' +\n", "    clean_jobs['skills'].fillna('')\n", ")\n", "\n", "# Clean job text\n", "print(\"🧹 Cleaning job text...\")\n", "clean_jobs['clean_text'] = clean_jobs['combined_text'].apply(clean_text)\n", "\n", "# Extract skills from jobs\n", "print(\"🎯 Extracting required skills...\")\n", "clean_jobs['required_skills'] = clean_jobs['combined_text'].apply(extract_skills)\n", "clean_jobs['skills_count'] = clean_jobs['required_skills'].apply(len)\n", "clean_jobs['skills_text'] = clean_jobs['required_skills'].apply(lambda x: ', '.join(x))\n", "\n", "# Extract experience requirements\n", "print(\"💼 Extracting experience requirements...\")\n", "clean_jobs['required_experience'] = clean_jobs['combined_text'].apply(extract_experience_years)\n", "\n", "# Clean and standardize other fields\n", "clean_jobs['title_clean'] = clean_jobs['title'].fillna('').str.strip().str.title()\n", "clean_jobs['company_clean'] = clean_jobs['company'].fillna('').str.strip().str.title()\n", "clean_jobs['location_clean'] = clean_jobs['location'].fillna('').str.strip().str.title()\n", "\n", "# Process salary information\n", "def clean_salary(salary_text):\n", "    if pd.isna(salary_text) or salary_text == '':\n", "        return 'Not specified'\n", "    return str(salary_text).strip()\n", "\n", "clean_jobs['salary_clean'] = clean_jobs['salary'].apply(clean_salary)\n", "\n", "# Calculate text statistics\n", "print(\"📊 Calculating text statistics...\")\n", "clean_jobs['text_length'] = clean_jobs['clean_text'].str.len()\n", "clean_jobs['word_count'] = clean_jobs['clean_text'].str.split().str.len()\n", "\n", "# Remove jobs with insufficient information\n", "initial_count = len(clean_jobs)\n", "clean_jobs = clean_jobs[\n", "    (clean_jobs['clean_text'].str.len() > 20) &\n", "    (clean_jobs['title_clean'] != '') &\n", "    (clean_jobs['company_clean'] != '')\n", "]\n", "final_count = len(clean_jobs)\n", "\n", "print(f\"✅ Job processing completed\")\n", "print(f\"📊 Records: {initial_count:,} → {final_count:,} (removed {initial_count-final_count:,} incomplete records)\")\n", "print(f\"🏢 Companies: {clean_jobs['company_clean'].nunique()} unique\")\n", "print(f\"📍 Locations: {clean_jobs['location_clean'].nunique()} unique\")\n", "print(f\"🎯 Average skills per job: {clean_jobs['skills_count'].mean():.1f}\")\n", "print(f\"💼 Average required experience: {clean_jobs['required_experience'].mean():.1f} years\")\n", "\n", "# Show sample processed data\n", "print(\"\\n📄 Sample processed job:\")\n", "sample_idx = clean_jobs['skills_count'].idxmax()\n", "sample = clean_jobs.loc[sample_idx]\n", "print(f\"Title: {sample['title_clean']}\")\n", "print(f\"Company: {sample['company_clean']}\")\n", "print(f\"Location: {sample['location_clean']}\")\n", "print(f\"Required Skills: {sample['skills_text'][:100]}...\")\n", "print(f\"Required Experience: {sample['required_experience']} years\")\n", "print(f\"Text length: {sample['text_length']} characters\")"]}, {"cell_type": "markdown", "id": "data-quality", "metadata": {}, "source": ["## 📊 Data Quality Analysis"]}, {"cell_type": "code", "execution_count": 6, "id": "quality-analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 Analyzing data quality...\n", "\n", "👤 RESUME DATA QUALITY:\n", "📊 Total records: 962\n", "📋 Categories: 25\n", "📝 Average text length: 2915 characters\n", "🎯 Records with skills: 962 (100.0%)\n", "💼 Records with experience: 89 (9.3%)\n", "\n", "📈 Resume Categories Distribution:\n", "  • Java Developer: 84 (8.7%)\n", "  • Testing: 70 (7.3%)\n", "  • Devops Engineer: 55 (5.7%)\n", "  • Python Developer: 48 (5.0%)\n", "  • Web Designing: 45 (4.7%)\n", "  • Hr: 44 (4.6%)\n", "  • Hadoop: 42 (4.4%)\n", "  • Blockchain: 40 (4.2%)\n", "  • Etl Developer: 40 (4.2%)\n", "  • Operations Manager: 40 (4.2%)\n", "\n", "🏢 JOB DATA QUALITY:\n", "📊 Total records: 995\n", "🏢 Companies: 510\n", "📍 Locations: 20\n", "📝 Average text length: 1396 characters\n", "🎯 Jobs with skills: 995 (100.0%)\n", "💼 Jobs with experience req: 110 (11.1%)\n", "\n", "📈 Top Job Locations:\n", "  • <PERSON>: 522 (52.5%)\n", "  • Ha Noi: 350 (35.2%)\n", "  • Da <PERSON>: 26 (2.6%)\n", "  • <PERSON> - <PERSON>: 24 (2.4%)\n", "  • <PERSON>: 18 (1.8%)\n", "  • <PERSON> <PERSON> <PERSON>: 13 (1.3%)\n", "  • <PERSON> <PERSON> <PERSON>: 7 (0.7%)\n", "  • <PERSON> - <PERSON>: 7 (0.7%)\n", "  • Others: 5 (0.5%)\n", "  • <PERSON> - <PERSON>: 5 (0.5%)\n", "\n", "📈 Top Companies:\n", "  • Mb Bank: 26 (2.6%)\n", "  • Crossian: 14 (1.4%)\n", "  • Viettel Group: 13 (1.3%)\n", "  • Tymex: 13 (1.3%)\n", "  • Nab Innovation Centre Vietnam: 10 (1.0%)\n", "  • Hdbank: 9 (0.9%)\n", "  • Saigon Technology: 8 (0.8%)\n", "  • Money Forward Vietnam Co.,Ltd: 8 (0.8%)\n", "  • Lg Cns Việt Nam: 8 (0.8%)\n", "  • <PERSON><PERSON> Tnhh Mtv Việt Nam Hiện Đạ<PERSON> (Mbv): 7 (0.7%)\n", "\n", "🎯 SKILLS ANALYSIS:\n", "📊 Unique skills in resumes: 75\n", "📊 Unique skills in jobs: 77\n", "\n", "🔥 Top 10 Skills in Resumes:\n", "  • r: 962\n", "  • ar: 940\n", "  • go: 609\n", "  • java: 327\n", "  • windows: 306\n", "  • html: 244\n", "  • mysql: 225\n", "  • python: 176\n", "  • oracle: 175\n", "  • css: 154\n", "\n", "🔥 Top 10 Skills in Jobs:\n", "  • r: 995\n", "  • ar: 709\n", "  • java: 429\n", "  • go: 384\n", "  • agile: 312\n", "  • php: 259\n", "  • aws: 244\n", "  • python: 203\n", "  • scrum: 203\n", "  • nodejs: 194\n", "\n", "✅ Data quality analysis completed\n"]}], "source": ["# Data quality analysis\n", "print(\"🔍 Analyzing data quality...\")\n", "\n", "# Resume data quality\n", "print(\"\\n👤 RESUME DATA QUALITY:\")\n", "print(f\"📊 Total records: {len(clean_resumes):,}\")\n", "print(f\"📋 Categories: {clean_resumes['category_clean'].nunique()}\")\n", "print(f\"📝 Average text length: {clean_resumes['text_length'].mean():.0f} characters\")\n", "print(f\"🎯 Records with skills: {(clean_resumes['skills_count'] > 0).sum():,} ({(clean_resumes['skills_count'] > 0).mean()*100:.1f}%)\")\n", "print(f\"💼 Records with experience: {(clean_resumes['experience_years'] > 0).sum():,} ({(clean_resumes['experience_years'] > 0).mean()*100:.1f}%)\")\n", "\n", "print(\"\\n📈 Resume Categories Distribution:\")\n", "category_dist = clean_resumes['category_clean'].value_counts().head(10)\n", "for cat, count in category_dist.items():\n", "    print(f\"  • {cat}: {count:,} ({count/len(clean_resumes)*100:.1f}%)\")\n", "\n", "# Job data quality\n", "print(\"\\n🏢 JOB DATA QUALITY:\")\n", "print(f\"📊 Total records: {len(clean_jobs):,}\")\n", "print(f\"🏢 Companies: {clean_jobs['company_clean'].nunique()}\")\n", "print(f\"📍 Locations: {clean_jobs['location_clean'].nunique()}\")\n", "print(f\"📝 Average text length: {clean_jobs['text_length'].mean():.0f} characters\")\n", "print(f\"🎯 Jobs with skills: {(clean_jobs['skills_count'] > 0).sum():,} ({(clean_jobs['skills_count'] > 0).mean()*100:.1f}%)\")\n", "print(f\"💼 Jobs with experience req: {(clean_jobs['required_experience'] > 0).sum():,} ({(clean_jobs['required_experience'] > 0).mean()*100:.1f}%)\")\n", "\n", "print(\"\\n📈 Top Job Locations:\")\n", "location_dist = clean_jobs['location_clean'].value_counts().head(10)\n", "for loc, count in location_dist.items():\n", "    print(f\"  • {loc}: {count:,} ({count/len(clean_jobs)*100:.1f}%)\")\n", "\n", "print(\"\\n📈 Top Companies:\")\n", "company_dist = clean_jobs['company_clean'].value_counts().head(10)\n", "for comp, count in company_dist.items():\n", "    print(f\"  • {comp}: {count:,} ({count/len(clean_jobs)*100:.1f}%)\")\n", "\n", "# Skills analysis\n", "print(\"\\n🎯 SKILLS ANALYSIS:\")\n", "from collections import Counter\n", "\n", "# Flatten all skills\n", "all_resume_skills = [skill for skills_list in clean_resumes['extracted_skills'] for skill in skills_list]\n", "all_job_skills = [skill for skills_list in clean_jobs['required_skills'] for skill in skills_list]\n", "\n", "resume_skill_counts = Counter(all_resume_skills)\n", "job_skill_counts = Counter(all_job_skills)\n", "\n", "print(f\"📊 Unique skills in resumes: {len(resume_skill_counts)}\")\n", "print(f\"📊 Unique skills in jobs: {len(job_skill_counts)}\")\n", "\n", "print(\"\\n🔥 Top 10 Skills in Resumes:\")\n", "for skill, count in resume_skill_counts.most_common(10):\n", "    print(f\"  • {skill}: {count:,}\")\n", "\n", "print(\"\\n🔥 Top 10 Skills in Jobs:\")\n", "for skill, count in job_skill_counts.most_common(10):\n", "    print(f\"  • {skill}: {count:,}\")\n", "\n", "print(\"\\n✅ Data quality analysis completed\")"]}, {"cell_type": "markdown", "id": "export-data", "metadata": {}, "source": ["## 💾 Export Clean Data"]}, {"cell_type": "code", "execution_count": 7, "id": "export-clean-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📤 Preparing data for export...\n", "💾 Exporting clean datasets...\n", "✅ Exported 962 clean resumes to: ../data/clean_resumes.csv\n", "✅ Exported 995 clean jobs to: ../data/clean_jobs.csv\n", "✅ Summary report saved to: ../data/preprocessing_summary.json\n", "\n", "🎉 DATA PREPROCESSING COMPLETED SUCCESSFULLY!\n", "============================================================\n", "📊 Final Output:\n", "   • Clean Resumes: 962 records\n", "   • Clean Jobs: 995 records\n", "   • Processing Summary: ../data/preprocessing_summary.json\n", "\n", "🚀 Ready for ML training and analysis!\n"]}], "source": ["# Prepare final datasets for export\n", "print(\"📤 Preparing data for export...\")\n", "\n", "# Select columns for final resume dataset\n", "final_resumes = clean_resumes[[\n", "    'Category',                    # Original category\n", "    'category_clean',             # Cleaned category\n", "    'Resume',                     # Original resume text\n", "    'clean_text',                 # Cleaned resume text\n", "    'extracted_skills',           # List of extracted skills\n", "    'skills_text',                # Skills as comma-separated text\n", "    'skills_count',               # Number of skills\n", "    'experience_years',           # Years of experience\n", "    'text_length',                # Text length\n", "    'word_count'                  # Word count\n", "]].copy()\n", "\n", "# Select columns for final job dataset\n", "final_jobs = clean_jobs[[\n", "    'id',                         # Job ID\n", "    'title',                      # Original title\n", "    'title_clean',                # Cleaned title\n", "    'company',                    # Original company\n", "    'company_clean',              # Cleaned company\n", "    'location',                   # Original location\n", "    'location_clean',             # Cleaned location\n", "    'salary',                     # Original salary\n", "    'salary_clean',               # Cleaned salary\n", "    'work_type',                  # Work type\n", "    'description',                # Original description\n", "    'requirements',               # Original requirements\n", "    'skills',                     # Original skills\n", "    'combined_text',              # Combined text\n", "    'clean_text',                 # Cleaned combined text\n", "    'required_skills',            # List of required skills\n", "    'skills_text',                # Skills as comma-separated text\n", "    'skills_count',               # Number of required skills\n", "    'required_experience',        # Required years of experience\n", "    'text_length',                # Text length\n", "    'word_count'                  # Word count\n", "]].copy()\n", "\n", "# Add metadata\n", "processing_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "final_resumes['processed_date'] = processing_date\n", "final_jobs['processed_date'] = processing_date\n", "\n", "# Export to CSV\n", "print(\"💾 Exporting clean datasets...\")\n", "\n", "# Export resumes\n", "resume_file = '../data/clean_resumes.csv'\n", "final_resumes.to_csv(resume_file, index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(final_resumes):,} clean resumes to: {resume_file}\")\n", "\n", "# Export jobs\n", "job_file = '../data/clean_jobs.csv'\n", "final_jobs.to_csv(job_file, index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(final_jobs):,} clean jobs to: {job_file}\")\n", "\n", "# Create summary report\n", "summary_report = {\n", "    'processing_info': {\n", "        'processed_date': processing_date,\n", "        'source_files': {\n", "            'resumes': '../data/raw/UpdatedResumeDataSet.csv',\n", "            'jobs': '../data/raw/itviec_jobs_undetected.csv'\n", "        },\n", "        'output_files': {\n", "            'clean_resumes': resume_file,\n", "            'clean_jobs': job_file\n", "        }\n", "    },\n", "    'data_statistics': {\n", "        'resumes': {\n", "            'total_records': len(final_resumes),\n", "            'categories': final_resumes['category_clean'].nunique(),\n", "            'avg_text_length': final_resumes['text_length'].mean(),\n", "            'avg_skills': final_resumes['skills_count'].mean(),\n", "            'avg_experience': final_resumes['experience_years'].mean()\n", "        },\n", "        'jobs': {\n", "            'total_records': len(final_jobs),\n", "            'companies': final_jobs['company_clean'].nunique(),\n", "            'locations': final_jobs['location_clean'].nunique(),\n", "            'avg_text_length': final_jobs['text_length'].mean(),\n", "            'avg_skills': final_jobs['skills_count'].mean(),\n", "            'avg_required_experience': final_jobs['required_experience'].mean()\n", "        }\n", "    }\n", "}\n", "\n", "# Save summary report\n", "summary_file = '../data/preprocessing_summary.json'\n", "import json\n", "with open(summary_file, 'w', encoding='utf-8') as f:\n", "    json.dump(summary_report, f, indent=2, ensure_ascii=False, default=str)\n", "\n", "print(f\"✅ Summary report saved to: {summary_file}\")\n", "\n", "print(\"\\n🎉 DATA PREPROCESSING COMPLETED SUCCESSFULLY!\")\n", "print(\"=\"*60)\n", "print(f\"📊 Final Output:\")\n", "print(f\"   • Clean Resumes: {len(final_resumes):,} records\")\n", "print(f\"   • Clean Jobs: {len(final_jobs):,} records\")\n", "print(f\"   • Processing Summary: {summary_file}\")\n", "print(f\"\\n🚀 Ready for ML training and analysis!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}