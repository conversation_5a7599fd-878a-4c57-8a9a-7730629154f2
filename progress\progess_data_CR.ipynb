{"cells": [{"cell_type": "code", "execution_count": 22, "id": "e22a1aa4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from langdetect import detect\n", "from deep_translator import GoogleTranslator\n", "import re\n", "import spacy\n", "from underthesea import word_tokenize, pos_tag\n", "from nltk.corpus import stopwords\n", "import numpy as np\n", "from collections import defaultdict\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from collections import Counter\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "c77272f1", "metadata": {}, "outputs": [], "source": ["nlp = spacy.load('en_core_web_md')\n", "stop_words = set(stopwords.words('english'))"]}, {"cell_type": "code", "execution_count": 3, "id": "9ad185d1", "metadata": {}, "outputs": [], "source": ["csv_cr = \"../data/raw/UpdatedResumeDataSet.csv\""]}, {"cell_type": "code", "execution_count": 4, "id": "edef4822", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(csv_cr)"]}, {"cell_type": "code", "execution_count": 5, "id": "61c435a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Resume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Data Science</td>\n", "      <td>Skills * Programming Languages: Python (pandas...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\nMay 2013 to May 2017 B.E...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Data Science</td>\n", "      <td>Areas of Interest Deep Learning, Control Syste...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Data Science</td>\n", "      <td>Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\n MCA   YMCAUST,  Faridab...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>957</th>\n", "      <td>Testing</td>\n", "      <td>Computer Skills: â¢ Proficient in MS office (...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>958</th>\n", "      <td>Testing</td>\n", "      <td>â Willingness to accept the challenges. â ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>959</th>\n", "      <td>Testing</td>\n", "      <td>PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>960</th>\n", "      <td>Testing</td>\n", "      <td>COMPUTER SKILLS &amp; SOFTWARE KNOWLEDGE MS-Power ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>961</th>\n", "      <td>Testing</td>\n", "      <td>Skill Set OS Windows XP/7/8/8.1/10 Database MY...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>962 rows × 2 columns</p>\n", "</div>"], "text/plain": ["         Category                                             Resume\n", "0    Data Science  Skills * Programming Languages: Python (pandas...\n", "1    Data Science  Education Details \\r\\nMay 2013 to May 2017 B.E...\n", "2    Data Science  Areas of Interest Deep Learning, Control Syste...\n", "3    Data Science  Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...\n", "4    Data Science  Education Details \\r\\n MCA   YMCAUST,  Faridab...\n", "..            ...                                                ...\n", "957       Testing  Computer Skills: â¢ Proficient in MS office (...\n", "958       Testing  â Willingness to accept the challenges. â ...\n", "959       Testing  PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...\n", "960       Testing  COMPUTER SKILLS & SOFTWARE KNOWLEDGE MS-Power ...\n", "961       Testing  Skill Set OS Windows XP/7/8/8.1/10 Database MY...\n", "\n", "[962 rows x 2 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 6, "id": "32a9da5d", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Data Science', 'HR', 'Advocate', 'Arts', 'Web Designing',\n", "       'Mechanical Engineer', 'Sales', 'Health and fitness',\n", "       'Civil Engineer', 'Java Developer', 'Business Analyst',\n", "       'SAP Developer', 'Automation Testing', 'Electrical Engineering',\n", "       'Operations Manager', 'Python Developer', 'DevOps Engineer',\n", "       'Network Security Engineer', 'PM<PERSON>', 'Database', 'Hadoop',\n", "       'ETL Developer', 'DotNet Developer', 'Blockchain', 'Testing'],\n", "      dtype=object)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Category'].unique()"]}, {"cell_type": "code", "execution_count": 7, "id": "85b88a7a", "metadata": {}, "outputs": [], "source": ["list_job_to_remove = ['Sales','Health and fitness','PMO','Arts']\n", "\n", "df = df[~df['Category'].isin(list_job_to_remove)]"]}, {"cell_type": "code", "execution_count": 8, "id": "49e5393e", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Data Science', 'HR', 'Advocate', 'Web Designing',\n", "       'Mechanical Engineer', 'Civil Engineer', 'Java Developer',\n", "       'Business Analyst', 'SAP Developer', 'Automation Testing',\n", "       'Electrical Engineering', 'Operations Manager', 'Python Developer',\n", "       'DevOps Engineer', 'Network Security Engineer', 'Database',\n", "       'Hadoop', 'ETL Developer', 'DotNet Developer', 'Blockchain',\n", "       'Testing'], dtype=object)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Category'].unique()"]}, {"cell_type": "code", "execution_count": 9, "id": "0a31f2ac", "metadata": {}, "outputs": [], "source": ["def clean_text(text):\n", "    text = text.lower()\n", "    text = re.sub(r'http\\S+|#\\S+|@\\S+|[^\\w\\s]|[\\*\\•\\n]', ' ', text)\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    doc = nlp(text)\n", "    tokens = [token.lemma_ for token in doc if token.text not in stop_words]\n", "    return ' '.join(tokens)"]}, {"cell_type": "code", "execution_count": 10, "id": "56d74156", "metadata": {}, "outputs": [], "source": ["df['resume_cleaned'] = df['Resume'].apply(clean_text)"]}, {"cell_type": "code", "execution_count": 11, "id": "3d13cf92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Resume</th>\n", "      <th>resume_cleaned</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Data Science</td>\n", "      <td>Skills * Programming Languages: Python (pandas...</td>\n", "      <td>skill programming language python pandas numpy...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\nMay 2013 to May 2017 B.E...</td>\n", "      <td>education detail may 2013 may 2017 b e uit rgp...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Data Science</td>\n", "      <td>Areas of Interest Deep Learning, Control Syste...</td>\n", "      <td>area interest deep learning control system des...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Data Science</td>\n", "      <td>Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...</td>\n", "      <td>skill â r â python â sap hana â tableau â sap ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\n MCA   YMCAUST,  Faridab...</td>\n", "      <td>education detail mca ymcaust faridabad haryana...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>957</th>\n", "      <td>Testing</td>\n", "      <td>Computer Skills: â¢ Proficient in MS office (...</td>\n", "      <td>computer skill â proficient ms office word bas...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>958</th>\n", "      <td>Testing</td>\n", "      <td>â Willingness to accept the challenges. â ...</td>\n", "      <td>â willingness accept challenge â positive thin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>959</th>\n", "      <td>Testing</td>\n", "      <td>PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...</td>\n", "      <td>personal skill â quick learner â eagerness lea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>960</th>\n", "      <td>Testing</td>\n", "      <td>COMPUTER SKILLS &amp; SOFTWARE KNOWLEDGE MS-Power ...</td>\n", "      <td>computer skill software knowledge ms power poi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>961</th>\n", "      <td>Testing</td>\n", "      <td>Skill Set OS Windows XP/7/8/8.1/10 Database MY...</td>\n", "      <td>skill set os windows xp 7 8 8 1 10 database my...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>826 rows × 3 columns</p>\n", "</div>"], "text/plain": ["         Category                                             Resume  \\\n", "0    Data Science  Skills * Programming Languages: Python (pandas...   \n", "1    Data Science  Education Details \\r\\nMay 2013 to May 2017 B.E...   \n", "2    Data Science  Areas of Interest Deep Learning, Control Syste...   \n", "3    Data Science  Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...   \n", "4    Data Science  Education Details \\r\\n MCA   YMCAUST,  Faridab...   \n", "..            ...                                                ...   \n", "957       Testing  Computer Skills: â¢ Proficient in MS office (...   \n", "958       Testing  â Willingness to accept the challenges. â ...   \n", "959       Testing  PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...   \n", "960       Testing  COMPUTER SKILLS & SOFTWARE KNOWLEDGE MS-Power ...   \n", "961       Testing  Skill Set OS Windows XP/7/8/8.1/10 Database MY...   \n", "\n", "                                        resume_cleaned  \n", "0    skill programming language python pandas numpy...  \n", "1    education detail may 2013 may 2017 b e uit rgp...  \n", "2    area interest deep learning control system des...  \n", "3    skill â r â python â sap hana â tableau â sap ...  \n", "4    education detail mca ymcaust faridabad haryana...  \n", "..                                                 ...  \n", "957  computer skill â proficient ms office word bas...  \n", "958  â willingness accept challenge â positive thin...  \n", "959  personal skill â quick learner â eagerness lea...  \n", "960  computer skill software knowledge ms power poi...  \n", "961  skill set os windows xp 7 8 8 1 10 database my...  \n", "\n", "[826 rows x 3 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 12, "id": "70fabb74", "metadata": {}, "outputs": [], "source": ["\n", "def read_skills(file_path):\n", "    skills = []\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            if line and not line.startswith('#'):\n", "                skills.append(line.lower())\n", "    return skills\n", "def extract_primary_skills(text):\n", "    # <PERSON><PERSON> lý trư<PERSON><PERSON> hợp text không phải chuỗi\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in primary_skills if skill in text.lower()]\n", "\n", "def extract_secondary_skills(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in secondary_skills if skill in text.lower()]\n", "\n", "def extract_adjectives(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADJ']))\n", "\n", "def extract_adverbs(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADV']))\n"]}, {"cell_type": "code", "execution_count": 27, "id": "3a55dcaf", "metadata": {}, "outputs": [], "source": ["primary_skills = read_skills('../data/primary_skills.txt')\n", "secondary_skills = read_skills('../data/secondary_skills.txt')"]}, {"cell_type": "code", "execution_count": 28, "id": "3335400e", "metadata": {}, "outputs": [], "source": ["df['primary_skills'] = df['resume_cleaned'].apply(extract_primary_skills)\n", "df['secondary_skills'] = df['resume_cleaned'].apply(extract_secondary_skills)\n", "df['adjectives'] = df['resume_cleaned'].apply(extract_adjectives)\n", "df['adverbs'] = df['resume_cleaned'].apply(extract_adverbs)"]}, {"cell_type": "code", "execution_count": null, "id": "f7e914f3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7524\\1403453381.py:12: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKUAAAMWCAYAAAAgRDUeAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAifRJREFUeJzs3QeYVdXZP+yHDiIDKhpQsRARsSBGwEJULLEba2LUNxbsHbtEsb/B3qOxJGLsHY0tVuyioqixIBaEqAkGFawUOd+11vud+c/QEdhnBu77urZzzt777L1OA+bns57doFQqlQIAAAAACtSwyJMBAAAAQCKUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAA6oRXX301zjzzzBg/fnylhwJAAYRSAAAU4h//+Eecc8458eOPP0ZdMGTIkGjfvn088MADsSB4/PHHY6mllopHH3006qMvv/wydtxxx2jVqlVUVVVVejgAFEAoBQDUKffdd1/+xfr888/P9/fYY4+444475uk5jj/++Nhtt93i448/jnfeeSfWWmutePDBB6Mu+Oabb+K///1vXho0aJDHOm7cuKjv/vWvf8Xuu+8e11xzTVx44YUVHcuIESOiadOmsdNOO8Xf//73uP322/PrXQnpPT7ssMNmud/AgQPzviNHjpxm20svvRQNGzaMgw8+OD+fG2+8McaOHTufRjzrMb7yyiuz/Zg999wzjz1VR6XQ8vDDD4++ffvO13ECUHcIpQCgQOkXttlZBg8ePF/HMXr06Dj99NOjZ8+esdhii0Xbtm2jd+/e8dhjj013/6+++ioOOOCAWHLJJaNly5ax8cYb52k2syMdd/XVV59uVcciiywSv/jFL+KLL76oXn/WWWfF73//+zj33HPzL9vDhw+Pjh07xry03377xZNPPhkrrLBCrLrqqtGiRYvYdNNNoy5IAUV6ndOSnHfeebH99ttHfZc+P0ceeWQ8/PDDOXBM72ulHHfccXk8K620Utx///1x/fXX5+9AfVQqlfLretJJJ+Xv0/PPP5+fzxJLLBF13dNPPx0PPfRQXlJQuc4668Sxxx5ba5+bb745Lr744oqNEYD5q0Ep/U0GABQiVTDU9Le//S1Ptbnhhhtqrf/Vr34VP/vZz+bbOC6//PJcgbPDDjtEr169YvLkyXksKWj661//Gvvss0/1vlOmTIkNNtggXn/99fzLfPrl/YorrsjB1tChQ6NTp06zDKVSFco///nP6nVPPPFEbLvtttG5c+ccTi2++OLV29LUnc8//zzvkypZUmj13HPP5bBuXvruu+/izTffzIFUCs1StUZd8Pbbb8enn35a/TlIAV0KHdZee+2orz755JMcLhxzzDH5dU7hZ3r9f/3rXxc+lhT47rvvvvHGG2/Ev//971hvvfVi2LBhsfTSS0clpM/1oYcemr+TM5OmPE6aNCmaNWtW67tw00035UAnTUV86623YvPNN8+foUqEUqlSKv3Z8fLLL0f37t1nuf/++++fPwPbbbddXH311fHZZ5/FqaeeWmuf9OdE+rNjehViANR/QikAqHBVzJ/+9Kdc7VCk9MtrCr1qVodMmDAhunXrlqePpcCpLE1t2nXXXfMUul122SWvS6HRyiuvHFtttVUOG+YklHrqqadi6623zmFWCqSm/uX5gw8+iJ///Of5dpq2lkKquhIYFS2FD+mX9NNOO222H5OCvrvuumu61WllAwYMyNVpqQqLyprdUKo+mNNQanYIpQAWbAvnv/AAoA779ttvc0VJhw4dclVEChnSdKepg6tyL5pUKZH2ad68ea6mSVNiZmW11VabZrpSOlcKi1Lvn6+//rp6/Z133pkDrFS1VJamlv32t7+Ne++9N4dZs+uZZ56JbbbZJk+bStUyNQOpjTbaKI+/HEglrVu3zoFUGu+sptelqXjpF9hnn302T0tMr0ea9pcqwGpKgdpRRx2Vj5mmIqZzpHAtVYJNXVGTXuMUyv3v//5vLLvssvmYaRzvv//+NOdP4WI6X6q8SudPzzUFcmmZlfQapjGl1zWFcKl6JL0PU9t7773z85xa//7981hT5c/EiRNzFVLy5z//OT/P9N6mSqDUqytVh6V+Q+lzVpaqbNJ7n6Zypteka9eucckll8xwvB9++GE+30UXXTTNtjR9LG275ZZbalVK9enTJ3+O0ljSmFJF3uz0TCq/D7MzpTWdJ1VBpeeazrPiiivmPkvpNUlSEJemh62xxhqx6KKL5mba03vv53YsKURM+6XPSXrP2rRpkz9nKbApvzdTGzRoUA4Sy69PmuY4O2NKU99SJWN639JnJ32/Uuhc04w+hzP6PP3U71bNz/PRRx9dPd03NS9P37ua0p8daazl9yp971NfqZpN8NOYUxP61PutPLV5dsYLQP0hlAKAOiQFTymQSL/sb7nllnlaTgqc0rS59Eve1FLVUWoK/D//8z9xxhln5LAhPa7mVLk5kUKN1JcmLWWvvfZankI3dbVS+uU0/YL93nvvzdax0xS8FHykoCBVSE0diqVpamn6YGo8XlNal6Yjpec4KykESNVcadrbBRdckEOW9It3zV/SUy+j1Aw69WlKr3MKKVIokUKx8rS5ms4+++y455578n79+vWLF198MTdfr+nKK6/MAWEKrlIvrBQSpKmR0wuWZtTjKvXNSVOv0vmaNGmSf2GfXeX3Jl1JLgUwKdxIvblSIJN+6U+vxc4775yDgRQ4pfvpNUrS9NENN9wwv8ZpmmDalnqGpV5LM5ICiTTtMwWiU0vrUjhS7oP1n//8J9Zdd90cQqbXKIVdKZRM4dG87BWU3rv0mbz11ltzZd+ll16aP1PpO1IOglKYlsKfFLCk71b6XqWQbkbv/dxKwW0KeFNlWrqdgqXUy21qKew55JBD4ne/+13+/Pzwww/5/ZpVs/I07Td9TlLAlq5qmMLJ9D7+8pe/nOeVRbPz3SpLzcrTdypV+ZWbr0/dzD29Fmnc6c+19JlIgfQpp5wSJ554YvU+qU9Wqt5Mf1ak55oW/aUAFjBp+h4AUBmHHnpoKn+qvj9o0KB8/6yzzqq13y677FJq0KBB6f33369el/ZLyyuvvFK97uOPPy41b968tOOOO87xWEaMGJEf+/vf/77W+pYtW5b69Okzzf4PPPBAPv/DDz880+NutNFGpcUXX7zUqlWr0mqrrVYaM2bMdPf76quv8vn/8Ic/1Fp/1FFHlVq0aFEaN27cTM+z/PLL5/E8/fTT1evSuZo1a1Y65phjqtd99913pR9//LHWYz/44IO83xlnnFG97sknn8zH69KlS2nChAnV6y+55JK8/s0338z307Ylllii1KNHj9KkSZOq9xs4cGDeLz3/mRk2bFje75BDDqm1fvfdd8/rTz311Op1e+21V36eU0v7pH3/9a9/lb7++uv8vJs2bVrafPPNaz3Xyy+/PO93+umn5/uTJ08urbjiivmYX375Za1jTpkyZabjvuqqq/Kx3nnnnep1EydOLLVt2zaPs2zfffcttW/fvvTf//631uN/97vflVq3bp3fj+S6667Lx/voo49q7Vd+H9LPmdlzzz1LDRs2LL388svTbCs/lx9++GGa9z6db+r3fm7HUn4/pv7epO9l+qzUlPZL71XN7/brr7+e11922WUzHFN6n9u0aVPaf//9ax3v3//+d35da65Pn8HpfQ5n9Hn6qd+t8hg322yzWp+f9B1u1KhR/o6Xld/3mg488MDSIosskt+nsm222Wa2xghA/aRSCgDqkAcffDAaNWoURxxxRK31aTpf+v01TdWpKTVprtkAe7nllssVKunS6jWnwcxKqiT5zW9+k6eepUqdmr7//vs8vWZqaQpPefuspKliqWIkTd9KU6amJ01vSlViqUdVeapieg6p8iWtn9HjakpX0ktVSmVp+lCqNEsVMmXpOdas+kpTjVI1UZcuXaZ7RcE05app06bV98vHLx/zlVdeyRUtqWlz48aNq/dL1VSpmmR23vNk6vc8VcDNqWWWWSZXn6SqpFQxlY5R87mmMabXMTX5LlfBffTRR3m/NMWsplk1lk+VP+kzULNaKn3uUv+wclVbeh9Tf6vUyDrdTtvKyxZbbJF7hs3uVRxnJjXjTxVQ6TzT62VUfi7pc1x+PdJnK71v6fVKn5F5MY6pHXTQQbXup89OOuf48eNrrd9ss81qTVtN1Wzpfar5uZ1aqnBLV8Xcbbfdar2u6c+PdBW7dHXJeWl2vltl6cqGNT8/6XHp9U7T8Gp+D8vSnw1p7Gm/9GfRu+++O0/HDkDdJZQCgDok/dKWApI0/ammFJiUt9c0vSvfpQbk6Re7qXu4zEj6ZTFNG0rTflL/qKmvQpZ+eZxe36g0xai8fVbSdK00vShdUS/9Ej2jwCxNt0rTjtJ0piRN80tX5ErrZ0cK5aaWgqEvv/yy+n4KR1KvpTQtKAUSKVhJzyFdgS2FJLM6ZjloKh+z/J6k51hTCqhmp/9NenwKSmqGEkn6hf+nKo9p6mOkcC1NvStvT03lk5k1RZ+RFGKlEKhmo/sUUKVgbJNNNsn302cwBSfpymopxKi5lK/wOGbMmJhb6Twp6JnV80jhVZqymb43KaBK08LSWFJIN733fm7N6rMzo/3K+069X00jRozIP9NrPfVr+8gjj8yT1/WnjnF2nnea9pd6TaUwOgVwadzlMHN+vBcA1E3/73/nAQALpVQ9k/oHpUChHCbUlPoUpWBoauV1U4dYM3L88cfnKpHUMyed8y9/+cs01TipH1b65fTGG2/MVRPpZ7qfqmpmR6oSmZ6aTeJTOJZ6Q6UePqmxcmq2nkKhVN2RQoufcsyizKh6aU6q4ualPffcM1+VMTU3T83D77vvvvy6lquRyq9nChv22muv6R4jVQUV9dz++Mc/5r5Lqel6eu8XX3zxPNZUKVbzvZ9XY5ndz85P+YyVx5v6LLVr126a7TWr9tLzmd6x5uT5zMkYZ7VvCipTH68URqVeeCmQTeFwqlY74YQTpvs9BGDBJJQCgDpk+eWXz1Ov0nSWmtVS5eksafv0qiVqSo3HU6PyFObMSmr0fN111+XmwamCaXpSRVG6klz6RbHmVLB0xbZ0nlSZNbtSIJSugHbttdfm6onUMHnqX6TTONIv2uUG46mipuYv2HPrtttuy1fQS1fLqykFZimkmFPl9yQ1gk4NwssmT56cq77KocvMHp9e21S1VLOyKTVkn1p6zdIv9FObuoKuPKZ0jFQZVZam9KXpemm6WFKuzkqN8cvr5kQ5REyBZpoylir0ala1la8mmMKPWR2/XE0z9fOb+rlNTzpPCjhm1eA/VQKm9ygFojWlc9ZsvD83YylK+b1LV1Scndd2etPsKvV80tUL0/ft7rvvzk32y9Jnc2qzmkYKQP1m+h4A1CHp6nTpF/jLL7+81vo05Sj9cpYuX1/TCy+8UKsXzujRo/Ol1tNV3GZUrVB23nnnxfnnnx9/+MMf8lXXZiRdcStdQS39AlmW+r+kCpk0fWt6/aZm5qqrrsrHTFc/S1eIm171TZrmc+CBB8Y333wzW1fdmxPpdZy6QuT222//yVdfSz2MUrXVNddck4OoshTUzGz6VVn5PU1Xi6tpelcZS0FEmtpU7glVrlhL4V1N6QppaapeOmbNSpYUxqTHl6/sl66qmK6GmM41dQAzO5Vg5RAxvX7pamqpWqpmCJc+g+kqcqmv1PQCo5pTTMshy9NPP129Lr1PaerfrKSwNF3tMF3lLfX4mlr5uaTxTP280uf4k08+qbVubsZSlFQ9mIK4VP01adKkWb62KdiuuS5dHS9dEbMSyn821XwvUmB6xRVXTLNvy5YtTecDWICplAKAOiSFPKmSI10KPVXZrLnmmrk/TAqa0hSjqfsOpR466ZfT1CQ7hUPlX+qmd9n5mlKIkabTpd46qV9VmiY3daiRmpInKUBad911c8VS6juVKkrSedIv6bM6z4wChBTYpF8001SqVJ2UpnyVpcbtqalyCgtSFVbPnj1jXkqBTArD9t133+jVq1e8+eab8be//a1WRdGcSOHPaaedFocffnie/pgagKf3LoU06f2aVaVHqkRLwU56TdNrsv766+deWqnyamqp91ea3pQCmPSepybzV155ZX4fU9PysvQepdc2LamaKTWKT1VT6Rw9evSoDvrSe5Eenz53aRzpPU7TNVOAkXr+pMbls5JCxBR+pcbaqRJuaqniLW1LlVRp2mZ6b1O1XApTU1Vgup2sttpq+XOWplamdelzkZrc1wz6ZiaFM+m7kqaFpamY6XOdArv0OUo9ylIPrG233TZPF0vPM73O6b1Pn8Wp3/u5HUsRUiCV3rtUmZbCxfTZSBVjo0aNigceeCB/tsvhdpqumELg9GdF+tynflOpr1p6nlM3XS9Ceu1T9Vaa0pk+x+k7kqojpxeEpj8PUnXj0UcfnT+7qQ9c+rwCsICo9OX/AGBhduihh+ZLqNeULvWeLqG+9NJLl5o0aVLq1KlT6bzzzqt1ifUkPS49/sYbb8z7pMuzr7XWWrO8XH3NS9bPaJn6GF988UVp3333zZezT5dsT5eXf/nll2frOaZ9V1tttWnWf/PNN6V111231LBhw9JNN91Ua9uAAQPyOM4444zS7EqXjU+Xj5/e+dNSli4337dv31L79u1LLVq0KPXq1av0wgsvTLNfeg3SGO64445ax/voo4/y+uuuu67W+ksvvTSPIb0PPXv2LD333HOltddeu7TlllvOcuzff/996Ygjjsivb8uWLUvbbbddafTo0fk86b2q6ZFHHimtvvrqpaZNm5Y6d+6c3//y+zm1K664otSlS5e8789+9rPSwQcfXPryyy+n2e/ZZ58t/epXvyq1atUqn79r166lyy67rDS70vub3sd//etf093+n//8J39WO3TokD/T7dq1K2266aalq6++utZ+H3zwQWmzzTbLr2Ea7x/+8IfSo48+Ot3P5PR8/PHHpT333LO05JJL5mN07Ngxn3fChAnV7/0xxxwzy/d+bsdSfj8+//zzWuvTZyatT5+hqb/HU0ufpb322mumj03SWLbYYotS69atS82bNy/9/Oc/L+29996lV155pdZ+6XOSXo/0WejWrVvpH//4Rz5+Os+8+m6Vxzj1nw3l71LN1y19P9L3P70P6c+6448/Po9p6v3SnxO77757qU2bNnnb7IwXgPqjQfpPpYMxAGDOpeqCQw89dJqpfguC1Gvq2GOPzX1w0vSy+ij1iUqVKzvttFOe2rcgW2uttXI1UarwAgCYXXpKAQB1Svr/Zan5err6Xn0JpH744Ydpph6lKYFp6lfv3r2jktJUwhRgpumE80Pq4TRs2LA8jQ8AYE7oKQUA1Anffvtt3Hfffbn/UOpnNGjQoKgvXnzxxTjqqKPiN7/5TW56nvolpabiqedXWrcgSo3Lhw4dmqvaUh+qXXfdtdJDAgDqGaEUAFAnpCuD7b777rkhdboi4Pbbbx/1xQorrBAdOnTIDb/LjbFT5VBq8p0aoVfS8ssvnxuiN2nSZJ4e984778xNwzt37hy33HJLNG/efJ4eHwBY8OkpBQAAAEDh9JQCAAAAoHBCKQAAAAAKp6fUAiJddvrTTz+NVq1a5SvsAAAAAFRC6hT19ddfx9JLLx0NG864HkootYBIgVRqsAoAAABQF4wePTqWXXbZGW4XSi0gUoVU+Q2vqqqq9HAAAACAhdT48eNz4Uw5q5gRodQCojxlLwVSQikAAACg0mbVXkgotYDZYdX9o3HDJpUeBgAAADCHHhl1YyxMXH0PAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQrw9ddfxx577BEtW7aM9u3bx0UXXRS9e/eOvn375u1ffvll7LnnnrHYYovFIossEltttVWMGDGi0sMGAAAAmG+EUgU4+uij47nnnov77rsvHn300XjmmWfi1Vdfrd6+9957xyuvvJK3v/DCC1EqlWLrrbeOSZMmzfCYEyZMiPHjx9daAAAAAOoLoVQBVVLXX399nH/++bHpppvG6quvHtddd138+OOPeXuqiEph1LXXXhsbbLBBrLnmmnHTTTfFJ598EoMGDZrhcQcMGBCtW7euXjp06FDgswIAAACYO0Kp+ezDDz/MFU89e/asXpdCpM6dO+fb77zzTjRu3DjWWWed6u1LLLFE3p62zUi/fv1i3Lhx1cvo0aPn8zMBAAAAmHcaz8NjUaBmzZrlBQAAAKA+Uik1n3Xs2DGaNGkSL7/8cvW6VNn03nvv5dtdunSJyZMnx5AhQ6q3jx07NoYPHx6rrrpqRcYMAAAAML+plJrPWrVqFXvttVccd9xxsfjii8dSSy0Vp556ajRs2DAaNGgQnTp1iu233z7233//uOqqq/L+J554YiyzzDJ5PQAAAMCCSKVUAS688MJYb731Ytttt43NNtssevXqlSukmjdvnrenxudrr7123p72S1ffe/DBB3OFFQAAAMCCSKVUAVL1U7qiXtm3334bp59+ehxwwAH5/mKLLRZ/+9vfKjhCAAAAgGIJpQrw2muvxbvvvpuvwJf6SZ1xxhl5vel5AAAAwMJKKFWQ888/Pzcvb9q0aZ6q98wzz0Tbtm0rPSwAAACAihBKFWCttdaKoUOHVnoYAAAAAHWGRucAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhNDpfwAx6+5qoqqqq9DAAAAAAZkqlFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAULjGxZ+S+Wnn9Y6JJo2aVnoYAABQ7cE3/lTpIQBQB6mUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieU+gn23nvv2GGHHSo9DAAAAIB6q3GlB1AfXXLJJVEqlSo9DAAAAIB6Syj1E7Ru3brSQwAAAACo10zfm8vpew8//HD88pe/jDZt2sQSSywR2267bXzwwQfV+66//vpxwgkn1Hr8559/Hk2aNImnn34637/hhhuie/fu0apVq2jXrl3svvvuMWbMmIKfFQAAAEBxhFJz6dtvv42jjz46XnnllXj88cejYcOGseOOO8aUKVPy9j322CNuvfXWWtP9brvttlh66aVjgw02yPcnTZoUZ555Zrz++usxaNCgGDlyZA6+ZmbChAkxfvz4WgsAAABAfdGgpDnSHEuB0VdffZUDpKn997//jSWXXDLefPPNWH311XNVVAqgnnjiieoQKlVPbbjhhnH22WdP9/gp4OrRo0d8/fXXseiii053n9NOOy1OP/30adZvtup+0aRR07l+jgAAMK88+MafKj0EAAqUCmdS66Nx48ZFVVXVDPdTKTWXRowYEbvttlt07Ngxv9ArrLBCXj9q1Kj8MwVUm2++edx00035/kcffRQvvPBCrqAqGzp0aGy33Xax3HLL5Sl8G220Ua1jTE+/fv3ym1teRo8ePZ+fKQAAAMC8I5SaSylM+uKLL+Kaa66JIUOG5CWZOHFi9T4pgLrzzjvzNL2bb7451lhjjbyUp/9tscUWOdBKwdXLL78c99xzzzTHmFqzZs3yY2ouAAAAAPWFUGoujB07NoYPHx4nn3xybLrpptGlS5f48ssvp9lv++23jx9++CE3RU+hVM0qqXfffTcfJ03lS9P7VlllFU3OAQAAgAVe40oPoD5bbLHF8hX3rr766mjfvn2ebnfiiSdOs1/Lli3z1fr69+8f77zzTp7uV5am7DVt2jQuu+yyOOigg+Kf//xnbnoOAAAAsCBTKTUX0pX20pX1Uk+o1NT8qKOOivPOO2+6+6bqqHR1vVQNlYKostRzauDAgXHHHXfEqquumiumzj///AKfBQAAAEDxVEr9BBMmTKi+Kt5mm20Wb7/9dq3t07ug4VZbbTXd9UmqnKpZPTWjYwAAAAAsKFRKzYHJkyfnACpdPW+11Var9HAAAAAA6i2h1BxI/Z66d++eA6nU/wkAAACAn8b0vTnQrVu3+O677yo9DAAAAIB6T6UUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAAABQOI3OFzB3vXBBVFVVVXoYAAAAADOlUgoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAAChc4+JPyfz0m837R5PGzSo9DACA+eL+Z8+t9BAAgHlEpRQAAAAAhRNKAQAAAFA4oRQAAAAAhRNKAQAAAFA4oRQAAAAAhRNKAQAAAFA4oRQAAAAAhRNKAQAAAFA4oRQAAAAAhRNKAQAAAFA4oRQAAAAAhRNKzaWvv/469thjj2jZsmW0b98+Lrrooujdu3f07ds3b7/hhhuie/fu0apVq2jXrl3svvvuMWbMmOrHDx48OBo0aBCPP/543m+RRRaJ9ddfP4YPH17BZwUAAAAwfwml5tLRRx8dzz33XNx3333x6KOPxjPPPBOvvvpq9fZJkybFmWeeGa+//noMGjQoRo4cGXvvvfc0xznppJPiggsuiFdeeSUaN24cffr0KfiZAAAAABSncYHnWiCrpK6//vq4+eabY9NNN83rrrvuulh66aWr96kZLnXs2DEuvfTS6NGjR3zzzTex6KKLVm/73//939hoo43y7RNPPDG22Wab+OGHH6J58+bTPfeECRPyUjZ+/Pj58hwBAAAA5geVUnPhww8/zJVQPXv2rF7XunXr6Ny5c/X9oUOHxnbbbRfLLbdcnsJXDp5GjRpV61hdu3atvp2mASY1p/lNbcCAAflc5aVDhw7z9LkBAAAAzE9Cqfno22+/jS222CKqqqripptuipdffjnuueeevG3ixIm19m3SpEn17dRjKpkyZcoMj92vX78YN25c9TJ69Oj59jwAAAAA5jWh1FxI0/FSmJTCprIUEL333nv59rvvvhtjx46Ns88+OzbYYINYZZVVZlr9NCeaNWuWw66aCwAAAEB9oafUXEjT8fbaa6847rjjYvHFF4+llloqTj311GjYsGGudkpT9po2bRqXXXZZHHTQQfHPf/4zNz0HAAAAWNiplJpLF154Yay33nqx7bbbxmabbRa9evWKLl265AblSy65ZAwcODDuuOOOWHXVVXPF1Pnnn1/pIQMAAABUXINSqVSq9CAWtD5SyyyzTFxwwQWx7777FnbedPW91PB883WOiCaNmxV2XgCAIt3/7LmVHgIAMJsZRWpxNLN2Q6bvzaXXXnst945KV+BLL/YZZ5yR12+//faVHhoAAABAnSWUmgfSlLzhw4fn/lFrr712PPPMM9G2bdtKDwsAAACgzhJKzaW11lorhg4dWulhAAAAANQrGp0DAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDhX31vA3PHImVFVVVXpYQAAAADMlEopAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcI2LPyXz0y47/280adKs0sMAgHrtgQfPqPQQAAAWeCqlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUKoOatCgQQwaNKjSwwAAAACYb4RSAAAAABROKAUAAABA4Rb6UKp3795x+OGHR9++fWOxxRaLn/3sZ3HNNdfEt99+G/vss0+0atUqVlpppXjooYeiVCrl2+eff36tYwwbNixPuXv//ffzPqeddlost9xy0axZs1h66aXjiCOOqN53zJgxsd1220WLFi1ixRVXjJtuuilWWGGFuPjiiyvw7AEAAAAqY6EPpZLrr78+2rZtGy+99FIOqA4++OD4zW9+E+uvv368+uqrsfnmm8fvf//7+P7776NPnz5x3XXX1Xp8ur/hhhvmwOquu+6Kiy66KK666qoYMWJE7g21xhprVO+79957x+jRo+PJJ5+MO++8M6644oocVAEAAAAsTIRSEbHmmmvGySefHJ06dYp+/fpF8+bNc0i1//7753WnnHJKjB07Nt54440cKg0fPjwHWMmkSZPi5ptvzmFVMmrUqGjXrl1sttlmuVqqZ8+e+TjJe++9lyuuUiXWuuuuG2uvvXb85S9/yWHXnJowYUKMHz++1gIAAABQXwilIqJr167Vtxs1ahRLLLFEreqmNKUvSRVNaTreNttsE3/961/zur///e85IEqVVUn6mUKmjh075jDqnnvuicmTJ+dt77zzTjRu3DiHUWWrrLJKtGnTZo7HPGDAgGjdunX10qFDh7l4BQAAAACKJZSKiCZNmtS6n/pD1VyX7idTpkzJP/fbb7+49dZbc/iUpu7tuuuuscgii+RtKRxKlVRpWl7qG3XIIYfkqX2pompeShVd48aNq17SlEAAAACA+qJxpQdQH2299dbRsmXLuPLKK+Phhx+Op59+utb2FEalZuZpOfTQQ3M11Jtvvpl/pqqpoUOHRo8ePfK+KcD66quv5ngMqYl6WgAAAADqI6HUT5Cm+KXeUqlaKfWcWm+99aq3DRw4MH788cdYZ511cvXUjTfemEOq5ZdfPk8L3HLLLePAAw/MgVaaypeu+pe2AwAAACxMTN/7ifbdd9+YOHFi7LPPPrXWp/5QqZF5r169cq+qxx57LPedSoFUkqb7pb5UG220Uey0005xwAEHxFJLLVWhZwEAAABQGQt9pdTgwYOnWTdy5Mhp1pVKpVr3P/nkk9x3as8996y1focddsjLjKQr891///211vXv33+m5wIAAABY0Cz0odScSlfa+/zzz+O0007LV9orX5kPAAAAgNln+t4cuuWWW3J/qNSc/Nxzz630cAAAAADqJZVScyg1OE/LvDS96YIAAAAACzKVUgAAAAAUTigFAAAAQOGEUgAAAAAUTigFAAAAQOGEUgAAAAAUztX3FjB33nVSVFVVVXoYAAAAADOlUgoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAAChc4+JPyfy04+8HROMmzSs9DABm0z/uPLXSQwAAgIpQKQUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSP9HIkSOjQYMGMWzYsEoPBQAAAKDeEUrNhr333jt22GGHSg8DAAAAYIEhlAIAAACgcAtFKNW7d+847LDD8tK6deto27Zt9O/fP0qlUpxxxhmx+uqrT/OYbt265X1OO+20uP766+Pee+/N0/XSMnjw4Or9Pvzww9h4441jkUUWiTXXXDNeeOGFWse56667YrXVVotmzZrFCiusEBdccEGt7WndH//4x+jTp0+0atUqlltuubj66qvn46sBAAAAUHkLRSiVpGCpcePG8dJLL8Ull1wSF154YVx77bU5DHrnnXfi5Zdfrt73tddeizfeeCP22WefOPbYY+O3v/1tbLnllvHZZ5/lZf3116/e96STTsr7pN5SK6+8cuy2224xefLkvG3o0KH5sb/73e/izTffzAFXCroGDhxYa2wpqOrevXs+7yGHHBIHH3xwDB8+vMBXBwAAAKBYjWMh0aFDh7joootypVPnzp1zSJTu77///rHFFlvEddddFz169Mj7ptsbbbRRdOzYMd9v0aJFTJgwIdq1azfNcVMgtc022+Tbp59+eq6Kev/992OVVVbJwdemm26ag6gkhVZvv/12nHfeeblPVdnWW2+dw6jkhBNOyON68skn8zhnJI0nLWXjx4+fZ68VAAAAwPy20FRKrbvuujmQKltvvfVixIgR8eOPP+Zg6pZbbokffvghJk6cGDfffHOuoJodXbt2rb7dvn37/HPMmDH5Z6rA6tWrV6390/3yead3jDTGFH6VjzEjAwYMyFMRy0sK3QAAAADqi4UmlJqZ7bbbLvd8uueee+Lvf/97TJo0KXbZZZfZemyTJk2qb5dDrylTpszR+Wseo3ycWR2jX79+MW7cuOpl9OjRc3ROAAAAgEpaaKbvDRkypNb9F198MTp16hSNGjXK9/faa688ba9p06a5B1SasleW1tWsbJpdXbp0ieeee67WunQ/TeMrn/enSiFaWgAAAADqo4UmlBo1alQcffTRceCBB8arr74al112Wa0r4e233345REqmDpLSFfL+8Y9/5ObjSyyxRJ4uNzuOOeaY3KfqzDPPjF133TVfme/yyy+PK664Yh4/OwAAAID6ZaEJpfbcc8/4/vvvo2fPnrlK6cgjj4wDDjigenuqmkpX1fviiy9inXXWqfXY1HNq8ODB+Qp533zzTW5CnoKqWfnFL34Rt99+e5xyyik5mEo9p84444xaTc4BAAAAFkYLTSiV+jZdfPHFceWVV053e6lUik8//bT6Kng1LbnkkvHII49M9zE1tWnTZpp1O++8c15mZOTIkdOsGzZs2EyfCwAAAEB9t9CEUjPz+eefx6233hr//ve/Y5999qn0cAAAAAAWeEKpiFhqqaWibdu2cfXVV8diiy1W6eEAAAAALPAWilAq9YOamamn3AEAAAAwfzWcz8cHAAAAgGkIpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMItFFffW5jcc0O/qKqqqvQwAAAAAGZKpRQAAAAAhRNKAQAAAFA4oRQAAAAAhRNKAQAAAFA4oRQAAAAAhRNKAQAAAFC4xsWfkvlp2wPOicZNm1d6GADMwhN/61/pIQAAQEWplAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEKpChg8eHA0aNAgvvrqq0oPBQAAAKAihFIAAAAAFE4oNQdKpVJMnjy50sMAAAAAqPcW+lBqwoQJccQRR8RSSy0VzZs3j1/+8pfx8ssv15pm99BDD8Xaa68dzZo1i2effTY++OCD2H777eNnP/tZLLrootGjR4947LHHpjnuCSecEB06dMiPW2mlleIvf/nLDMeRjrvBBhtEixYt8mPSmL799tv5/vwBAAAAKmGhD6WOP/74uOuuu+L666+PV199NYdHW2yxRXzxxRfV+5x44olx9tlnxzvvvBNdu3aNb775Jrbeeut4/PHH47XXXostt9wytttuuxg1alT1Y/bcc8+45ZZb4tJLL82Pu+qqq3KANT0p5ErH2HnnneONN96I2267LYdUhx12WCGvAQAAAEDRGpTSnLSFVKpEWmyxxWLgwIGx++6753WTJk2KFVZYIfr27ZsroDbeeOMYNGhQroyamdVXXz0OOuigHCS999570blz53j00Udjs802m2bfVIGVjvvll19GmzZtYr/99otGjRrl4KoshVIbbbRRHmOq4JpaqsRKS9n48eNzhdUGu/4hGjeddn8A6pYn/ta/0kMAAID5ImUUrVu3jnHjxkVVVdUM91uoK6VShVIKoXr16lW9rkmTJtGzZ89c3VTWvXv3Wo9LlVLHHntsdOnSJYdKqQIq7V+ulBo2bFgOmVKoNDtef/31HIyl45SXVK01ZcqU+Oijj6b7mAEDBuQ3uLykQAoAAACgvmhc6QHUBy1btqx1PwVSqQrq/PPPz9P9Uh+oXXbZJSZOnJi3p/tzIoVcBx54YO4jNbXllltuuo/p169fHH300dNUSgEAAADUBwt1KPXzn/88mjZtGs8991wsv/zyeV2qnEqNztP0vRlJ+++9996x4447VodKI0eOrN6+xhpr5Cqnp556arrT96b2i1/8It5+++0ccM2u1Dw9LQAAAAD1UcOFvQLq4IMPjuOOOy4efvjhHAztv//+8d1338W+++47w8d16tQp7r777jxNL029S/2oUghVlnpS7bXXXtGnT5/cjypNwUt9pG6//fbpHi9dpe/555/P/ajSMUeMGBH33nuvRucAAADAAmuhrpRK0lX1UqD0+9//Pr7++uvcP+of//hHboA+IxdeeGEOnNZff/1o27ZtDpXS9LmarrzyyvjDH/4QhxxySIwdOzZPw0v3pydd0S9VVZ100kmxwQYbROo9n6q4dt1113n+fAEAAADqgoX66nsLYmd7V98DqB9cfQ8AgAWVq+8BAAAAUGcJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAoXOPiT8n8dP/VJ0RVVVWlhwEAAAAwUyqlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwjWu9ACYt7Y48pxo3LR5pYcBUDHPXNW/0kMAAABmg0opAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAq3UIRSgwcPjgYNGsRXX31V6aEAAAAAsLCEUvOakAsAAABg7gil5qOJEydWeggAAAAAddICE0pNmTIlBgwYECuuuGK0aNEi1lxzzbjzzjtnuP+zzz4bG2ywQd63Q4cOccQRR8S3335bvX3ChAlxwgkn5G3NmjWLlVZaKf7yl7/EyJEjY+ONN877LLbYYrliau+99873e/fuHYcddlj07ds32rZtG1tssUVe/9RTT0XPnj3zcdq3bx8nnnhiTJ48ufpc6XHp/Mcff3wsvvji0a5duzjttNPm46sFAAAAUFkLTCiVAqm//e1v8ec//zneeuutOOqoo+J//ud/ciA0tQ8++CC23HLL2HnnneONN96I2267LYdUKVAq23PPPeOWW26JSy+9NN5555246qqrYtFFF80h1V133ZX3GT58eHz22WdxySWXVD/u+uuvj6ZNm8Zzzz2Xx/LJJ5/E1ltvHT169IjXX389rrzyyhxunXXWWbXGlB7XsmXLGDJkSJx77rlxxhlnxKOPPjpfXzMAAACASmlQKpVKUc+lqqZUYfTYY4/FeuutV71+v/32i++++y4OOOCAXN305ZdfRps2bfL6Ro0a5aCpLIVSG220Ua6WGjVqVHTu3DmHQpttttl0e0rVPF7Niqfx48fHq6++Wr3upJNOyiFWCrZSVVVyxRVX5CqscePGRcOGDfPjfvzxx3jmmWeqH5cqqzbZZJM4++yzZ/ic01KWzpsCs3X3/kM0btp8rl5PgPrsmav6V3oIAACwUBs/fny0bt065x5VVVUz3K9xLADef//9HD796le/mqan01prrTXN/qliKVVI3XTTTdXrUjaXpgB+9NFH8eabb+bQKoVUc2rttdeudT+FUSkoKwdSSa9eveKbb76Jf/3rX7HccsvldV27dq31uDTNb8yYMTOtDDv99NPneHwAAAAAdcECEUqlgCd54IEHYplllqm1LfVxStP1pt7/wAMPzH2cppZCohRy/VRpCt5P0aRJk1r3U4iVQrIZ6devXxx99NHTVEoBAAAA1AcLRCi16qqr5vApTbubXnXT1KHUL37xi3j77bdz8/LpWWONNXIglPpRTW/6XuoZlaQpd7PSpUuXPH0vVWKVq6VSv6lWrVrFsssuGz9Ver5pAQAAAKiPFohG5yngOfbYY3Nz89QwPIVQqa/TZZddlu9PLfVzev7553Nj82HDhsWIESPi3nvvrW50vsIKK8Ree+0Vffr0iUGDBuUpfamP1O233563L7/88jlguv/+++Pzzz+vrtSankMOOSRGjx4dhx9+eLz77rv5PKeeemquckr9pAAAAAAWRgtMKnLmmWdG//79c6+lVJ2Urq6XpvOtuOKK0+yb+jelKqj33nsvNthgg9x36pRTTomll166ep90lbxddtklh0qrrLJK7L///rkJepKmCKZ+TieeeGL87Gc/q3XVvqmlfR988MF46aWXYs0114yDDjoo9t133zj55JPn0ysBAAAAUPctEFff4/91tnf1PWBh5+p7AABQP66+t8BUSgEAAABQfwilAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAAChc4+JPyfz0j0tOiKqqqkoPAwAAAGCmVEoBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFa1z8KZmfNj3+nGjcrHmlhwEw37xwSf9KDwEAAJgHVEoBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAULg6G0rtvffescMOO0R91rt37+jbt2+lhwEAAABQ5zSOOuqSSy6JUqkU9dndd98dTZo0maPHNGjQIO655556H8gBAAAA1MtQqnXr1lFfTZw4MZo2bRqLL754pYcCAAAAUCfVi+l7Dz/8cPzyl7+MNm3axBJLLBHbbrttfPDBB9X7rr/++nHCCSfUevznn3+eq5SefvrpfP+GG26I7t27R6tWraJdu3ax++67x5gxY6r3//LLL2OPPfaIJZdcMlq0aBGdOnWK6667rnr7v/71r9htt91y0NSyZct8rCFDhuRtp512WnTr1i2uvfbaWHHFFaN58+bTnb63wgorxJlnnpmPk46xzDLLxJ/+9Kda25Mdd9wxV0yV7wMAAAAsaOpsKFXTt99+G0cffXS88sor8fjjj0fDhg1zcDNlypS8PYVJt956a63pfrfddlssvfTSscEGG+T7kyZNyoHQ66+/HoMGDYqRI0fm4Kusf//+8fbbb8dDDz0U77zzTlx55ZXRtm3bvO2bb76JjTbaKD755JO477778jGOP/746vMn77//ftx11115yt6wYcNm+FzOO++8WHPNNeO1116LE088MY488sh49NFH87aXX345/0xh2GeffVZ9HwAAAGBBU2en79W0884717r/17/+NVc0pRBp9dVXj9/+9re5IunZZ5+tDqFuvvnmXJGUKo6SPn36VD++Y8eOcemll0aPHj1y4LTooovGqFGjYq211soVUEnNKqV0rFR5lUKi8pS8lVZaaZope3/729/yuGamV69eOYxKVl555Xjuuefioosuil/96lfVj00VYamaa2YmTJiQl7Lx48fPdH8AAACAuqReVEqNGDEiB0wpTKqqqqoOjFKQlKQwZ/PNN4+bbrop3//oo4/ihRdeyBVUZUOHDo3tttsulltuuTyFL1U+1TzGwQcfnKut0jS8VAX1/PPPVz82VT6lwGpmPaKWX375WQZSyXrrrTfN/VSZNacGDBiQ+26Vlw4dOszxMQAAAAAqpV6EUilM+uKLL+Kaa67JfZzKvZxSdVJZCqDuvPPOPE0vVTatscYaeSlP/9tiiy1yoJWCq1TxlK5wV/MYW221VXz88cdx1FFHxaeffhqbbrppHHvssXlb6jE1K6lHVJH69esX48aNq15Gjx5d6PkBAAAAFuhQauzYsTF8+PA4+eSTc1DUpUuX3JR8attvv3388MMPuSl6CqVqVkm9++67+Thnn312nt63yiqr1GpyXpYqnfbaa6+48cYb4+KLL46rr746r+/atWuulkrB2Nx68cUXp7mfnlNZas7+448/zvI4zZo1yyFbzQUAAACgvqjzodRiiy2Wr7iXAqLUTPyJJ57ITc+nV6mUrtaXGpan6XBpul9ZmrLXtGnTuOyyy+LDDz/MzcpT0/OaTjnllLj33nvzOd566624//77q8OidKzU4ykdP/WASsdITc3TFME5lR5/7rnnxnvvvZevvHfHHXfkZudlaWpiaub+73//e7rhGwAAAMCCoM6HUulKe6nXU+oJlZqap+l16Qp205Oqo9KV8VI1VAqialZADRw4MAdAq666aq6YOv/882s9NoVWaUpcqoracMMNo1GjRvm85W2PPPJILLXUUrH11lvnaYHpGGmfOXXMMcfkqwimHlVnnXVWXHjhhXlqYdkFF1yQr8aXekSlfQAAAAAWRA1KpVIp6qBUnZRCnzSVbkGRqqDSVQLTMq+lq++lhufdD/xDNG7WfJ4fH6CueOGS/pUeAgAAMBsZReqBPbN2Q3WuUmry5Mnx9ttv56lxq622WqWHAwAAAMB8UOdCqX/+85/RvXv3HEgddNBBlR4OAAAAAPNB46hjunXrFt99910siEaOHFnpIQAAAADUCXWuUgoAAACABZ9QCgAAAIDCCaUAAAAAKJxQCgAAAIDCCaUAAAAAKFydu/oec+fxc0+IqqqqSg8DAAAAYKZUSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIVrXPwpmZ96n3J2NGrWvNLDAGp4+ZxTKj0EAACAOkelFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFW2BDqd69e0ffvn0rPQwAAAAAFqZQCgAAAIC6Syg1j0ycOHG+HPfHH3+MKVOmzJdjAwAAAFRKnQ2l7rzzzlhjjTWiRYsWscQSS8Rmm20W3377bXVQc/TRR0ebNm3ytuOPPz722muv2GGHHWb7+K+//npsvPHG0apVq6iqqoq11147Xnnllertzz77bGywwQb5/B06dIgjjjii+vzJCiusEGeeeWbsueee+fEHHHBArL/++nHCCSfUOs/nn38eTZo0iaeffjrfnzBhQhx77LGxzDLLRMuWLWOdddaJwYMHV+8/cODA/Lzuu+++WHXVVaNZs2YxatSouXotAQAAAOqaOhlKffbZZ7HbbrtFnz594p133smhzU477RSlUilvv+CCC3J489e//jWHR1988UXcc889c3SOPfbYI5Zddtl4+eWXY+jQoXHiiSfm8Cj54IMPYsstt4ydd9453njjjbjtttvyeQ477LBaxzj//PNjzTXXjNdeey369++fj3nrrbdWjzNJj1166aVzwJWkY7zwwgt5v3Ts3/zmN/lcI0aMqH7Md999F+ecc05ce+218dZbb8VSSy01V68nAAAAQF3ToFQzQakjXn311Vy5NHLkyFh++eWn2Z5CnqOOOiqOO+64fH/y5Mmx4oor5scMGjSoutF5t27d4uKLL57uOVJ102WXXZYrrKa23377RaNGjeKqq66qXpdCqY022ihXSzVv3jxXSq211lq1wrBUFZXG9sQTT1SHUKl6asMNN4yzzz47Vzx17Ngx/0z7laUqsJ49e8Yf//jHHLbts88+MWzYsBx4zUiquEpL2fjx43NF11pH9otGzZrP8jUGivPyOadUeggAAACFSRlF69atY9y4cTl/qVeVUimM2XTTTfP0vVRJdM0118SXX36Zt6UnlCqp0rS3ssaNG0f37t3n6Bxp+l8Kn1IglAKjVB1Vc2pfCocWXXTR6mWLLbbIvZ0++uij6v2mPueSSy4Zm2++edx00035fto3VUWlCqrkzTffzFMPV1555VrHfuqpp2qdv2nTptG1a9eZjn/AgAH5DS4vKZACAAAAqC/qZCiVqpQeffTReOihh3JfpVTR1Llz51qB0Nw67bTT8tS4bbbZJlc2pfOUq56++eabOPDAA3O1UnlJQVWaYvfzn/+8+hipJ9TUUgCV+mFNmjQpbr755hyspaV83PTc0nTBmsdOUxQvueSS6mOkPlYNGjSY6fj79euXA7ryMnr06Hn22gAAAAAslKFUkkKZXr16xemnn557NqXqoRQapaqg9u3bx5AhQ6r3TdP3UtAzp1LFUpoG+Mgjj+SeVdddd11e/4tf/CLefvvtWGmllaZZ0jhmZvvtt48ffvghHn744RxKlaukkjTdL1VKjRkzZprjtmvXbo7GnhqgpxK4mgsAAABAfdE46qAUOD3++ON5Klxq8p3up35NXbp0yduPPPLIPOWuU6dOscoqq8SFF14YX3311Wwf//vvv8/9qHbZZZfci+pf//pXbnieGpsn6Qp66667bm5Knqb4pYqoFFKl6q3LL798psdO+6arAKbG56kCKjVsrxmCpZAqXbEvNWtPIVV6Xum5pul6qWoLAAAAYGFQJ0OpVPXz9NNP5yblqTlWanaeQpytttoqbz/mmGNyX6nUpLxhw4b5Kn077rhjnsY2O9IUurFjx+Zw6D//+U+0bds2V0qlqqwkBUSpz9NJJ52UG5anXvBp2t6uu+46W8dPwdPWW2+dG5wvt9xytbalaqyzzjorP4dPPvkknzsFYNtuu+0cv04AAAAA9VWdvPreT7H33nvnaqny1fcW1s72rr4HdY+r7wEAAAuT8fX56nsAAAAALNiEUgAAAAAUrk72lPopBg4cWOkhAAAAADCbVEoBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAULgGpVKpVPxpmdfGjx8frVu3jnHjxkVVVVWlhwMAAAAspMbPZkahUgoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAAChc4+JPyfz0yz8OiEbNmld6GFC4104/tdJDAAAAYA6olAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAOp+o/P77rtvuusbNGgQzZs3j5VWWilWXHHFeTE2AAAAABZQcxxK7bDDDjmAKpVKtdaX16Wfv/zlL2PQoEGx2GKLzcuxAgAAALCwTt979NFHo0ePHvnnuHHj8pJur7POOnH//ffH008/HWPHjo1jjz12/owYAAAAgIWvUurII4+Mq6++OtZff/3qdZtuummeunfAAQfEW2+9FRdffHH06dMnFna9e/eObt265dcDAAAAgLmolPrggw+iqqpqmvVp3Ycffphvd+rUKf773//O6aEBAAAAWEjMcSi19tprx3HHHReff/559bp0+/jjj8/T+pIRI0ZEhw4d5u1IF0KpR9fkyZMrPQwAAACAyodSf/nLX+Kjjz6KZZddNl9pLy3p9siRI+Paa6/N+3zzzTdx8sknR6U8/PDDudl6mzZtYokllohtt902V3glaZypGfvdd98dG2+8cSyyyCKx5pprxgsvvFDrGNdcc00O1tL2HXfcMS688MJ8vLK99947N32vqW/fvnnK3ozccMMN0b1792jVqlW0a9cudt999xgzZkz19sGDB+exPfTQQzn8a9asWTz77LPz8JUBAAAAqKc9pTp37hxvv/12PPLII/Hee+9Vr/vVr34VDRv+X8Y1dVhTtG+//TaOPvro6Nq1aw7ITjnllBwsDRs2rHqfk046Kc4///w81TDd3m233eL999+Pxo0bx3PPPRcHHXRQnHPOOfHrX/86Hnvssejfv/9cj2vSpElx5pln5tcrhVFpjCncevDBB2vtd+KJJ+axdezY0RUMAQAAgAXSHIdSSQqfttxyy7zURTvvvHOt+3/9619jySWXzGHaoosumtelqwNus802+fbpp58eq622Wg6lVllllbjssstiq622qr6C4MorrxzPP/98vrrg3KjZ/D0FTpdeemme8piCs/K4kjPOOCOHfDMzYcKEvJSNHz9+rsYGAAAAUOdDqccffzwvqdpnypQp0wRAlZZ6WqXqqCFDhuSG6+Uxjho1KlZdddV8O1VRlbVv3z7/TM8nhVLDhw/PlVU19ezZc65DqaFDh8Zpp50Wr7/+enz55ZfTHVeSpvjNyoABA3KYBgAAALBQ9JRKQcjmm2+eQ6kU+KRwpeZSF2y33XbxxRdf5L5QKZhKSzJx4sTqfZo0aVJ9O/VxSqYO2GZVLZYakU89PW9mUwq32GKLfJXCm266KV5++eW45557phlX0rJly1mev1+/fjFu3LjqZfTo0bM9dgAAAIB6Vyn15z//OQYOHBi///3voy4aO3ZsrnRKgdQGG2yQ181ps/DU8ymFRjVNfT9NB/znP/9Za13qWVUz7Krp3XffzWM7++yzq69M+Morr8RPlZqgpwUAAABgoaiUSlU966+/ftRVqTF4uuLe1VdfnXtEPfHEE7mh+Jw4/PDDc/PxdMW9NBXwqquuylfEK1dUJZtsskkOlf72t7/lfU499dRpQqqalltuuWjatGnuV/Xhhx/Gfffdl5ueAwAAACyM5jiU2m+//eLmm2+OuipNq7v11ltz/6bVV189jjrqqDjvvPPm6Bi9evXKFWEplFpzzTXj4Ycfzsdp3rx59T5pKl66It/xxx+fm5V//fXXseeee87wmKmyKlWY3XHHHbl/VKqYSlfYAwAAAFgYNShN3RhpFo488shcHZQahadl6ulqKchZEO2///55Ct4zzzwTdVG6+l7r1q1jjRNOjEbN/l94BguL104/tdJDAAAAIP5fRpF6YKfe2vOsp9Qbb7wR3bp1y7ennq5Wc3pbfZeqmH71q1/lpuNp6t71118fV1xxRaWHBQAAALBAmONQ6sknn4yFwUsvvRTnnntunpbXsWPHuPTSS/PURQAAAAAqEEotLG6//fZKDwEAAABggfWTQql01bkU2owaNSpfja+mu+++e16NDQAAAICF+ep7qZfS2LFj8+10Zbt0dbrU9DtdSa5Zs2YxfPjwuP/++3P/JQAAAACYJ6HUv/71rxxEJX/84x/jkksuifvuuy/ShftuueWWeOutt/L6xRZbbHYOBwAAAMBCbrZCqRQ4ffTRR/HZZ5/FBx98EFtuuWVe37Rp0/j222/z7WOPPTYeeOCB+TtaAAAAABaeUOraa6+NJZZYIpZaaqlcDZWuSJcss8wy8frrr+fb48ePr14PAAAAAHPd6Dz1j3rssceiUaNGscEGG+Tba6yxRvz2t7+N3/zmN7HtttvG888/HxtuuOHsHI756Nk/9IuqqqpKDwMAAABgphqUUmOoOfDf//43vv/+++jQoUNMnjw5zjnnnHjxxRejc+fOcfLJJ0ebNm3m5HDMI6lSrXXr1jFu3DihFAAAAFDnM4o5DqWom4RSAAAAQH3KKGZr+t7UpkyZEu+//36MGTMm367JFD4AAAAAZmWOQ6k0VW/33XePjz/+OKYusmrQoEH8+OOPc3pIAAAAABYycxxKHXTQQdG9e/d44IEHon379jmIAgAAAID5GkqNGDEi7rzzzlhppZXm9KEAAAAA8NNCqXXWWSf3kxJK1U3rXzggGjVvVulhwFx5/cTTKj0EAAAA6loodfjhh8cxxxwT//73v2ONNdaIJk2a1NretWvXeTk+AAAAABZAcxxK7bzzzvlnnz59qtelvlKp6blG5wAAAADMl1Dqo48+mtOHAAAAAMDchVLLL7/8nD4EAAAAAGppWPsuAAAAAMx/QikAAAAACieUAgAAAKBwQikAAAAA6n4o9bvf/S4effTRmN/23nvv2GGHHebqGAMHDow2bdpU3z/ttNOiW7duUaTevXtH3759Cz0nAAAAwAIRSp1xxhnxwQcf5NulUil+/etfxworrJBDnpEjR87vMdZrd999d5x55plz9JgGDRrEoEGD5tuYAAAAAOpFKPXzn/88tt9++3z7tttui08//TSOOeaYuO+++2KllVaKzTbbLG655ZaYMGHC/B5vvTFx4sT8c/HFF49WrVpVejgAAAAA9S+UGj9+fHTs2LH6/mKLLRaHH354vPrqq/HKK6/EqquuGn369In27dvHYYcdFsOHD5+tk995552xxhprRIsWLWKJJZbI4da3335ba5/zzz8/HzdtP/TQQ2PSpEnV21IIduyxx8YyyywTLVu2jHXWWScGDx4cP9WXX34Ze+yxRyy55JJ5TJ06dYrrrruuevu//vWv2G233XLQlM7XvXv3GDJkSK2pgddee22suOKK0bx58+lO30sVZqlyKh0nHSON/U9/+lOt7cmOO+6YK6bK9wEAAAAWJI1nZ6eTTjppun2k/vOf/8QjjzySl0aNGsV2220X7777bqy++upx7rnnxlFHHTXDY3722Wc5mEn7pQDm66+/jmeeeSZPDyx78skncyCVfr7//vux66675uBn//33z9tTAPb222/HrbfeGksvvXTcc889seWWW8abb76ZA6U51b9//3y8hx56KNq2bZvP+f333+dt33zzTWy00UY5REoVYu3atcuh3JQpU6ofn/a/66678pS99HrMyHnnnRd/+MMf4vTTT49//OMfceSRR8bKK68cv/rVr+Lll1+OpZZaKodh6bnM6DgpkKtZmZaCQwAAAIAFKpQ6+uij4+CDD46XXnopfvzxx/j73/8ef/3rX+Phhx+Orl275kqg3XffPaqqqvL+KRxKlVOzCqUmT54cO+20Uyy//PJ5XaqaqilVZF1++eU5mFlllVVim222iccffzyHUqNGjcrBTfqZAqkkVU2lMaX1f/zjH+f4xUjHWmuttXIFVFKzSunmm2+Ozz//PIdGqVIqSVMXp56y97e//S1XWs1Mr1694sQTT8y3Uxj13HPPxUUXXZRDqfJjU4P2FHzNyIABA3KoBQAAALDATt87+eSTcyCVpAAoBU4dOnTI69L0vYMOOqg6kEo23njjWle9m54111wzNt100xxE/eY3v4lrrrkmT5+rabXVVqtVKZSqpsaMGZNvp2qoFJClUGfRRRetXp566qnqpuxzKgVvqeoqVWMdf/zx8fzzz1dvGzZsWA6syoHU9KRwbVaBVLLeeutNc/+dd96Zo7H269cvxo0bV72MHj16jh4PAAAAUOcrpabu8ZRCpHLPpJrSFLcUDKVA6qOPPprpcVLYlKYEpuAnTf+77LLL8jTB1KMp9WRKmjRpUusxqcdSebpcOlc6xtChQ6eZ4pbG8FNstdVW8fHHH8eDDz6Yx5ZCs9THKj3n1GNqVlKPqKI0a9YsLwAAAAALbKVUkqaXJb///e+nG0ilnlBbbLHFHJ08hUxpKluahvbaa69F06ZN89S/2ZGqllKlVKqcStPoai4zm/Y2K6nSaa+99oobb7wxLr744rj66qvz+jRNMVVLffHFFzG3XnzxxWnud+nSpfp+CuPScwMAAACIhT2USo25U7+k6UlXzEtNuceOHTvbJ04VUanvU5r+l3o5pebgqWdTzXBmZtK0vXSlvD333DM/NlVmpemEqdfSAw88ED/FKaecEvfee29uWP7WW2/F/fffXz2e1JQ9hV077LBD7gH14Ycf5qbmL7zwwhyfJz0+NXh/77338pX37rjjjtzsvCz1skq9s/79739PM6URAAAAYKEKpW644YY48MAD85Xnpg6kUoVUCpTSVfJmV+pB9fTTT8fWW2+dA6bUt+qCCy7IU+hmV2ponkKpY445Jjp37pwDo9SIfLnlloufIlVqpV5NqSpqww03zNMCU4+p8rY0zTBdGS+NOfXCOvvss2d6lb0ZSeNNYVyq9jrrrLPiwgsvrFVlll6HNH0w9e1K+wAAAAAsaBqUSqXS7O587bXX5oqeVInUu3fv6gqpVNGTGoyXr4LHjKUqqHS1wrTMS+PHj4/WrVvHaqeeGI2a6zVF/fb6iadVeggAAADMZUaRLsxW88J4c9XofL/99ss9lbbffvs8zS1Nd/v0008FUgAAAADM36vvHX/88TmYSlemS1U/gwcPjmWXXXZODwMAAADAQmy2Q6mddtqp1v10hbi2bdvWatCdpKbjzNjIkSMrPQQAAACA+hNKpbmANaWr0QEAAADAfA2l0pXuAAAAAGBeaDhPjgIAAAAAc0AoBQAAAEDdv/oeddvzR/eLqqqqSg8DAAAAYKZUSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIVrXPwpmZ96/fmP0ah5s0oPA36SYUecXukhAAAAUBCVUgAAAAAUTigFAAAAQOGEUgAAAAAUTigFAAAAQOGEUgAAAAAUTigFAAAAQOGEUgAAAAAUTigFAAAAQOGEUvNB7969o2/fvpUeBgAAAECdJZQCAAAAoHBCKQAAAAAKJ5Sazxo0aBCDBg2qta5NmzYxcODA6vsnnHBCrLzyyrHIIotEx44do3///jFp0qQKjBYAAACgGI0LOg8z0apVqxxSLb300vHmm2/G/vvvn9cdf/zxM3zMhAkT8lI2fvz4gkYLAAAAMPdUStUBJ598cqy//vqxwgorxHbbbRfHHnts3H777TN9zIABA6J169bVS4cOHQobLwAAAMDcEkrVAbfddlv06tUr2rVrF4suumgOqUaNGjXTx/Tr1y/GjRtXvYwePbqw8QIAAADMLaFUAT2lSqVSrXU1+0W98MILsccee8TWW28d999/f7z22mtx0kknxcSJE2d63GbNmkVVVVWtBQAAAKC+0FNqPltyySXjs88+q74/YsSI+O6776rvP//887H88svnIKrs448/LnycAAAAAEUSSs1nm2yySVx++eWx3nrrxY8//pivtNekSZPq7Z06dcpT9W699dbo0aNHPPDAA3HPPfdUdMwAAAAA85vpe/PZBRdckJuQb7DBBrH77rvnJuaLLLJI9fZf//rXcdRRR8Vhhx0W3bp1y5VT/fv3r+iYAQAAAOa3BqWpGx5RL40fPz5fhW/1c06IRs2bVXo48JMMO+L0Sg8BAACAeZRRpAuzzawHtkopAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAArXuPhTMj89d9AfoqqqqtLDAAAAAJgplVIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhGhd/SuanTf52ZjRu0azSw2AB8uK+Z1V6CAAAACyAVEoBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0pVwMCBA6NNmzaVHgYAAABAxQilAAAAACicUAoAAACAwgmlZuLOO++MNdZYI1q0aBFLLLFEbLbZZvHtt9/mbX/9619jtdVWi2bNmkX79u3jsMMOq37chRdemB/XsmXL6NChQxxyyCHxzTffzPA8H3zwQWy//fbxs5/9LBZddNHo0aNHPPbYY4U8RwAAAIBKEErNwGeffRa77bZb9OnTJ955550YPHhw7LTTTlEqleLKK6+MQw89NA444IB4880347777ouVVlqp+rENGzaMSy+9NN566624/vrr44knnojjjz9+hudKgdXWW28djz/+eLz22mux5ZZbxnbbbRejRo2a4WMmTJgQ48ePr7UAAAAA1BcNSillYRqvvvpqrL322jFy5MhYfvnla21bZpllYp999omzzjprtiuuDjrooPjvf/9b3ei8b9++8dVXX83wMauvvnp+TM0KrJpOO+20OP3006dZv/Zlx0bjFs1ma1wwO17cd/Y+5wAAAJCkwpnWrVvHuHHjoqqqKmZEpdQMrLnmmrHpppvmaXi/+c1v4pprrokvv/wyxowZE59++mneNiNp6l3ansKrVq1axe9///sYO3ZsfPfddzOslDr22GOjS5cu+ap8aQpfqs6aWaVUv3798ptbXkaPHj1PnjcAAABAEYRSM9CoUaN49NFH46GHHopVV101LrvssujcuXP85z//menjUmXVtttuG127do277rorhg4dGn/605/ytokTJ073MSmQuueee+KPf/xjPPPMMzFs2LAchs1o/yT1skppY80FAAAAoL4QSs1EgwYNolevXnmaXOr11LRp0xxUrbDCCrn/0/SkEGrKlClxwQUXxLrrrhsrr7xyrqyameeeey723nvv2HHHHXMY1a5duxxuAQAAACyoGld6AHXVkCFDcvC0+eabx1JLLZXvf/7553mKXernlPo9pfVbbbVVfP311zlYOvzww3PD80mTJuXKqtSsPK3/85//PNNzderUKe6+++68fwrC+vfvn4MtAAAAgAWVUGoG0nS4p59+Oi6++OLcoCs1O0/VTymESn744Ye46KKL8tS7tm3bxi677FLdi+rCCy+Mc845J/d92nDDDWPAgAGx5557zvBcaf90lb/1118/H+uEE05wNT0AAABggebqewtYZ3tX32Nec/U9AAAA5oSr7wEAAABQZwmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwjUu/pTMT0/s2T+qqqoqPQwAAACAmVIpBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFK5x8adkftrhztOi8SLNKj0M6ohHfjeg0kMAAACA6VIpBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFJ1yMiRI6NBgwYxbNiwSg8FAAAAYL4SSs1DEydOrPQQAAAAAOoFodRc6N27dxx22GHRt2/faNu2bWyxxRbTVDp99dVXed3gwYPz/S+//DL22GOPWHLJJaNFixbRqVOnuO666/K2FVdcMf9ca6218mPS8QEAAAAWRI0rPYD67vrrr4+DDz44nnvuuXx/lVVWmen+/fv3j7fffjseeuihHGS9//778f333+dtL730UvTs2TMee+yxWG211aJp06YzPM6ECRPyUjZ+/Ph59pwAAAAA5jeh1FxKlU7nnntudU+oWRk1alSuhOrevXu+v8IKK1RvS9VTyRJLLBHt2rWb6XEGDBgQp59++lyOHgAAAKAyTN+bS2uvvfYc7Z+qqm699dbo1q1bHH/88fH888//pPP269cvxo0bV72MHj36Jx0HAAAAoBKEUnOpZcuW1bcbNvy/l7NUKlWvmzRpUq39t9pqq/j444/jqKOOik8//TQ23XTTOPbYY+f4vM2aNYuqqqpaCwAAAEB9IZSah8rT7z777LPqdTWbntfcb6+99oobb7wxLr744rj66qvz+nIPqR9//LGwMQMAAABUgp5S81C6mt66664bZ599dr6S3pgxY+Lkk0+utc8pp5ySp/ylRuapUfn9998fXbp0yduWWmqpfIyHH344ll122WjevHm0bt26Qs8GAAAAYP5RKTWP/fWvf43Jkyfn4Klv375x1lln1dqeqqFSP6iuXbvGhhtuGI0aNco9ppLGjRvHpZdeGldddVUsvfTSsf3221foWQAAAADMXw1KNRsgUW+NHz8+V1Vt/JejovEizSo9HOqIR343oNJDAAAAYCHNKMaNGzfTHtgqpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAoXOPiT8n8NGiX06KqqqrSwwAAAACYKZVSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4RoXf0rmp30fOimaLNKs0sOgYDdvd36lhwAAAABzRKUUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAAABQOKHUT9S7d+/o27fvXB9n5MiR0aBBgxg2bNg8GRcAAABAfSCUAgAAAKBwQikAAAAACieUmg3ffvtt7LnnnrHoootG+/bt44ILLqi1/YYbboju3btHq1atol27drH77rvHmDFjqrd/+eWXsccee8SSSy4ZLVq0iE6dOsV1111X6xgffvhhbLzxxrHIIovEmmuuGS+88EJhzw8AAACgaEKp2XDcccfFU089Fffee2888sgjMXjw4Hj11Vert0+aNCnOPPPMeP3112PQoEG5T9Tee+9dvb1///7x9ttvx0MPPRTvvPNOXHnlldG2bdta5zjppJPi2GOPzb2lVl555dhtt91i8uTJMxzThAkTYvz48bUWAAAAgPqicaUHUNd988038Ze//CVuvPHG2HTTTfO666+/PpZddtnqffr06VN9u2PHjnHppZdGjx498mNTddWoUaNirbXWytVUyQorrDDNeVIgtc022+Tbp59+eqy22mrx/vvvxyqrrDLdcQ0YMCDvBwAAAFAfqZSahQ8++CAmTpwY66yzTvW6xRdfPDp37lx9f+jQobHddtvFcsstl6fwbbTRRnl9CqOSgw8+OG699dbo1q1bHH/88fH8889Pc56uXbtW305TBJOaUwCn1q9fvxg3blz1Mnr06Hn0jAEAAADmP6HUPOg3tcUWW0RVVVXcdNNN8fLLL8c999yTt6UwK9lqq63i448/jqOOOio+/fTTXHGVKqNqatKkSfXtBg0a5J9TpkyZ4XmbNWuWz1lzAQAAAKgvhFKz8POf/zwHRkOGDKnVuPy9997Lt999990YO3ZsnH322bHBBhvk6XbTq3BKTc732muvPA3w4osvjquvvrrQ5wEAAABQl+gpNQupJ9S+++6bm50vscQSsdRSS+Wm5A0b/l+el6bsNW3aNC677LI46KCD4p///Gduel7TKaecEmuvvXbuE5UalN9///3RpUuXCj0jAAAAgMpTKTUbzjvvvFwFlfpGbbbZZvHLX/4yh0zlCqiBAwfGHXfcEauuumqumDr//PNrPT6FVqkHVOobteGGG0ajRo1yjykAAACAhVWDUqlUqvQgmHvjx4+P1q1bxy63HhZNFmlW6eFQsJu3qx2EAgAAQKUzinRhtpn1wFYpBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhGhd/Suanv2z1v1FVVVXpYQAAAADMlEopAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcI2LPyXzU7+nj41mLZtWehjMgQs3vrzSQwAAAIDCqZQCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5Saz1ZYYYW4+OKLKz0MAAAAgDpFKAUAAABA4YRSAAAAABROKDWXevfuHYcddlheWrduHW3bto3+/ftHqVSq3ue7776LPn36RKtWrWK55ZaLq6++unrbyJEjo0GDBnHrrbfG+uuvH82bN4/VV189nnrqqQo9IwAAAID5Tyg1D1x//fXRuHHjeOmll+KSSy6JCy+8MK699trq7RdccEF07949XnvttTjkkEPi4IMPjuHDh9c6xnHHHRfHHHNM3me99daL7bbbLsaOHTvDc06YMCHGjx9fawEAAACoL4RS80CHDh3ioosuis6dO8cee+wRhx9+eL5ftvXWW+cwaqWVVooTTjghV1M9+eSTtY6RKq123nnn6NKlS1x55ZW56uovf/nLDM85YMCAvE95SWMAAAAAqC+EUvPAuuuum6fglaVKpxEjRsSPP/6Y73ft2rV6W9qvXbt2MWbMmFrHSI8pS1VXqbLqnXfemeE5+/XrF+PGjateRo8ePY+fFQAAAMD803g+Hpv/X5MmTWrdT8HUlClT5uqYzZo1ywsAAABAfaRSah4YMmRIrfsvvvhidOrUKRo1ajTbx0iPKZs8eXIMHTo0T+UDAAAAWBCplJoHRo0aFUcffXQceOCB8eqrr8Zll12Wm5vPiT/96U85yEpBVOpH9eWXX+Yr9gEAAAAsiIRS88Cee+4Z33//ffTs2TNXRx155JFxwAEHzNExzj777LwMGzYsN0S/7777ckN0AAAAgAWRUGoe9Yy6+OKL81XzpjZy5Mhp1qXgaWqpQmrqaYAAAAAACyo9pQAAAAAonFAKAAAAgMKZvjeXBg8ePFePX2GFFaJUKs2z8QAAAADUByqlAAAAACicUAoAAACAwgmlAAAAACicUAoAAACAwml0voAZsOH5UVVVVelhAAAAAMyUSikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwjYs/JfPT+c8fEM1bNq30MBZKf9jgb5UeAgAAANQbKqUAAAAAKJxQCgAAAIDCCaUAAAAAKJxQCgAAAIDCCaUAAAAAKJxQCgAAAIDCCaUAAAAAKJxQCgAAAIDCLbChVO/evaNv377z9JgDBw6MNm3azNNjAgAAACyMFthQCgAAAIC6SygFAAAAQOEW6FBqypQpcfzxx8fiiy8e7dq1i9NOO61624UXXhhrrLFGtGzZMjp06BCHHHJIfPPNN9NM11tuueVikUUWiR133DHGjh07zTmuvPLK+PnPfx5NmzaNzp07xw033FBr+6hRo2L77bePRRddNKqqquK3v/1t/Oc//6nensbUrVu3uOqqq/I40rnSPuPGjZsvrwkAAABAXbBAh1LXX399Dp2GDBkS5557bpxxxhnx6KOP5m0NGzaMSy+9NN5666283xNPPJEDrLL0mH333TcOO+ywGDZsWGy88cZx1lln1Tr+PffcE0ceeWQcc8wx8c9//jMOPPDA2GeffeLJJ5+sDsVSIPXFF1/EU089lc/94Ycfxq677lrrOO+//37cfvvt8fe//z0efvjheO2113JINjMTJkyI8ePH11oAAAAA6osGpVKpFAtoo/Mff/wxnnnmmep1PXv2jE022STOPvvsafa/884746CDDor//ve/+f7uu++eq5UeeOCB6n1+97vf5dDoq6++yvd79eoVq622Wlx99dXV+6Qqp2+//TY/LoVQW221VXz00Ue5Cip5++2382Neeuml6NGjR66USmHXxx9/HMsss0zeJ51jm222iU8++SRXeE1Petzpp58+zfr+D+0azVs2nYtXjp/qDxv8rdJDAAAAgIpLhTOtW7fOuUqaNbZQVkp17dq11v327dvHmDFj8u3HHnssNt100xwEtWrVKn7/+9/n6Xnfffdd3v7OO+/EOuusU+vx6623Xq37aZ8UTNWU7qf15e0pjCoHUsmqq66ar+BX3idJUwTLgVT5PKnKavjw4TN8bv369ctvbnkZPXr0HL02AAAAAJW0QIdSTZo0qXW/QYMGOewZOXJkbLvttjm0uuuuu2Lo0KHxpz/9Ke8zceLEqA+aNWuW08aaCwAAAEB9sUCHUjOSQqgUTl1wwQWx7rrrxsorrxyffvpprX26dOmS+0rV9OKLL06zz3PPPVdrXbqfqqHK21MFU80qpjR9L03/K+9TboZe8/zpPKnnVWqcDgAAALAgahwLoZVWWikmTZoUl112WWy33XY5SPrzn/9ca58jjjgiT8U7//zzc7Pyf/zjH7nXU03HHXdc7iG11lprxWabbZYbld999915amCS1qUr/O2xxx5x8cUXx+TJk3MD84022ii6d+9efZzmzZvHXnvtlc+V5l2mc6fjzqifFAAAAEB9t1BWSq255ppx4YUXxjnnnBOrr7563HTTTTFgwIBa+6QKqmuuuSYuueSSvP8jjzwSJ598cq19dthhh7w9hUmpeflVV10V1113XW6yXp4ueO+998Ziiy0WG264YQ6pOnbsGLfddts0IdlOO+0UW2+9dWy++eZ5WuEVV1xRwCsBAAAAUBkL7NX36ot0Fb1BgwbFsGHD5klne1ffqxxX3wMAAIBw9T0AAAAA6i6hFAAAAACFE0rVgel7czt1DwAAAKC+EUoBAAAAUDihFAAAAACFE0oBAAAAUDihFAAAAACFE0oBAAAAULjGxZ+S+enY9a+OqqqqSg8DAAAAYKZUSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIVrXPwpmZ8GvrRrtGjZpNLDWODtv959lR4CAAAA1GsqpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMIJpQAAAAAonFAKAAAAgMItNKFU796944gjjojjjz8+Fl988WjXrl2cdtppedvIkSOjQYMGMWzYsOr9v/rqq7xu8ODB+X76me7/4x//iLXWWitatGgRm2yySYwZMyYeeuih6NKlS1RVVcXuu+8e3333Xa3zHnbYYXlp3bp1tG3bNvr37x+lUilvP+OMM2L11VefZrzdunXL+wEAAAAsiBaaUCq5/vrro2XLljFkyJA499xzcyD06KOPztExUpB1+eWXx/PPPx+jR4+O3/72t3HxxRfHzTffHA888EA88sgjcdlll01z3saNG8dLL70Ul1xySVx44YVx7bXX5m19+vSJd955J15++eXq/V977bV44403Yp999plHzxwAAACgbmkcC5GuXbvGqaeemm936tQph0uPP/54vj27zjrrrOjVq1e+ve+++0a/fv3igw8+iI4dO+Z1u+yySzz55JNxwgknVD+mQ4cOcdFFF+VKq86dO8ebb76Z7++///6x7LLLxhZbbBHXXXdd9OjRI++fbm+00UbVx5yeCRMm5KVs/PjxP+EVAQAAAKiMhgtbKFVT+/bt8/S7n3qMn/3sZ7HIIovUCo/SuqmPue666+ZAqmy99daLESNGxI8//pjvp3DqlltuiR9++CEmTpyYq65SBdXMDBgwIE8HLC8p+AIAAACoLxaqUKpJkya17qegaMqUKdGw4f+9DOU+T8mkSZNmeYz0+Bkdc05st9120axZs7jnnnvi73//ez53qriamVShNW7cuOolTSUEAAAAqC8Wqul7M7Lkkkvmn5999lluYp7UbHo+t1IPq5pefPHFPGWwUaNG+X7qN7XXXnvlaXtNmzaN3/3ud7mR+sykECstAAAAAPWRUCoiB0Bpit3ZZ58dK664Yp5+d/LJJ8+z448aNSqOPvroOPDAA+PVV1/NjdAvuOCCWvvst99++Qp+yXPPPTfPzg0AAABQFwml/n9//etfc+PytddeOzcjT1fn23zzzefJsffcc8/4/vvvo2fPnrk66sgjj4wDDjig1j6pcmr99dePL774ItZZZ515cl4AAACAuqpBqWYjJea53r17R7du3eLiiy+e6X7pbUjB1CGHHJKrquZUuvpeanh+yaNbRouWtftcMe/tv959lR4CAAAA1EnljCL1wK6qqprhfiql6oDPP/88br311vj3v/8d++yzT6WHAwAAADDfCaXqgKWWWiratm0bV199dSy22GKVHg4AAADAfCeUms8GDx48y33MoAQAAAAWNg0rPQAAAAAAFj5CKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHCuvreA2bvnbVFVVVXpYQAAAADMlEopAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcI2LPyXz0wOvbBGLtPS2zm/br/NMpYcAAAAA9ZpKKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKt9CHUr17946+fftOcxsAAACA+afxfDx2vXP33XdHkyZNKj0MAAAAgAWeUKqGxRdfvNJDAAAAAFgoLFTT97799tvYc889Y9FFF4327dvHBRdcUGv71NP3brjhhujevXu0atUq2rVrF7vvvnuMGTOmevvgwYOjQYMG8fjjj+f9FllkkVh//fVj+PDhtY571llnxVJLLZWPs99++8WJJ54Y3bp1q94+ZcqUOOOMM2LZZZeNZs2a5W0PP/zwfH0tAAAAACppoQqljjvuuHjqqafi3nvvjUceeSSHSq+++uoM9580aVKceeaZ8frrr8egQYNi5MiRsffee0+z30knnZQDrldeeSUaN24cffr0qd520003xf/+7//GOeecE0OHDo3lllsurrzyylqPv+SSS/Ljzz///HjjjTdiiy22iF//+tcxYsSIefwKAAAAANQNDUqlUikWAt98800sscQSceONN8ZvfvObvO6LL77I1UkHHHBAXHzxxblSKlUppdvTk0KnHj16xNdff52rrVKotfHGG8djjz0Wm266ad7nwQcfjG222Sa+//77aN68eay77rq5iuryyy+vPs4vf/nLPJ5hw4bl+8sss0wceuih8Yc//KF6n549e+Zz/elPf5ruWCZMmJCXsvHjx0eHDh3i5sfXjUVampU5v22/zjOVHgIAAADUSSmjaN26dYwbNy6qqqpmuN9CUyn1wQcfxMSJE2Odddap1UOqc+fOM3xMqmzabrvtcnVTmnq30UYb5fWjRo2qtV/Xrl2rb6dpgUl5ml+aypcCpppq3k9v1Keffhq9evWqtU+6/84778xwbAMGDMhvcHlJgRQAAABAfbHQhFI/pf9UmkaXEr00Be/ll1+Oe+65J29L4VZNNa/Yl3pMlftEzU/9+vXLiWN5GT169Hw9HwAAAMC8tNCEUj//+c9zeDRkyJDqdV9++**********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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["skills_series = df['primary_skills'].explode().dropna().str.strip()\n", "\n", "# <PERSON><PERSON><PERSON> kỹ năng phổ biến nhất\n", "skill_counts = Counter(skills_series)\n", "top_skills = skill_counts.most_common(20)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> thành DataFrame để vẽ biểu đồ\n", "skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])\n", "\n", "# Vẽ biểu đồ\n", "plt.figure(figsize=(12, 8))\n", "sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n", "plt.title('Top 20 Kỹ năng đư<PERSON><PERSON> yêu cầu nhiều nhất')\n", "plt.xlabel('<PERSON><PERSON> lượ<PERSON> công việc yêu cầu')\n", "plt.ylabel('K<PERSON> năng')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 31, "id": "98b039e8", "metadata": {}, "outputs": [], "source": ["for i in range(len(df)):\n", "    df.at[i, 'id'] = \"CANDIDATE_\" + str(i)"]}, {"cell_type": "code", "execution_count": 32, "id": "69719d17", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Resume</th>\n", "      <th>resume_cleaned</th>\n", "      <th>primary_skills</th>\n", "      <th>secondary_skills</th>\n", "      <th>adjectives</th>\n", "      <th>adverbs</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Data Science</td>\n", "      <td>Skills * Programming Languages: Python (pandas...</td>\n", "      <td>skill programming language python pandas numpy...</td>\n", "      <td>[python, java, javascript, go, angular, flask,...</td>\n", "      <td>[git, docker, elasticsearch, logstash, kibana,...</td>\n", "      <td>[neural, negative, blob, predictive, meta, ran...</td>\n", "      <td>[plotly, well, also, personally, frequently]</td>\n", "      <td>CANDIDATE_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\nMay 2013 to May 2017 B.E...</td>\n", "      <td>education detail may 2013 may 2017 b e uit rgp...</td>\n", "      <td>[python, keras, machine learning]</td>\n", "      <td>[git, github]</td>\n", "      <td>[encode, mixed, less, 5th, dummy, k<PERSON><PERSON>la,...</td>\n", "      <td>[mainly]</td>\n", "      <td>CANDIDATE_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Data Science</td>\n", "      <td>Areas of Interest Deep Learning, Control Syste...</td>\n", "      <td>area interest deep learning control system des...</td>\n", "      <td>[python, java, go, matlab, bash, django, flask...</td>\n", "      <td>[git, github, linux, ubuntu, debian, pycharm, ...</td>\n", "      <td>[deep, mathematic, little, current, hindustan,...</td>\n", "      <td>[basically, henceforth, currently, much, back]</td>\n", "      <td>CANDIDATE_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Data Science</td>\n", "      <td>Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...</td>\n", "      <td>skill â r â python â sap hana â tableau â sap ...</td>\n", "      <td>[python, go, swift, electron, sql server, lstm...</td>\n", "      <td>[git, windows server, visual studio, segment]</td>\n", "      <td>[enable, apart, predictive, analytic, present,...</td>\n", "      <td>[deep, individually, manually, fast, also, act...</td>\n", "      <td>CANDIDATE_3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\n MCA   YMCAUST,  Faridab...</td>\n", "      <td>education detail mca ymcaust faridabad haryana...</td>\n", "      <td>[python, java]</td>\n", "      <td>[]</td>\n", "      <td>[less]</td>\n", "      <td>[]</td>\n", "      <td>CANDIDATE_4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>704</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_704</td>\n", "    </tr>\n", "    <tr>\n", "      <th>705</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_705</td>\n", "    </tr>\n", "    <tr>\n", "      <th>706</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>707</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>708</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_708</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>962 rows × 8 columns</p>\n", "</div>"], "text/plain": ["         Category                                             Resume  \\\n", "0    Data Science  Skills * Programming Languages: Python (pandas...   \n", "1    Data Science  Education Details \\r\\nMay 2013 to May 2017 B.E...   \n", "2    Data Science  Areas of Interest Deep Learning, Control Syste...   \n", "3    Data Science  Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...   \n", "4    Data Science  Education Details \\r\\n MCA   YMCAUST,  Faridab...   \n", "..            ...                                                ...   \n", "704           NaN                                                NaN   \n", "705           NaN                                                NaN   \n", "706           NaN                                                NaN   \n", "707           NaN                                                NaN   \n", "708           NaN                                                NaN   \n", "\n", "                                        resume_cleaned  \\\n", "0    skill programming language python pandas numpy...   \n", "1    education detail may 2013 may 2017 b e uit rgp...   \n", "2    area interest deep learning control system des...   \n", "3    skill â r â python â sap hana â tableau â sap ...   \n", "4    education detail mca ymcaust faridabad haryana...   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                        primary_skills  \\\n", "0    [python, java, javascript, go, angular, flask,...   \n", "1                    [python, keras, machine learning]   \n", "2    [python, java, go, matlab, bash, django, flask...   \n", "3    [python, go, swift, electron, sql server, lstm...   \n", "4                                       [python, java]   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                      secondary_skills  \\\n", "0    [git, docker, elasticsearch, logstash, kibana,...   \n", "1                                        [git, github]   \n", "2    [git, github, linux, ubuntu, debian, pycharm, ...   \n", "3        [git, windows server, visual studio, segment]   \n", "4                                                   []   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                            adjectives  \\\n", "0    [neural, negative, blob, predictive, meta, ran...   \n", "1    [encode, mixed, less, 5th, dummy, k<PERSON><PERSON><PERSON>,...   \n", "2    [deep, mathematic, little, current, hindustan,...   \n", "3    [enable, apart, predictive, analytic, present,...   \n", "4                                               [less]   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                               adverbs             id  \n", "0         [plotly, well, also, personally, frequently]    CANDIDATE_0  \n", "1                                             [mainly]    CANDIDATE_1  \n", "2       [basically, henceforth, currently, much, back]    CANDIDATE_2  \n", "3    [deep, individually, manually, fast, also, act...    CANDIDATE_3  \n", "4                                                   []    CANDIDATE_4  \n", "..                                                 ...            ...  \n", "704                                                NaN  CANDIDATE_704  \n", "705                                                NaN  CANDIDATE_705  \n", "706                                                NaN  CANDIDATE_706  \n", "707                                                NaN  CANDIDATE_707  \n", "708                                                NaN  CANDIDATE_708  \n", "\n", "[962 rows x 8 columns]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 38, "id": "7002b030", "metadata": {}, "outputs": [], "source": ["df.dropna(inplace=True)\n", "df.to_csv('../data/clean/clean_resumes_v2.csv', index=False, encoding='utf-8')"]}, {"cell_type": "code", "execution_count": null, "id": "fcd65fba", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_7524\\3871042410.py:7: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x='Count', y='Category', data=category_counts, palette='viridis')\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1400x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}