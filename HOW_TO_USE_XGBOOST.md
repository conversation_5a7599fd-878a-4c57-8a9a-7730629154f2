# 🤖 XGBoost Model Usage Guide

## 📋 Overview
Hướng dẫn sử dụng model XGBoost đã được train để predict job-candidate matching.

## 🚀 Quick Start

### 1. Chạy Prediction Script
```bash
python xgboost_prediction.py
```

### 2. Chọn Mode:
- **1**: Interactive prediction (nhập từng feature)
- **2**: Batch prediction từ CSV file
- **3**: Tạo sample input file
- **4**: Train model mới

## 📊 Input Features Required

### Core Features (20 features):
| Feature | Description | Range | Example |
|---------|-------------|-------|---------|
| `tfidf_unigram` | Text similarity (unigram) | 0-1 | 0.7 |
| `tfidf_bigram` | Text similarity (bigram) | 0-1 | 0.3 |
| `tfidf_trigram` | Text similarity (trigram) | 0-1 | 0.2 |
| `tfidf_char` | Character similarity | 0-1 | 0.1 |
| `skill_similarity` | Skill matching score | 0-1 | 0.8 |
| `experience_match` | Experience matching | 0-1 | 0.9 |
| `education_match` | Education matching | 0-1 | 1.0 |
| `semantic_similarity` | Semantic similarity | 0-1 | 0.6 |
| `location_compatibility` | Location match | 0-1 | 1.0 |
| `length_ratio` | Text length ratio | 0-1 | 0.5 |
| `skill_count_ratio` | Skill count ratio | 0-1 | 0.7 |
| `required_experience` | Required years | 0+ | 3 |
| `candidate_experience` | Candidate years | 0+ | 4 |
| `required_education` | Required level (1-4) | 1-4 | 2 |
| `candidate_education` | Candidate level (1-4) | 1-4 | 2 |
| `job_skill_count` | Number of job skills | 0+ | 10 |
| `resume_skill_count` | Number of resume skills | 0+ | 8 |
| `job_length` | Job description length | 0+ | 500 |
| `resume_length` | Resume length | 0+ | 800 |
| `exp_edu_interaction` | Experience × Education | 0-1 | 0.9 |

### Education Levels:
- **1**: High school
- **2**: Bachelor's degree
- **3**: Master's degree
- **4**: PhD/Doctorate

## 📝 Usage Examples

### Example 1: Interactive Prediction
```bash
python xgboost_prediction.py
# Select option 1
# Enter values for each feature
```

### Example 2: Batch Prediction
1. Tạo CSV file với format như `sample_input.csv`
2. Chạy script và chọn option 2
3. Nhập path đến CSV file
4. Kết quả sẽ được save vào `prediction_results.csv`

### Example 3: Sample Input
```csv
skill_similarity,experience_match,education_match,tfidf_unigram,...
0.8,0.9,1.0,0.7,...
0.5,0.6,0.8,0.4,...
```

## 🎯 Output Format

### Single Prediction:
```python
{
    'prediction': 2,
    'prediction_label': 'Highly Suitable',
    'probabilities': {
        'Not Suitable': 0.017,
        'Moderately Suitable': 0.175,
        'Highly Suitable': 0.807
    },
    'confidence': 0.807
}
```

### Batch Prediction (CSV):
| Column | Description |
|--------|-------------|
| `prediction` | Predicted class (0, 1, 2) |
| `prediction_label` | Label text |
| `confidence` | Highest probability |
| `prob_not_suitable` | Probability for class 0 |
| `prob_moderately_suitable` | Probability for class 1 |
| `prob_highly_suitable` | Probability for class 2 |

## 📊 Prediction Classes

| Class | Label | Description |
|-------|-------|-------------|
| **0** | Not Suitable | Candidate không phù hợp với job |
| **1** | Moderately Suitable | Candidate phù hợp ở mức trung bình |
| **2** | Highly Suitable | Candidate rất phù hợp với job |

## 🔧 Model Performance
- **Accuracy**: 87.25%
- **Best for**: Class 1 (Moderately Suitable)
- **Confidence**: Typically 80%+ for clear cases

## 📁 Files Generated
- `xgboost_model.pkl`: Trained model
- `sample_input.csv`: Template input file
- `prediction_results.csv`: Batch prediction results

## 💡 Tips for Better Predictions

### 1. Feature Quality:
- **skill_similarity**: Most important feature
- **experience_match**: Second most important
- **exp_edu_interaction**: Key interaction feature

### 2. Input Guidelines:
- Ensure all similarity scores are between 0-1
- Use realistic experience values (0-50 years)
- Education levels should be 1-4
- Text lengths should be positive integers

### 3. Interpretation:
- **Confidence > 80%**: High reliability
- **Confidence 60-80%**: Medium reliability  
- **Confidence < 60%**: Low reliability, manual review needed

## 🚨 Common Issues

### Issue 1: Low Confidence
**Cause**: Ambiguous input features
**Solution**: Check if features are realistic and consistent

### Issue 2: Unexpected Results
**Cause**: Missing or incorrect feature values
**Solution**: Verify all 20 features are provided correctly

### Issue 3: Model Not Found
**Cause**: Model file missing
**Solution**: Run option 4 to train and save new model

## 🔄 Retraining Model
```bash
python xgboost_prediction.py
# Select option 4
# Model will be retrained and saved
```

## 📞 Support
- Check `four_models_results.csv` for model performance
- Review `sample_input.csv` for correct input format
- Ensure all dependencies are installed: `pandas`, `numpy`, `xgboost`, `scikit-learn`

---
**Model Version**: XGBoost 87.25% accuracy
**Last Updated**: Current training session
**Compatible**: Python 3.7+
