{"cells": [{"cell_type": "code", "execution_count": 20, "id": "e22a1aa4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from langdetect import detect\n", "from deep_translator import GoogleTranslator\n", "import re\n", "import spacy\n", "from underthesea import word_tokenize, pos_tag\n", "from nltk.corpus import stopwords\n", "import numpy as np\n", "from collections import defaultdict\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from collections import Counter\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 21, "id": "c77272f1", "metadata": {}, "outputs": [], "source": ["nlp = spacy.load('en_core_web_md')\n", "stop_words = set(stopwords.words('english'))"]}, {"cell_type": "code", "execution_count": 22, "id": "9ad185d1", "metadata": {}, "outputs": [], "source": ["csv_cr = \"../../data/raw/UpdatedResumeDataSet.csv\""]}, {"cell_type": "code", "execution_count": 23, "id": "edef4822", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(csv_cr)"]}, {"cell_type": "code", "execution_count": 24, "id": "61c435a8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Resume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Data Science</td>\n", "      <td>Skills * Programming Languages: Python (pandas...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\nMay 2013 to May 2017 B.E...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Data Science</td>\n", "      <td>Areas of Interest Deep Learning, Control Syste...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Data Science</td>\n", "      <td>Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\n MCA   YMCAUST,  Faridab...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>957</th>\n", "      <td>Testing</td>\n", "      <td>Computer Skills: â¢ Proficient in MS office (...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>958</th>\n", "      <td>Testing</td>\n", "      <td>â Willingness to accept the challenges. â ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>959</th>\n", "      <td>Testing</td>\n", "      <td>PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>960</th>\n", "      <td>Testing</td>\n", "      <td>COMPUTER SKILLS &amp; SOFTWARE KNOWLEDGE MS-Power ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>961</th>\n", "      <td>Testing</td>\n", "      <td>Skill Set OS Windows XP/7/8/8.1/10 Database MY...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>962 rows × 2 columns</p>\n", "</div>"], "text/plain": ["         Category                                             Resume\n", "0    Data Science  Skills * Programming Languages: Python (pandas...\n", "1    Data Science  Education Details \\r\\nMay 2013 to May 2017 B.E...\n", "2    Data Science  Areas of Interest Deep Learning, Control Syste...\n", "3    Data Science  Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...\n", "4    Data Science  Education Details \\r\\n MCA   YMCAUST,  Faridab...\n", "..            ...                                                ...\n", "957       Testing  Computer Skills: â¢ Proficient in MS office (...\n", "958       Testing  â Willingness to accept the challenges. â ...\n", "959       Testing  PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...\n", "960       Testing  COMPUTER SKILLS & SOFTWARE KNOWLEDGE MS-Power ...\n", "961       Testing  Skill Set OS Windows XP/7/8/8.1/10 Database MY...\n", "\n", "[962 rows x 2 columns]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 25, "id": "32a9da5d", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Data Science', 'HR', 'Advocate', 'Arts', 'Web Designing',\n", "       'Mechanical Engineer', 'Sales', 'Health and fitness',\n", "       'Civil Engineer', 'Java Developer', 'Business Analyst',\n", "       'SAP Developer', 'Automation Testing', 'Electrical Engineering',\n", "       'Operations Manager', 'Python Developer', 'DevOps Engineer',\n", "       'Network Security Engineer', 'PM<PERSON>', 'Database', 'Hadoop',\n", "       'ETL Developer', 'DotNet Developer', 'Blockchain', 'Testing'],\n", "      dtype=object)"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Category'].unique()"]}, {"cell_type": "code", "execution_count": 26, "id": "85b88a7a", "metadata": {}, "outputs": [], "source": ["list_job_to_remove = ['Sales','Health and fitness','PMO','Arts']\n", "\n", "df = df[~df['Category'].isin(list_job_to_remove)]"]}, {"cell_type": "code", "execution_count": 27, "id": "49e5393e", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['Data Science', 'HR', 'Advocate', 'Web Designing',\n", "       'Mechanical Engineer', 'Civil Engineer', 'Java Developer',\n", "       'Business Analyst', 'SAP Developer', 'Automation Testing',\n", "       'Electrical Engineering', 'Operations Manager', 'Python Developer',\n", "       'DevOps Engineer', 'Network Security Engineer', 'Database',\n", "       'Hadoop', 'ETL Developer', 'DotNet Developer', 'Blockchain',\n", "       'Testing'], dtype=object)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Category'].unique()"]}, {"cell_type": "code", "execution_count": 28, "id": "0a31f2ac", "metadata": {}, "outputs": [], "source": ["def clean_text(text):\n", "    text = text.lower()\n", "    text = re.sub(r'http\\S+|#\\S+|@\\S+|[^\\w\\s]|[\\*\\•\\n]', ' ', text)\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    doc = nlp(text)\n", "    tokens = [token.lemma_ for token in doc if token.text not in stop_words]\n", "    return ' '.join(tokens)"]}, {"cell_type": "code", "execution_count": 29, "id": "56d74156", "metadata": {}, "outputs": [], "source": ["df['resume_cleaned'] = df['Resume'].apply(clean_text)"]}, {"cell_type": "code", "execution_count": 30, "id": "3d13cf92", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Resume</th>\n", "      <th>resume_cleaned</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Data Science</td>\n", "      <td>Skills * Programming Languages: Python (pandas...</td>\n", "      <td>skill programming language python pandas numpy...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\nMay 2013 to May 2017 B.E...</td>\n", "      <td>education detail may 2013 may 2017 b e uit rgp...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Data Science</td>\n", "      <td>Areas of Interest Deep Learning, Control Syste...</td>\n", "      <td>area interest deep learning control system des...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Data Science</td>\n", "      <td>Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...</td>\n", "      <td>skill â r â python â sap hana â tableau â sap ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\n MCA   YMCAUST,  Faridab...</td>\n", "      <td>education detail mca ymcaust faridabad haryana...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>957</th>\n", "      <td>Testing</td>\n", "      <td>Computer Skills: â¢ Proficient in MS office (...</td>\n", "      <td>computer skill â proficient ms office word bas...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>958</th>\n", "      <td>Testing</td>\n", "      <td>â Willingness to accept the challenges. â ...</td>\n", "      <td>â willingness accept challenge â positive thin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>959</th>\n", "      <td>Testing</td>\n", "      <td>PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...</td>\n", "      <td>personal skill â quick learner â eagerness lea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>960</th>\n", "      <td>Testing</td>\n", "      <td>COMPUTER SKILLS &amp; SOFTWARE KNOWLEDGE MS-Power ...</td>\n", "      <td>computer skill software knowledge ms power poi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>961</th>\n", "      <td>Testing</td>\n", "      <td>Skill Set OS Windows XP/7/8/8.1/10 Database MY...</td>\n", "      <td>skill set os windows xp 7 8 8 1 10 database my...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>826 rows × 3 columns</p>\n", "</div>"], "text/plain": ["         Category                                             Resume  \\\n", "0    Data Science  Skills * Programming Languages: Python (pandas...   \n", "1    Data Science  Education Details \\r\\nMay 2013 to May 2017 B.E...   \n", "2    Data Science  Areas of Interest Deep Learning, Control Syste...   \n", "3    Data Science  Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...   \n", "4    Data Science  Education Details \\r\\n MCA   YMCAUST,  Faridab...   \n", "..            ...                                                ...   \n", "957       Testing  Computer Skills: â¢ Proficient in MS office (...   \n", "958       Testing  â Willingness to accept the challenges. â ...   \n", "959       Testing  PERSONAL SKILLS â¢ Quick learner, â¢ Eagerne...   \n", "960       Testing  COMPUTER SKILLS & SOFTWARE KNOWLEDGE MS-Power ...   \n", "961       Testing  Skill Set OS Windows XP/7/8/8.1/10 Database MY...   \n", "\n", "                                        resume_cleaned  \n", "0    skill programming language python pandas numpy...  \n", "1    education detail may 2013 may 2017 b e uit rgp...  \n", "2    area interest deep learning control system des...  \n", "3    skill â r â python â sap hana â tableau â sap ...  \n", "4    education detail mca ymcaust faridabad haryana...  \n", "..                                                 ...  \n", "957  computer skill â proficient ms office word bas...  \n", "958  â willingness accept challenge â positive thin...  \n", "959  personal skill â quick learner â eagerness lea...  \n", "960  computer skill software knowledge ms power poi...  \n", "961  skill set os windows xp 7 8 8 1 10 database my...  \n", "\n", "[826 rows x 3 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 31, "id": "70fabb74", "metadata": {}, "outputs": [], "source": ["\n", "def read_skills(file_path):\n", "    skills = []\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            if line and not line.startswith('#'):\n", "                skills.append(line.lower())\n", "    return skills\n", "def extract_primary_skills(text):\n", "    # <PERSON><PERSON> lý trư<PERSON><PERSON> hợp text không phải chuỗi\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in primary_skills if skill in text.lower()]\n", "\n", "def extract_secondary_skills(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in secondary_skills if skill in text.lower()]\n", "\n", "def extract_adjectives(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADJ']))\n", "\n", "def extract_adverbs(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADV']))\n"]}, {"cell_type": "code", "execution_count": 32, "id": "3a55dcaf", "metadata": {}, "outputs": [], "source": ["primary_skills = read_skills('../../data/primary_skills.txt')\n", "secondary_skills = read_skills('../../data/secondary_skills.txt')"]}, {"cell_type": "code", "execution_count": 33, "id": "3335400e", "metadata": {}, "outputs": [], "source": ["df['primary_skills'] = df['resume_cleaned'].apply(extract_primary_skills)\n", "df['secondary_skills'] = df['resume_cleaned'].apply(extract_secondary_skills)\n", "df['adjectives'] = df['resume_cleaned'].apply(extract_adjectives)\n", "df['adverbs'] = df['resume_cleaned'].apply(extract_adverbs)"]}, {"cell_type": "code", "execution_count": 34, "id": "f7e914f3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_14576\\1403453381.py:12: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["skills_series = df['primary_skills'].explode().dropna().str.strip()\n", "\n", "# <PERSON><PERSON><PERSON> kỹ năng phổ biến nhất\n", "skill_counts = Counter(skills_series)\n", "top_skills = skill_counts.most_common(20)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> thành DataFrame để vẽ biểu đồ\n", "skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])\n", "\n", "# Vẽ biểu đồ\n", "plt.figure(figsize=(12, 8))\n", "sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n", "plt.title('Top 20 Kỹ năng đư<PERSON><PERSON> yêu cầu nhiều nhất')\n", "plt.xlabel('<PERSON><PERSON> lượ<PERSON> công việc yêu cầu')\n", "plt.ylabel('K<PERSON> năng')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 35, "id": "98b039e8", "metadata": {}, "outputs": [], "source": ["for i in range(len(df)):\n", "    df.at[i, 'id'] = \"CANDIDATE_\" + str(i)"]}, {"cell_type": "code", "execution_count": 36, "id": "69719d17", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Category</th>\n", "      <th>Resume</th>\n", "      <th>resume_cleaned</th>\n", "      <th>primary_skills</th>\n", "      <th>secondary_skills</th>\n", "      <th>adjectives</th>\n", "      <th>adverbs</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Data Science</td>\n", "      <td>Skills * Programming Languages: Python (pandas...</td>\n", "      <td>skill programming language python pandas numpy...</td>\n", "      <td>[python, java, javascript, angular, flask, mys...</td>\n", "      <td>[git, docker, elasticsearch, logstash, kibana,...</td>\n", "      <td>[present, automate, inbuilt, predictive, negat...</td>\n", "      <td>[well, plotly, frequently, personally, also]</td>\n", "      <td>CANDIDATE_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\nMay 2013 to May 2017 B.E...</td>\n", "      <td>education detail may 2013 may 2017 b e uit rgp...</td>\n", "      <td>[python, keras, machine learning, data scientist]</td>\n", "      <td>[git, github]</td>\n", "      <td>[k<PERSON><PERSON><PERSON>, professional, 5th, dummy, less, ...</td>\n", "      <td>[mainly]</td>\n", "      <td>CANDIDATE_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Data Science</td>\n", "      <td>Areas of Interest Deep Learning, Control Syste...</td>\n", "      <td>area interest deep learning control system des...</td>\n", "      <td>[python, java, matlab, bash, django, flask, el...</td>\n", "      <td>[git, github, linux, ubuntu, debian, pycharm, ...</td>\n", "      <td>[ide, electrical, mathematic, little, senior, ...</td>\n", "      <td>[back, currently, much, henceforth, basically]</td>\n", "      <td>CANDIDATE_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Data Science</td>\n", "      <td>Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...</td>\n", "      <td>skill â r â python â sap hana â tableau â sap ...</td>\n", "      <td>[python, swift, electron, sql server, lstm, de...</td>\n", "      <td>[git, windows server, visual studio, segment]</td>\n", "      <td>[present, enough, close, historic, predictive,...</td>\n", "      <td>[successfully, manually, fast, actively, deep,...</td>\n", "      <td>CANDIDATE_3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Data Science</td>\n", "      <td>Education Details \\r\\n MCA   YMCAUST,  Faridab...</td>\n", "      <td>education detail mca ymcaust faridabad haryana...</td>\n", "      <td>[python, java]</td>\n", "      <td>[]</td>\n", "      <td>[less]</td>\n", "      <td>[]</td>\n", "      <td>CANDIDATE_4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>704</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_704</td>\n", "    </tr>\n", "    <tr>\n", "      <th>705</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_705</td>\n", "    </tr>\n", "    <tr>\n", "      <th>706</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_706</td>\n", "    </tr>\n", "    <tr>\n", "      <th>707</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_707</td>\n", "    </tr>\n", "    <tr>\n", "      <th>708</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>CANDIDATE_708</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>962 rows × 8 columns</p>\n", "</div>"], "text/plain": ["         Category                                             Resume  \\\n", "0    Data Science  Skills * Programming Languages: Python (pandas...   \n", "1    Data Science  Education Details \\r\\nMay 2013 to May 2017 B.E...   \n", "2    Data Science  Areas of Interest Deep Learning, Control Syste...   \n", "3    Data Science  Skills â¢ R â¢ Python â¢ SAP HANA â¢ Table...   \n", "4    Data Science  Education Details \\r\\n MCA   YMCAUST,  Faridab...   \n", "..            ...                                                ...   \n", "704           NaN                                                NaN   \n", "705           NaN                                                NaN   \n", "706           NaN                                                NaN   \n", "707           NaN                                                NaN   \n", "708           NaN                                                NaN   \n", "\n", "                                        resume_cleaned  \\\n", "0    skill programming language python pandas numpy...   \n", "1    education detail may 2013 may 2017 b e uit rgp...   \n", "2    area interest deep learning control system des...   \n", "3    skill â r â python â sap hana â tableau â sap ...   \n", "4    education detail mca ymcaust faridabad haryana...   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                        primary_skills  \\\n", "0    [python, java, javascript, angular, flask, mys...   \n", "1    [python, keras, machine learning, data scientist]   \n", "2    [python, java, matlab, bash, django, flask, el...   \n", "3    [python, swift, electron, sql server, lstm, de...   \n", "4                                       [python, java]   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                      secondary_skills  \\\n", "0    [git, docker, elasticsearch, logstash, kibana,...   \n", "1                                        [git, github]   \n", "2    [git, github, linux, ubuntu, debian, pycharm, ...   \n", "3        [git, windows server, visual studio, segment]   \n", "4                                                   []   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                            adjectives  \\\n", "0    [present, automate, inbuilt, predictive, negat...   \n", "1    [<PERSON><PERSON><PERSON><PERSON>, professional, 5th, dummy, less, ...   \n", "2    [ide, electrical, mathematic, little, senior, ...   \n", "3    [present, enough, close, historic, predictive,...   \n", "4                                               [less]   \n", "..                                                 ...   \n", "704                                                NaN   \n", "705                                                NaN   \n", "706                                                NaN   \n", "707                                                NaN   \n", "708                                                NaN   \n", "\n", "                                               adverbs             id  \n", "0         [well, plotly, frequently, personally, also]    CANDIDATE_0  \n", "1                                             [mainly]    CANDIDATE_1  \n", "2       [back, currently, much, henceforth, basically]    CANDIDATE_2  \n", "3    [successfully, manually, fast, actively, deep,...    CANDIDATE_3  \n", "4                                                   []    CANDIDATE_4  \n", "..                                                 ...            ...  \n", "704                                                NaN  CANDIDATE_704  \n", "705                                                NaN  CANDIDATE_705  \n", "706                                                NaN  CANDIDATE_706  \n", "707                                                NaN  CANDIDATE_707  \n", "708                                                NaN  CANDIDATE_708  \n", "\n", "[962 rows x 8 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 37, "id": "7002b030", "metadata": {}, "outputs": [], "source": ["df.dropna(inplace=True)\n", "df.to_csv('../../data/clean/clean_resumes_v2.csv', index=False, encoding='utf-8')"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}