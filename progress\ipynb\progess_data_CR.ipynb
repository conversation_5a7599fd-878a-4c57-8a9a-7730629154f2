import pandas as pd
from langdetect import detect
from deep_translator import GoogleTranslator
import re
import spacy
from underthesea import word_tokenize, pos_tag
from nltk.corpus import stopwords
import numpy as np
from collections import defaultdict
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns

nlp = spacy.load('en_core_web_md')
stop_words = set(stopwords.words('english'))

csv_cr = "../data/raw/UpdatedResumeDataSet.csv"

df = pd.read_csv(csv_cr)

pd.DataFrame(df)

df['Category'].unique()

list_job_to_remove = ['Sales','Health and fitness','PMO','Arts']

df = df[~df['Category'].isin(list_job_to_remove)]

df['Category'].unique()

def clean_text(text):
    text = text.lower()
    text = re.sub(r'http\S+|#\S+|@\S+|[^\w\s]|[\*\•\n]', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()
    doc = nlp(text)
    tokens = [token.lemma_ for token in doc if token.text not in stop_words]
    return ' '.join(tokens)

df['resume_cleaned'] = df['Resume'].apply(clean_text)

pd.DataFrame(df)


def read_skills(file_path):
    skills = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                skills.append(line.lower())
    return skills
def extract_primary_skills(text):
    # Xử lý trường hợp text không phải chuỗi
    if not isinstance(text, str) or pd.isna(text):
        return []
    return [skill for skill in primary_skills if skill in text.lower()]

def extract_secondary_skills(text):
    if not isinstance(text, str) or pd.isna(text):
        return []
    return [skill for skill in secondary_skills if skill in text.lower()]

def extract_adjectives(text):
    if not isinstance(text, str) or pd.isna(text):
        return []
    doc = nlp(text)
    return list(set([token.text for token in doc if token.pos_ == 'ADJ']))

def extract_adverbs(text):
    if not isinstance(text, str) or pd.isna(text):
        return []
    doc = nlp(text)
    return list(set([token.text for token in doc if token.pos_ == 'ADV']))


primary_skills = read_skills('../data/primary_skills.txt')
secondary_skills = read_skills('../data/secondary_skills.txt')

df['primary_skills'] = df['resume_cleaned'].apply(extract_primary_skills)
df['secondary_skills'] = df['resume_cleaned'].apply(extract_secondary_skills)
df['adjectives'] = df['resume_cleaned'].apply(extract_adjectives)
df['adverbs'] = df['resume_cleaned'].apply(extract_adverbs)

skills_series = df['primary_skills'].explode().dropna().str.strip()

# Đếm kỹ năng phổ biến nhất
skill_counts = Counter(skills_series)
top_skills = skill_counts.most_common(20)

# Chuyển thành DataFrame để vẽ biểu đồ
skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])

# Vẽ biểu đồ
plt.figure(figsize=(12, 8))
sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')
plt.title('Top 20 Kỹ năng được yêu cầu nhiều nhất')
plt.xlabel('Số lượng công việc yêu cầu')
plt.ylabel('Kỹ năng')
plt.tight_layout()
plt.show()

for i in range(len(df)):
    df.at[i, 'id'] = "CANDIDATE_" + str(i)

pd.DataFrame(df)

df.dropna(inplace=True)
df.to_csv('../data/clean/clean_resumes_v2.csv', index=False, encoding='utf-8')

