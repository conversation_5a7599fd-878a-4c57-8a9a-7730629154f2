#!/usr/bin/env python3
"""
Test script for Job-Resume Matching API
"""

import requests
import json
import time

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test health check endpoints"""
    print("🔍 Testing health check endpoints...")
    
    # Basic health check
    response = requests.get(f"{BASE_URL}/")
    print(f"GET / - Status: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {response.json()}")
    
    # Detailed health check
    response = requests.get(f"{BASE_URL}/health")
    print(f"GET /health - Status: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {response.json()}")
    
    print()

def test_model_info():
    """Test model information endpoint"""
    print("📊 Testing model info endpoint...")
    
    response = requests.get(f"{BASE_URL}/model/info")
    print(f"GET /model/info - Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Model Type: {data['model_type']}")
        print(f"Feature Count: {data['feature_count']}")
        print(f"Features: {data['feature_names']}")
        print(f"Performance: {data['model_performance']}")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_single_prediction():
    """Test single prediction endpoint"""
    print("🎯 Testing single prediction...")
    
    # Test data
    test_cases = [
        {
            "name": "High similarity",
            "data": {
                "primary_skills_sim": 0.85,
                "secondary_skills_sim": 0.75,
                "adjectives_sim": 0.65
            }
        },
        {
            "name": "Medium similarity",
            "data": {
                "primary_skills_sim": 0.55,
                "secondary_skills_sim": 0.45,
                "adjectives_sim": 0.35
            }
        },
        {
            "name": "Low similarity",
            "data": {
                "primary_skills_sim": 0.25,
                "secondary_skills_sim": 0.15,
                "adjectives_sim": 0.10
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"Testing {test_case['name']}:")
        response = requests.post(
            f"{BASE_URL}/predict",
            json=test_case['data'],
            headers={"Content-Type": "application/json"}
        )
        
        print(f"  Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"  Prediction: {result['suitability_label']} (Class {result['suitability_class']})")
            print(f"  Confidence: {max(result['confidence_scores'].values()):.3f}")
        else:
            print(f"  Error: {response.text}")
        print()

def test_batch_prediction():
    """Test batch prediction endpoint"""
    print("📦 Testing batch prediction...")
    
    batch_data = {
        "features_list": [
            {
                "primary_skills_sim": 0.85,
                "secondary_skills_sim": 0.75,
                "adjectives_sim": 0.65
            },
            {
                "primary_skills_sim": 0.55,
                "secondary_skills_sim": 0.45,
                "adjectives_sim": 0.35
            },
            {
                "primary_skills_sim": 0.25,
                "secondary_skills_sim": 0.15,
                "adjectives_sim": 0.10
            }
        ]
    }
    
    response = requests.post(
        f"{BASE_URL}/predict/batch",
        json=batch_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Total predictions: {result['total_predictions']}")
        
        for i, prediction in enumerate(result['predictions']):
            print(f"  {i+1}. {prediction['suitability_label']} (Class {prediction['suitability_class']})")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_feature_engineering():
    """Test feature engineering endpoint"""
    print("🔧 Testing feature engineering...")
    
    test_data = {
        "primary_skills_sim": 0.75,
        "secondary_skills_sim": 0.60,
        "adjectives_sim": 0.45
    }
    
    response = requests.post(
        f"{BASE_URL}/features/engineer",
        json=test_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Input features: {result['input_features']}")
        print(f"Total features: {result['total_features']}")
        print(f"Feature vector shape: {result['feature_vector_shape']}")
        print("Engineered features:")
        for feature, value in result['engineered_features'].items():
            print(f"  {feature}: {value:.4f}")
    else:
        print(f"Error: {response.text}")
    
    print()

def test_error_handling():
    """Test error handling"""
    print("⚠️ Testing error handling...")
    
    # Test invalid input
    invalid_data = {
        "primary_skills_sim": 1.5,  # Invalid: > 1.0
        "secondary_skills_sim": -0.1,  # Invalid: < 0.0
        "adjectives_sim": 0.5
    }
    
    response = requests.post(
        f"{BASE_URL}/predict",
        json=invalid_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Invalid input test - Status: {response.status_code}")
    if response.status_code != 200:
        print(f"Expected error: {response.status_code}")
    
    # Test missing fields
    incomplete_data = {
        "primary_skills_sim": 0.5
        # Missing secondary_skills_sim and adjectives_sim
    }
    
    response = requests.post(
        f"{BASE_URL}/predict",
        json=incomplete_data,
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Missing fields test - Status: {response.status_code}")
    if response.status_code != 200:
        print(f"Expected error: {response.status_code}")
    
    print()

def main():
    """Run all tests"""
    print("🧪 TESTING JOB-RESUME MATCHING API")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    print("Waiting for server to be ready...")
    time.sleep(2)
    
    try:
        # Run tests
        test_health_check()
        test_model_info()
        test_single_prediction()
        test_batch_prediction()
        test_feature_engineering()
        test_error_handling()
        
        print("✅ All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server!")
        print("Make sure the API is running on http://localhost:8000")
        print("Run: cd api/app && python main.py")
    except Exception as e:
        print(f"❌ Test error: {str(e)}")

if __name__ == "__main__":
    main()
