# 🎯 Four Categories Extraction
## Linguistic Analysis: Primary Skills, Secondary Skills, Adjectives, Adverbs

### 📋 Overview
This notebook implements the **four categories extraction methodology** from academic research papers, specifically designed for job-resume text analysis following linguistic research standards.

---

## 🎯 Research Methodology

### Based on Academic Literature
This approach follows the **linguistic analysis framework** used in job-resume matching research papers, where text is decomposed into four distinct linguistic categories:

1. **Primary Skills** - Core technical competencies
2. **Secondary Skills** - Supporting skills and methodologies  
3. **Adjectives** - Descriptive qualifiers and experience levels
4. **Adverbs** - Performance and manner descriptors

### Why Four Categories?
- **Comprehensive Coverage**: Captures different aspects of professional profiles
- **Linguistic Precision**: Uses POS tagging for accurate extraction
- **Research Validation**: Proven methodology in academic literature
- **Feature Engineering**: Creates rich features for ML models

---

## 📂 Input/Output

### Input Data
- `data/clean_resumes.csv` - Preprocessed candidate resumes
- `data/clean_jobs.csv` - Preprocessed job descriptions

### Output Data
- `data/primary_skills.csv` - Core technical skills
- `data/secondary_skills.csv` - Supporting skills and soft skills
- `data/adjectives.csv` - Descriptive adjectives with POS tags
- `data/adverbs.csv` - Performance adverbs with POS tags
- `data/four_categories_summary.json` - Extraction statistics

---

## 🔧 Category Definitions

### 1. Primary Skills (Core Technical)
#### Programming Languages
- Python, Java, JavaScript, C++, C#, PHP, Ruby, Go, Rust, Swift, Kotlin, TypeScript, Scala, R, MATLAB

#### Frameworks & Libraries  
- React, Angular, Vue, Node.js, Django, Flask, Spring, Laravel, TensorFlow, PyTorch, Keras

#### Databases
- MySQL, PostgreSQL, MongoDB, Redis, Oracle, SQL Server, Elasticsearch, Cassandra

#### Cloud & DevOps
- AWS, Azure, GCP, Docker, Kubernetes, Jenkins, Terraform, Ansible

### 2. Secondary Skills (Supporting)
#### Soft Skills
- Leadership, Communication, Teamwork, Problem Solving, Analytical Thinking, Creativity

#### Methodologies
- Agile, Scrum, Kanban, DevOps, CI/CD, TDD, BDD, Microservices, REST API

#### Tools & Platforms
- Git, JIRA, Confluence, Figma, Tableau, Power BI, Excel

#### Operating Systems
- Linux, Windows, macOS, Ubuntu, CentOS

### 3. Adjectives (Descriptive Qualifiers)
#### Experience Level
- Senior, Junior, Experienced, Skilled, Expert, Advanced, Professional, Certified

#### Quality Descriptors
- Excellent, Outstanding, Strong, Proven, Reliable, Efficient, Innovative, Creative

#### Technical Descriptors
- Technical, Hands-on, Comprehensive, Specialized, Versatile, Flexible

#### Work Style
- Collaborative, Independent, Self-motivated, Proactive, Results-driven

### 4. Adverbs (Performance Descriptors)
#### Performance
- Efficiently, Effectively, Successfully, Consistently, Reliably, Accurately

#### Speed & Frequency
- Quickly, Rapidly, Frequently, Regularly, Continuously, Daily

#### Quality
- Excellently, Professionally, Skillfully, Expertly, Competently, Confidently

#### Manner
- Creatively, Innovatively, Analytically, Strategically, Methodically

---

## 🚀 Usage Instructions

### 1. Prerequisites
```bash
# Ensure clean data exists
ls data/clean_resumes.csv data/clean_jobs.csv

# Install required packages
pip install pandas nltk scikit-learn
```

### 2. Run Extraction
```bash
cd visiualize
jupyter notebook extract_four_categories.ipynb
```

### 3. Execute All Cells
- Processing takes 10-20 minutes depending on data size
- Monitor progress through print statements
- NLTK data will be downloaded automatically

### 4. Verify Output
```bash
ls data/primary_skills.csv data/secondary_skills.csv data/adjectives.csv data/adverbs.csv
```

---

## 📊 Expected Results

### Extraction Statistics
```
📊 Primary Skills: 50,000+ records
   • Unique skills: 200+ distinct technical skills
   • Categories: Programming, Frameworks, Databases, Cloud

📊 Secondary Skills: 30,000+ records  
   • Unique skills: 150+ supporting skills
   • Categories: Soft skills, Methodologies, Tools, OS

📊 Adjectives: 80,000+ records
   • Job-related: 15,000+ professional descriptors
   • General: 65,000+ quality descriptors

📊 Adverbs: 25,000+ records
   • Job-related: 8,000+ performance descriptors
   • General: 17,000+ manner descriptors
```

### Data Structure Example

#### Primary Skills CSV
| record_id | source_type | skill | skill_category | category | job_title | company |
|-----------|-------------|-------|----------------|----------|-----------|---------|
| resume_1 | resume | python | programming_languages | Data Science | | |
| job_123 | job | react | frameworks | | Frontend Developer | TechCorp |

#### Adjectives CSV
| record_id | source_type | adjective | pos_tag | adj_type | category | job_title |
|-----------|-------------|-----------|----------|----------|----------|-----------|
| resume_1 | resume | experienced | JJ | job_related | Data Science | |
| job_123 | job | senior | JJ | job_related | | Senior Developer |

---

## 🔍 Technical Implementation

### NLP Processing Pipeline
1. **Text Tokenization**: NLTK word tokenization
2. **POS Tagging**: Part-of-speech identification
3. **Skill Matching**: Pattern matching against skill dictionaries
4. **Linguistic Filtering**: Extract adjectives (JJ*) and adverbs (RB*)
5. **Context Classification**: Job-related vs general terms

### Quality Assurance
- **Skill Validation**: Comprehensive technical skill dictionaries
- **POS Accuracy**: NLTK's proven POS tagger
- **Context Filtering**: Job-specific term identification
- **Duplicate Removal**: Automatic deduplication

### Performance Optimization
- **Batch Processing**: Efficient text processing
- **Memory Management**: Optimized for large datasets
- **Progress Tracking**: Real-time processing updates

---

## 📈 Research Applications

### Feature Engineering
```python
# Use extracted categories as ML features
primary_skills_features = primary_skills_df.groupby('record_id')['skill'].apply(list)
adjective_features = adjectives_df.groupby('record_id')['adjective'].apply(list)
```

### Linguistic Analysis
- **Skill Distribution**: Analyze technology trends
- **Language Patterns**: Study professional communication
- **Experience Indicators**: Extract seniority signals
- **Performance Descriptors**: Identify success metrics

### Academic Research
- **Corpus Analysis**: Large-scale linguistic study
- **Feature Comparison**: Compare category effectiveness
- **Model Input**: Rich features for ML training
- **Validation Studies**: Benchmark against manual annotation

---

## 🎯 Integration with ML Pipeline

### Feature Vector Creation
```python
# Create feature vectors from categories
def create_feature_vector(record_id):
    primary = primary_skills_df[primary_skills_df['record_id'] == record_id]['skill'].tolist()
    secondary = secondary_skills_df[secondary_skills_df['record_id'] == record_id]['skill'].tolist()
    adjectives = adjectives_df[adjectives_df['record_id'] == record_id]['adjective'].tolist()
    adverbs = adverbs_df[adverbs_df['record_id'] == record_id]['adverb'].tolist()
    
    return {
        'primary_skills': primary,
        'secondary_skills': secondary, 
        'adjectives': adjectives,
        'adverbs': adverbs
    }
```

### Model Training
- **Skill Similarity**: Compare primary/secondary skills
- **Experience Level**: Use adjectives for seniority detection
- **Performance Prediction**: Leverage adverbs for quality assessment
- **Comprehensive Matching**: Combine all four categories

---

## 🔮 Advanced Features

### Custom Skill Dictionaries
```python
# Add domain-specific skills
CUSTOM_PRIMARY_SKILLS = {
    'blockchain': ['solidity', 'ethereum', 'bitcoin', 'smart contracts'],
    'quantum_computing': ['qiskit', 'quantum algorithms', 'quantum gates']
}
```

### Contextual Analysis
```python
# Analyze skill co-occurrence
skill_pairs = primary_skills_df.groupby('record_id')['skill'].apply(
    lambda x: list(combinations(x, 2))
)
```

### Temporal Analysis
```python
# Track skill evolution over time
skill_trends = primary_skills_df.groupby(['extracted_date', 'skill']).size()
```

---

## 🚨 Troubleshooting

### Common Issues
1. **NLTK Data Missing**: Run `nltk.download('all')` 
2. **Memory Issues**: Process data in smaller chunks
3. **Encoding Problems**: Ensure UTF-8 encoding
4. **Empty Results**: Check input data quality

### Performance Tips
- **Parallel Processing**: Use multiprocessing for large datasets
- **Caching**: Cache POS tagging results
- **Filtering**: Pre-filter irrelevant text
- **Optimization**: Use compiled regex patterns

---

**🎯 This four-categories extraction provides the linguistic foundation for advanced job-resume matching research, following proven academic methodologies!**
