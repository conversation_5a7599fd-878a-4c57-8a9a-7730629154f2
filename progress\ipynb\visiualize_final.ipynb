{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 📊 Job-Resume Matching Data Visualization\n", "## <PERSON><PERSON> tích và trực quan hóa dữ liệu từ file CSV\n", "\n", "**Dataset**: `jd_cr_similarity.csv`  \n", "**<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON> tích dữ liệu matching giữa Job Descriptions và Candidate Resumes"]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure display\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.width', None)\n", "\n", "print(\"📚 Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "load_data", "metadata": {}, "outputs": [], "source": ["# Load data\n", "print(\"🔄 Loading data...\")\n", "df = pd.read_csv('../csv/jd_cr_similarity.csv')\n", "\n", "print(f\"✅ Data loaded successfully!\")\n", "print(f\"📊 Dataset shape: {df.shape}\")\n", "print(f\"📋 Columns: {list(df.columns)}\")\n", "\n", "# Display first few rows\n", "print(\"\\n🔍 First 5 rows:\")\n", "display(df.head())\n", "\n", "# Display basic info\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"DATASET OVERVIEW\")\n", "print(\"=\"*60)\n", "df.info()"]}, {"cell_type": "code", "execution_count": null, "id": "data_summary", "metadata": {}, "outputs": [], "source": ["# Data summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"DATA SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "print(f\"📈 Total records: {len(df):,}\")\n", "print(f\"🏢 Unique Job IDs: {df['jd_id'].nunique():,}\")\n", "print(f\"👥 Unique CR Categories: {df['cr_category'].nunique():,}\")\n", "print(f\"🎯 Suitability levels: {df['suitability'].nunique()}\")\n", "\n", "print(\"\\n📊 Suitability Distribution:\")\n", "suitability_counts = df['suitability'].value_counts()\n", "for suit, count in suitability_counts.items():\n", "    pct = count / len(df) * 100\n", "    print(f\"  {suit}: {count:,} ({pct:.1f}%)\")\n", "\n", "print(\"\\n📋 CR Categories (Top 15):\")\n", "cr_counts = df['cr_category'].value_counts()\n", "for i, (cat, count) in enumerate(cr_counts.head(15).items(), 1):\n", "    print(f\"  {i:2d}. {cat}: {count:,}\")"]}, {"cell_type": "code", "execution_count": null, "id": "stats_summary", "metadata": {}, "outputs": [], "source": ["# Statistical summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"SIMILARITY SCORES STATISTICS\")\n", "print(\"=\"*60)\n", "\n", "similarity_cols = ['primary_skills_sim', 'secondary_skills_sim', 'adjectives_sim', 'total_similarity']\n", "stats_df = df[similarity_cols].describe()\n", "print(stats_df.round(4))\n", "\n", "# Check for missing values\n", "print(\"\\n🔍 Missing Values:\")\n", "missing = df.isnull().sum()\n", "if missing.sum() == 0:\n", "    print(\"  ✅ No missing values found!\")\n", "else:\n", "    for col, count in missing[missing > 0].items():\n", "        print(f\"  {col}: {count}\")"]}, {"cell_type": "markdown", "id": "viz_title", "metadata": {}, "source": ["## 📊 Data Visualizations"]}, {"cell_type": "code", "execution_count": null, "id": "suitability_distribution", "metadata": {}, "outputs": [], "source": ["# 1. Suitability Distribution\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# Pie chart\n", "suitability_counts = df['suitability'].value_counts()\n", "colors = ['#ff9999', '#66b3ff', '#99ff99']\n", "axes[0].pie(suitability_counts.values, labels=suitability_counts.index, autopct='%1.1f%%', \n", "           colors=colors, startangle=90)\n", "axes[0].set_title('📊 Suitability Distribution', fontsize=14, fontweight='bold')\n", "\n", "# Bar chart\n", "bars = axes[1].bar(suitability_counts.index, suitability_counts.values, color=colors)\n", "axes[1].set_title('📈 Suitability Counts', fontsize=14, fontweight='bold')\n", "axes[1].set_ylabel('Count')\n", "axes[1].tick_params(axis='x', rotation=45)\n", "\n", "# Add value labels on bars\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    axes[1].text(bar.get_x() + bar.get_width()/2., height + 1000,\n", "                f'{int(height):,}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n📊 Suitability Statistics:\")\n", "for suit, count in suitability_counts.items():\n", "    pct = count / len(df) * 100\n", "    print(f\"  {suit}: {count:,} records ({pct:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "id": "cr_categories_viz", "metadata": {}, "outputs": [], "source": ["# 2. CR Categories Distribution\n", "plt.figure(figsize=(16, 10))\n", "\n", "# Top 20 categories\n", "top_categories = df['cr_category'].value_counts().head(20)\n", "\n", "# Create horizontal bar chart\n", "bars = plt.barh(range(len(top_categories)), top_categories.values, \n", "                color=plt.cm.Set3(np.linspace(0, 1, len(top_categories))))\n", "\n", "plt.yticks(range(len(top_categories)), top_categories.index)\n", "plt.xlabel('Number of Records')\n", "plt.title('👥 Top 20 CR Categories Distribution', fontsize=16, fontweight='bold', pad=20)\n", "plt.gca().invert_yaxis()\n", "\n", "# Add value labels\n", "for i, (bar, value) in enumerate(zip(bars, top_categories.values)):\n", "    plt.text(value + 500, i, f'{value:,}', va='center', ha='left')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\n👥 Total CR Categories: {df['cr_category'].nunique()}\")\n", "print(f\"📊 Showing top 20 out of {df['cr_category'].nunique()} categories\")"]}, {"cell_type": "code", "execution_count": null, "id": "similarity_distributions", "metadata": {}, "outputs": [], "source": ["# 3. Similarity Scores Distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('📈 Similarity Scores Distribution', fontsize=16, fontweight='bold')\n", "\n", "similarity_cols = ['primary_skills_sim', 'secondary_skills_sim', 'adjectives_sim', 'total_similarity']\n", "titles = ['🎯 Primary Skills Similarity', '🔧 Secondary Skills Similarity', \n", "          '📝 Adjectives Similarity', '📊 Total Similarity']\n", "\n", "for i, (col, title) in enumerate(zip(similarity_cols, titles)):\n", "    row, col_idx = i // 2, i % 2\n", "    \n", "    # Histogram\n", "    axes[row, col_idx].hist(df[col], bins=50, alpha=0.7, color=plt.cm.Set2(i))\n", "    axes[row, col_idx].set_title(title)\n", "    axes[row, col_idx].set_xlabel('Similarity Score')\n", "    axes[row, col_idx].set_ylabel('Frequency')\n", "    \n", "    # Add statistics text\n", "    mean_val = df[col].mean()\n", "    median_val = df[col].median()\n", "    axes[row, col_idx].axvline(mean_val, color='red', linestyle='--', alpha=0.8, label=f'Mean: {mean_val:.3f}')\n", "    axes[row, col_idx].axvline(median_val, color='blue', linestyle='--', alpha=0.8, label=f'Median: {median_val:.3f}')\n", "    axes[row, col_idx].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "correlation_heatmap", "metadata": {}, "outputs": [], "source": ["# 4. Correlation Heatmap\n", "plt.figure(figsize=(10, 8))\n", "\n", "# Select numeric columns for correlation\n", "numeric_cols = ['primary_skills_sim', 'secondary_skills_sim', 'adjectives_sim', \n", "                'adj_weight', 'total_similarity']\n", "corr_matrix = df[numeric_cols].corr()\n", "\n", "# Create heatmap\n", "mask = np.triu(np.ones_like(corr_matrix, dtype=bool))\n", "sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,\n", "            square=True, linewidths=0.5, cbar_kws={\"shrink\": .8})\n", "\n", "plt.title('🔥 Correlation Heatmap - Similarity Features', fontsize=14, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n🔍 Key Correlations:\")\n", "# Find high correlations (excluding diagonal)\n", "corr_pairs = []\n", "for i in range(len(corr_matrix.columns)):\n", "    for j in range(i+1, len(corr_matrix.columns)):\n", "        corr_val = corr_matrix.iloc[i, j]\n", "        if abs(corr_val) > 0.3:  # Show correlations > 0.3\n", "            corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_val))\n", "\n", "corr_pairs.sort(key=lambda x: abs(x[2]), reverse=True)\n", "for col1, col2, corr_val in corr_pairs[:5]:\n", "    print(f\"  {col1} ↔ {col2}: {corr_val:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "suitability_by_category", "metadata": {}, "outputs": [], "source": ["# 5. Suitability by CR Category (Top 15 categories)\n", "plt.figure(figsize=(16, 10))\n", "\n", "# Get top 15 categories by count\n", "top_15_categories = df['cr_category'].value_counts().head(15).index\n", "df_top15 = df[df['cr_category'].isin(top_15_categories)]\n", "\n", "# Create crosstab\n", "crosstab = pd.crosstab(df_top15['cr_category'], df_top15['suitability'], normalize='index') * 100\n", "\n", "# Create stacked bar chart\n", "ax = crosstab.plot(kind='bar', stacked=True, figsize=(16, 8), \n", "                   color=['#ff9999', '#66b3ff', '#99ff99'])\n", "\n", "plt.title('🎯 Suitability Distribution by CR Category (Top 15)', fontsize=16, fontweight='bold')\n", "plt.xlabel('CR Category')\n", "plt.ylabel('Percentage (%)')\n", "plt.legend(title='Suitability', bbox_to_anchor=(1.05, 1), loc='upper left')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n📊 Categories with highest 'Most Suitable' percentage:\")\n", "most_suitable_pct = crosstab['Most Suitable'].sort_values(ascending=False)\n", "for i, (cat, pct) in enumerate(most_suitable_pct.head(5).items(), 1):\n", "    print(f\"  {i}. {cat}: {pct:.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "id": "similarity_boxplots", "metadata": {}, "outputs": [], "source": ["# 6. Similarity Scores by Suitability (Box plots)\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "fig.suptitle('📦 Similarity Scores by Suitability Level', fontsize=16, fontweight='bold')\n", "\n", "similarity_cols = ['primary_skills_sim', 'secondary_skills_sim', 'adjectives_sim', 'total_similarity']\n", "titles = ['🎯 Primary Skills', '🔧 Secondary Skills', '📝 Adjectives', '📊 Total Similarity']\n", "\n", "for i, (col, title) in enumerate(zip(similarity_cols, titles)):\n", "    row, col_idx = i // 2, i % 2\n", "    \n", "    # Box plot\n", "    df.boxplot(column=col, by='suitability', ax=axes[row, col_idx])\n", "    axes[row, col_idx].set_title(title)\n", "    axes[row, col_idx].set_xlabel('Suitability')\n", "    axes[row, col_idx].set_ylabel('Similarity Score')\n", "    \n", "    # Remove the automatic title\n", "    axes[row, col_idx].set_title(title)\n", "\n", "plt.suptitle('📦 Similarity Scores by Suitability Level', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "job_analysis", "metadata": {}, "outputs": [], "source": ["# 7. Job Analysis\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"🏢 JOB ANALYSIS\")\n", "print(\"=\"*60)\n", "\n", "# Jobs with most records\n", "job_counts = df['jd_id'].value_counts()\n", "print(f\"\\n📈 Jobs with most matching records (Top 10):\")\n", "for i, (job_id, count) in enumerate(job_counts.head(10).items(), 1):\n", "    print(f\"  {i:2d}. {job_id}: {count:,} records\")\n", "\n", "# Average similarity by job\n", "job_avg_similarity = df.groupby('jd_id')['total_similarity'].mean().sort_values(ascending=False)\n", "print(f\"\\n🎯 Jobs with highest average similarity (Top 10):\")\n", "for i, (job_id, avg_sim) in enumerate(job_avg_similarity.head(10).items(), 1):\n", "    print(f\"  {i:2d}. {job_id}: {avg_sim:.3f}\")\n", "\n", "# Visualization: Job distribution\n", "plt.figure(figsize=(14, 8))\n", "\n", "# Top 20 jobs by record count\n", "top_jobs = job_counts.head(20)\n", "bars = plt.bar(range(len(top_jobs)), top_jobs.values, \n", "               color=plt.cm.viridis(np.linspace(0, 1, len(top_jobs))))\n", "\n", "plt.xticks(range(len(top_jobs)), top_jobs.index, rotation=45, ha='right')\n", "plt.xlabel('Job ID')\n", "plt.ylabel('Number of Records')\n", "plt.title('🏢 Top 20 Jobs by Number of Matching Records', fontsize=14, fontweight='bold')\n", "\n", "# Add value labels\n", "for bar, value in zip(bars, top_jobs.values):\n", "    plt.text(bar.get_x() + bar.get_width()/2., value + 50,\n", "             f'{value:,}', ha='center', va='bottom', fontsize=8)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "advanced_analysis", "metadata": {}, "outputs": [], "source": ["# 8. Advanced Analysis\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"🔬 ADVANCED ANALYSIS\")\n", "print(\"=\"*60)\n", "\n", "# Skills analysis\n", "print(\"\\n🎯 Skills Similarity Analysis:\")\n", "skills_stats = df[['primary_skills_sim', 'secondary_skills_sim']].describe()\n", "print(skills_stats.round(4))\n", "\n", "# Categories with zero secondary skills\n", "zero_secondary = df[df['secondary_skills_sim'] == 0]['cr_category'].value_counts()\n", "print(f\"\\n🔧 Categories with most zero secondary skills (Top 10):\")\n", "for i, (cat, count) in enumerate(zero_secondary.head(10).items(), 1):\n", "    total_cat = df[df['cr_category'] == cat].shape[0]\n", "    pct = count / total_cat * 100\n", "    print(f\"  {i:2d}. {cat}: {count:,}/{total_cat:,} ({pct:.1f}%)\")\n", "\n", "# High similarity analysis\n", "high_similarity = df[df['total_similarity'] > 1.0]\n", "print(f\"\\n📊 Records with total_similarity > 1.0: {len(high_similarity):,} ({len(high_similarity)/len(df)*100:.1f}%)\")\n", "\n", "if len(high_similarity) > 0:\n", "    print(\"\\nTop categories with high similarity:\")\n", "    high_sim_cats = high_similarity['cr_category'].value_counts().head(10)\n", "    for i, (cat, count) in enumerate(high_sim_cats.items(), 1):\n", "        print(f\"  {i:2d}. {cat}: {count:,}\")"]}, {"cell_type": "code", "execution_count": null, "id": "summary_insights", "metadata": {}, "outputs": [], "source": ["# 9. Su<PERSON><PERSON> and Insights\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"💡 KEY INSIGHTS & SUMMARY\")\n", "print(\"=\"*60)\n", "\n", "total_records = len(df)\n", "unique_jobs = df['jd_id'].nunique()\n", "unique_categories = df['cr_category'].nunique()\n", "\n", "print(f\"\\n📊 Dataset Overview:\")\n", "print(f\"  • Total matching records: {total_records:,}\")\n", "print(f\"  • Unique job positions: {unique_jobs:,}\")\n", "print(f\"  • Unique CV categories: {unique_categories:,}\")\n", "print(f\"  • Average records per job: {total_records/unique_jobs:.1f}\")\n", "\n", "# Suitability insights\n", "suitability_dist = df['suitability'].value_counts(normalize=True) * 100\n", "print(f\"\\n🎯 Suitability Distribution:\")\n", "for suit, pct in suitability_dist.items():\n", "    print(f\"  • {suit}: {pct:.1f}%\")\n", "\n", "# Similarity insights\n", "print(f\"\\n📈 Similarity Score Insights:\")\n", "print(f\"  • Average primary skills similarity: {df['primary_skills_sim'].mean():.3f}\")\n", "print(f\"  • Average secondary skills similarity: {df['secondary_skills_sim'].mean():.3f}\")\n", "print(f\"  • Average adjectives similarity: {df['adjectives_sim'].mean():.3f}\")\n", "print(f\"  • Average total similarity: {df['total_similarity'].mean():.3f}\")\n", "\n", "# Data quality insights\n", "zero_primary = (df['primary_skills_sim'] == 0).sum()\n", "zero_secondary = (df['secondary_skills_sim'] == 0).sum()\n", "zero_adjectives = (df['adjectives_sim'] == 0).sum()\n", "\n", "print(f\"\\n🔍 Data Quality:\")\n", "print(f\"  • Records with zero primary skills: {zero_primary:,} ({zero_primary/total_records*100:.1f}%)\")\n", "print(f\"  • Records with zero secondary skills: {zero_secondary:,} ({zero_secondary/total_records*100:.1f}%)\")\n", "print(f\"  • Records with zero adjectives: {zero_adjectives:,} ({zero_adjectives/total_records*100:.1f}%)\")\n", "\n", "print(f\"\\n✅ Analysis completed successfully!\")\n", "print(f\"📊 Total visualizations created: 8\")\n", "print(f\"🎯 Ready for machine learning model training!\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}