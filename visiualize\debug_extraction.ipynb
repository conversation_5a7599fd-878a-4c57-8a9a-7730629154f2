{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 🔍 Debug Four Categories Extraction\n", "## Quick Fix for Adjectives/Adverbs Extraction Issues"]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import nltk\n", "from nltk.tokenize import word_tokenize\n", "from nltk.tag import pos_tag\n", "from nltk.corpus import stopwords\n", "\n", "# Download NLTK data if needed\n", "try:\n", "    nltk.data.find('tokenizers/punkt')\n", "    nltk.data.find('taggers/averaged_perceptron_tagger')\n", "    nltk.data.find('corpora/stopwords')\n", "except LookupError:\n", "    nltk.download('punkt')\n", "    nltk.download('averaged_perceptron_tagger')\n", "    nltk.download('stopwords')\n", "\n", "print(\"✅ Libraries loaded\")"]}, {"cell_type": "code", "execution_count": null, "id": "load-data", "metadata": {}, "outputs": [], "source": ["# Load clean data\n", "try:\n", "    clean_resumes = pd.read_csv('../data/clean_resumes.csv')\n", "    clean_jobs = pd.read_csv('../data/clean_jobs.csv')\n", "    print(f\"✅ Loaded {len(clean_resumes):,} resumes and {len(clean_jobs):,} jobs\")\n", "except FileNotFoundError as e:\n", "    print(f\"❌ Error: {e}\")\n", "    print(\"Please run data_preprocessing.ipynb first\")"]}, {"cell_type": "code", "execution_count": null, "id": "test-extraction", "metadata": {}, "outputs": [], "source": ["# Test extraction on a small sample\n", "sample_text = \"I am an experienced Python developer with excellent problem-solving skills. I work efficiently on challenging projects and consistently deliver high-quality results.\"\n", "\n", "print(\"🔍 Testing extraction on sample text:\")\n", "print(f\"Text: {sample_text}\")\n", "print()\n", "\n", "# Test POS tagging\n", "tokens = word_tokenize(sample_text.lower())\n", "pos_tags = pos_tag(tokens)\n", "\n", "print(\"📝 POS Tags:\")\n", "for word, pos in pos_tags:\n", "    print(f\"  {word} -> {pos}\")\n", "\n", "print()\n", "\n", "# Extract adjectives and adverbs\n", "adjectives = []\n", "adverbs = []\n", "stop_words = set(stopwords.words('english'))\n", "\n", "for word, pos in pos_tags:\n", "    if word in stop_words or len(word) < 3:\n", "        continue\n", "    \n", "    if pos.startswith('JJ'):  # Adjectives\n", "        adjectives.append((word, pos))\n", "    elif pos.startswith('RB'):  # Adverbs\n", "        adverbs.append((word, pos))\n", "\n", "print(f\"🎯 Found adjectives: {adjectives}\")\n", "print(f\"🎯 Found adverbs: {adverbs}\")"]}, {"cell_type": "code", "execution_count": null, "id": "simple-extraction", "metadata": {}, "outputs": [], "source": ["# Simple extraction function\n", "def simple_extract_adj_adv(text, max_records=5):\n", "    \"\"\"Simple extraction for testing\"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return {'adjectives': [], 'adverbs': []}\n", "    \n", "    try:\n", "        tokens = word_tokenize(str(text).lower())\n", "        pos_tags = pos_tag(tokens)\n", "        \n", "        adjectives = []\n", "        adverbs = []\n", "        stop_words = set(stopwords.words('english'))\n", "        \n", "        for word, pos in pos_tags:\n", "            if word in stop_words or len(word) < 3:\n", "                continue\n", "            \n", "            if pos.startswith('JJ') and len(adjectives) < max_records:\n", "                adjectives.append({\n", "                    'word': word,\n", "                    'pos': pos,\n", "                    'type': 'general'\n", "                })\n", "            elif pos.startswith('RB') and len(adverbs) < max_records:\n", "                adverbs.append({\n", "                    'word': word,\n", "                    'pos': pos,\n", "                    'type': 'general'\n", "                })\n", "        \n", "        return {'adjectives': adjectives, 'adverbs': adverbs}\n", "    except Exception as e:\n", "        print(f\"Error processing text: {e}\")\n", "        return {'adjectives': [], 'adverbs': []}\n", "\n", "# Test on sample\n", "result = simple_extract_adj_adv(sample_text)\n", "print(f\"✅ Extraction result: {result}\")"]}, {"cell_type": "code", "execution_count": null, "id": "process-sample", "metadata": {}, "outputs": [], "source": ["# Process a small sample of data\n", "print(\"🔄 Processing sample data...\")\n", "\n", "# Take first 10 resumes and 10 jobs for testing\n", "sample_resumes = clean_resumes.head(10)\n", "sample_jobs = clean_jobs.head(10)\n", "\n", "all_adjectives = []\n", "all_adverbs = []\n", "\n", "# Process sample resumes\n", "for idx, row in sample_resumes.iterrows():\n", "    result = simple_extract_adj_adv(row['clean_text'])\n", "    \n", "    for adj in result['adjectives']:\n", "        all_adjectives.append({\n", "            'record_id': f'resume_{idx}',\n", "            'source_type': 'resume',\n", "            'adjective': adj['word'],\n", "            'pos_tag': adj['pos'],\n", "            'adj_type': adj['type'],\n", "            'category': row['category_clean']\n", "        })\n", "    \n", "    for adv in result['adverbs']:\n", "        all_adverbs.append({\n", "            'record_id': f'resume_{idx}',\n", "            'source_type': 'resume',\n", "            'adverb': adv['word'],\n", "            'pos_tag': adv['pos'],\n", "            'adv_type': adv['type'],\n", "            'category': row['category_clean']\n", "        })\n", "\n", "# Process sample jobs\n", "for idx, row in sample_jobs.iterrows():\n", "    result = simple_extract_adj_adv(row['clean_text'])\n", "    \n", "    for adj in result['adjectives']:\n", "        all_adjectives.append({\n", "            'record_id': f'job_{idx}',\n", "            'source_type': 'job',\n", "            'adjective': adj['word'],\n", "            'pos_tag': adj['pos'],\n", "            'adj_type': adj['type'],\n", "            'job_title': row['title_clean']\n", "        })\n", "    \n", "    for adv in result['adverbs']:\n", "        all_adverbs.append({\n", "            'record_id': f'job_{idx}',\n", "            'source_type': 'job',\n", "            'adverb': adv['word'],\n", "            'pos_tag': adv['pos'],\n", "            'adv_type': adv['type'],\n", "            'job_title': row['title_clean']\n", "        })\n", "\n", "# Create DataFrames\n", "adjectives_df = pd.DataFrame(all_adjectives)\n", "adverbs_df = pd.DataFrame(all_adverbs)\n", "\n", "print(f\"✅ Created DataFrames:\")\n", "print(f\"   Adjectives: {len(adjectives_df)} records\")\n", "print(f\"   Adverbs: {len(adverbs_df)} records\")\n", "\n", "if len(adjectives_df) > 0:\n", "    print(f\"\\n📊 Adjectives columns: {list(adjectives_df.columns)}\")\n", "    print(f\"📊 Sample adjectives:\")\n", "    print(adjectives_df.head())\n", "\n", "if len(adverbs_df) > 0:\n", "    print(f\"\\n📊 Adverbs columns: {list(adverbs_df.columns)}\")\n", "    print(f\"📊 Sample adverbs:\")\n", "    print(adverbs_df.head())"]}, {"cell_type": "code", "execution_count": null, "id": "test-analysis", "metadata": {}, "outputs": [], "source": ["# Test analysis with error handling\n", "print(\"📈 Testing analysis with error handling...\")\n", "\n", "# Adjectives Analysis\n", "print(\"\\n🎯 ADJECTIVES ANALYSIS:\")\n", "print(f\"Total adjectives extracted: {len(adjectives_df):,}\")\n", "\n", "if len(adjectives_df) > 0 and 'adjective' in adjectives_df.columns:\n", "    print(f\"Unique adjectives: {adjectives_df['adjective'].nunique()}\")\n", "    \n", "    print(\"\\n🔥 Top adjectives:\")\n", "    top_adj = adjectives_df['adjective'].value_counts().head(5)\n", "    for adj, count in top_adj.items():\n", "        print(f\"  • {adj}: {count}\")\n", "else:\n", "    print(\"  ⚠️ No adjectives found or missing columns\")\n", "\n", "# Adverbs Analysis\n", "print(\"\\n🎯 ADVERBS ANALYSIS:\")\n", "print(f\"Total adverbs extracted: {len(adverbs_df):,}\")\n", "\n", "if len(adverbs_df) > 0 and 'adverb' in adverbs_df.columns:\n", "    print(f\"Unique adverbs: {adverbs_df['adverb'].nunique()}\")\n", "    \n", "    print(\"\\n🔥 Top adverbs:\")\n", "    top_adv = adverbs_df['adverb'].value_counts().head(5)\n", "    for adv, count in top_adv.items():\n", "        print(f\"  • {adv}: {count}\")\n", "else:\n", "    print(\"  ⚠️ No adverbs found or missing columns\")\n", "\n", "print(\"\\n✅ Debug analysis completed\")"]}, {"cell_type": "code", "execution_count": null, "id": "export-sample", "metadata": {}, "outputs": [], "source": ["# Export sample results\n", "if len(adjectives_df) > 0:\n", "    adjectives_df.to_csv('../data/sample_adjectives.csv', index=False)\n", "    print(f\"✅ Exported sample adjectives to: data/sample_adjectives.csv\")\n", "\n", "if len(adverbs_df) > 0:\n", "    adverbs_df.to_csv('../data/sample_adverbs.csv', index=False)\n", "    print(f\"✅ Exported sample adverbs to: data/sample_adverbs.csv\")\n", "\n", "print(\"\\n🎉 Debug extraction completed!\")\n", "print(\"\\n💡 If this works, the issue is likely with processing large datasets.\")\n", "print(\"   Consider processing data in smaller batches or optimizing the extraction function.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}