{"cells": [{"cell_type": "markdown", "id": "e953e889", "metadata": {}, "source": ["## Python version 11."]}, {"cell_type": "code", "execution_count": 1, "id": "943b40e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pandas in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (2.3.0)\n", "Requirement already satisfied: langdetect in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (1.0.9)\n", "Requirement already satisfied: deep_translator in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (1.11.4)\n", "Requirement already satisfied: spacy in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (3.8.7)\n", "Requirement already satisfied: underthesea in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (6.8.4)\n", "Requirement already satisfied: nltk in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (3.9.1)\n", "Requirement already satisfied: numpy>=1.23.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2.3.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: six in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from langdetect) (1.17.0)\n", "Requirement already satisfied: beautifulsoup4<5.0.0,>=4.9.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from deep_translator) (4.13.4)\n", "Requirement already satisfied: requests<3.0.0,>=2.23.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from deep_translator) (2.32.4)\n", "Requirement already satisfied: soupsieve>1.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from beautifulsoup4<5.0.0,>=4.9.1->deep_translator) (2.7)\n", "Requirement already satisfied: typing-extensions>=4.0.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from beautifulsoup4<5.0.0,>=4.9.1->deep_translator) (4.14.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (2025.6.15)\n", "Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.11 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.0.12)\n", "Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (1.0.5)\n", "Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (1.0.13)\n", "Requirement already satisfied: cymem<2.1.0,>=2.0.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.0.11)\n", "Requirement already satisfied: preshed<3.1.0,>=3.0.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.0.10)\n", "Requirement already satisfied: thinc<8.4.0,>=8.3.4 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (8.3.6)\n", "Requirement already satisfied: wasabi<1.2.0,>=0.9.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (1.1.3)\n", "Requirement already satisfied: srsly<3.0.0,>=2.4.3 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.5.1)\n", "Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.0.10)\n", "Requirement already satisfied: weasel<0.5.0,>=0.1.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (0.4.1)\n", "Requirement already satisfied: typer<1.0.0,>=0.3.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (0.16.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.38.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (4.67.1)\n", "Requirement already satisfied: pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.11.7)\n", "Requirement already satisfied: jinja2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.1.6)\n", "Requirement already satisfied: setuptools in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (65.5.0)\n", "Requirement already satisfied: packaging>=20.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (25.0)\n", "Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.5.0)\n", "Requirement already satisfied: language-data>=1.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from langcodes<4.0.0,>=3.2.0->spacy) (1.3.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (0.4.1)\n", "Requirement already satisfied: blis<1.4.0,>=1.3.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from thinc<8.4.0,>=8.3.4->spacy) (1.3.0)\n", "Requirement already satisfied: confection<1.0.0,>=0.0.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from thinc<8.4.0,>=8.3.4->spacy) (0.1.5)\n", "Requirement already satisfied: colorama in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from tqdm<5.0.0,>=4.38.0->spacy) (0.4.6)\n", "Requirement already satisfied: click>=8.0.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from typer<1.0.0,>=0.3.0->spacy) (8.2.1)\n", "Requirement already satisfied: shellingham>=1.3.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from typer<1.0.0,>=0.3.0->spacy) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from typer<1.0.0,>=0.3.0->spacy) (14.0.0)\n", "Requirement already satisfied: cloudpathlib<1.0.0,>=0.7.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from weasel<0.5.0,>=0.1.0->spacy) (0.21.1)\n", "Requirement already satisfied: smart-open<8.0.0,>=5.2.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from weasel<0.5.0,>=0.1.0->spacy) (7.1.0)\n", "Requirement already satisfied: wrapt in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from smart-open<8.0.0,>=5.2.1->weasel<0.5.0,>=0.1.0->spacy) (1.17.2)\n", "Requirement already satisfied: python-crfsuite>=0.9.6 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (0.9.11)\n", "Requirement already satisfied: joblib in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (1.5.1)\n", "Requirement already satisfied: scikit-learn in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (1.7.0)\n", "Requirement already satisfied: PyYAML in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (6.0.2)\n", "Requirement already satisfied: underthesea-core==1.0.4 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (1.0.4)\n", "Requirement already satisfied: regex>=2021.8.3 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from nltk) (2024.11.6)\n", "Requirement already satisfied: marisa-trie>=1.1.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from language-data>=1.2->langcodes<4.0.0,>=3.2.0->spacy) (1.2.1)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy) (2.19.1)\n", "Requirement already satisfied: mdurl~=0.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy) (0.1.2)\n", "Requirement already satisfied: MarkupSafe>=2.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from jinja2->spacy) (3.0.2)\n", "Requirement already satisfied: scipy>=1.8.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from scikit-learn->underthesea) (1.15.3)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from scikit-learn->underthesea) (3.6.0)\n"]}], "source": ["!pip install pandas langdetect deep_translator spacy underthesea nltk"]}, {"cell_type": "code", "execution_count": 2, "id": "1c57442c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting en-core-web-md==3.8.0\n", "  Downloading https://github.com/explosion/spacy-models/releases/download/en_core_web_md-3.8.0/en_core_web_md-3.8.0-py3-none-any.whl (33.5 MB)\n", "     ---------------------------------------- 0.0/33.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 3.7/33.5 MB 27.3 MB/s eta 0:00:02\n", "     ---------- ----------------------------- 8.7/33.5 MB 26.8 MB/s eta 0:00:01\n", "     ----------------- --------------------- 14.7/33.5 MB 27.9 MB/s eta 0:00:01\n", "     ------------------------ -------------- 21.0/33.5 MB 34.0 MB/s eta 0:00:01\n", "     ------------------------------- ------- 27.0/33.5 MB 29.5 MB/s eta 0:00:01\n", "     --------------------------------------  33.3/33.5 MB 29.4 MB/s eta 0:00:01\n", "     --------------------------------------- 33.5/33.5 MB 27.3 MB/s eta 0:00:00\n", "\u001b[38;5;2m✔ Download and installation successful\u001b[0m\n", "You can now load the package via spacy.load('en_core_web_md')\n"]}], "source": ["!python -m spacy download en_core_web_md"]}, {"cell_type": "code", "execution_count": 3, "id": "f912b9c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: matplotlib in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (3.10.3)\n", "Requirement already satisfied: seaborn in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (0.13.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: numpy>=1.23 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (2.3.0)\n", "Requirement already satisfied: packaging>=20.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (25.0)\n", "Requirement already satisfied: pillow>=8 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: pandas>=1.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from seaborn) (2.3.0)\n", "Requirement already satisfied: pytz>=2020.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: six>=1.5 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n"]}], "source": ["!pip install matp<PERSON><PERSON>b seaborn"]}, {"cell_type": "code", "execution_count": 4, "id": "d81e86d7", "metadata": {}, "outputs": [], "source": ["# Import c<PERSON><PERSON> thư viện c<PERSON>n thiết\n", "import pandas as pd\n", "from langdetect import detect\n", "from deep_translator import GoogleTranslator\n", "import re\n", "import spacy\n", "from underthesea import word_tokenize, pos_tag\n", "from nltk.corpus import stopwords\n", "import numpy as np\n", "from collections import defaultdict\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from collections import Counter\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 5, "id": "23a0e1ff", "metadata": {}, "outputs": [], "source": ["nlp_en = spacy.load('en_core_web_md')\n", "stop_words_en = None\n", "stop_words_vi = None"]}, {"cell_type": "code", "execution_count": 10, "id": "04cdb790", "metadata": {}, "outputs": [], "source": ["# Đường dẫn đến file csv chứa các job description\n", "csv_jd = \"../../data/raw/itviec_jobs_undetected.csv\"\n", "stop_words_vn_txt = \"../../docs/vietnamese-stopwords.txt\""]}, {"cell_type": "code", "execution_count": 11, "id": "2c5413fc", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(csv_jd)"]}, {"cell_type": "code", "execution_count": 13, "id": "adf045bf", "metadata": {}, "outputs": [], "source": ["def detect_language(text):\n", "    try:\n", "        return detect(text)\n", "    except:\n", "        return 'unknown'\n", "def translate_to_english(text, lang):\n", "    try:\n", "        if lang == 'en' or pd.isna(text) or text.strip() == \"\":\n", "            return text\n", "        return GoogleTranslator(source=lang, target='en').translate(text)\n", "    except Exception as e:\n", "        print(f\"Error translating: {e} | text: {text}\")\n", "        return text\n", "def clean_text(text, lang='en'):\n", "    if not isinstance(text, str) or not text.strip():\n", "        return ''\n", "    text = text.lower()\n", "    text = re.sub(r'http\\S+|#\\S+|@\\S+|[^\\w\\s]|\\n|\\r|\\t|\\*|\\•', ' ', text)\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    if lang == 'en':\n", "        doc = nlp_en(text)\n", "        tokens = [token.lemma_ for token in doc if token.text not in stop_words_en]\n", "        return ' '.join(tokens)\n", "    else:\n", "        tokens = word_tokenize(text)\n", "        tokens = [t for t in tokens if t not in stop_words_vi]\n", "        return ' '.join(tokens)\n", "def clean_skills(text):\n", "    text.lower()\n", "    if pd.isna(text) or text.strip() == \"\":\n", "        return []\n", "    return [t.strip() for t in text.split(',') if t.strip()]"]}, {"cell_type": "code", "execution_count": 14, "id": "2f57f34e", "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> dụng cho cột 'description'\n", "df['language'] = df['description'].apply(detect_language)\n", "df['skills_en'] = df['skills']"]}, {"cell_type": "code", "execution_count": 15, "id": "c5520aac", "metadata": {}, "outputs": [], "source": ["df['description_en'] = df.apply(lambda x: translate_to_english(x['description'], x['language']), axis=1)\n", "df['requirements_en'] = df.apply(lambda x: translate_to_english(x['requirements'], x['language']), axis=1)"]}, {"cell_type": "code", "execution_count": 16, "id": "d832b02d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'khi', 'quan trọng', 'từ điều', 'ngộ nhỡ', 'nhất quyết', 'đâu đây', 'phót', 'cùng với', 'biết bao nhiêu', 'nghĩ đến', 'thúng thắng', 'bởi vì', 'xuất hiện', 'cao xa', 'ít hơn', 'bỏ việc', 'ý da', 'mà vẫn', 'ngôi', 'gi<PERSON>m thấp', 'dù gì', 'kể tới', 'như sau', 'vẫn', 'lấy giống', 'phải khi', 'dù rằng', 'cực lực', 'lư<PERSON><PERSON> số', 'do vì', 'vừa lúc', 'một lúc', 'nói ra', 'cứ như', 'làm dần dần', 'cho biết', 'à', 'tốt mối', 'mợ', 'lúc đi', 'sao bản', 'thả<PERSON> hèn', 'ba họ', 'duy', 'thốt thôi', 'nhà khó', 'cấp số', 'từ', 'bản riêng', 'lời', 'chúng ông', 'khoảng không', 'khác xa', 'xệp', 'dùng làm', 'chịu', 'nhận nhau', 'vung tán tàn', 'từng thời gian', 'xăm xăm', 'có tháng', 'tiếp tục', 'nóc', 'tăng thêm', 'về sau', 'giữa lúc', 'phăn phắt', 'bất ngờ', 'bỏ mẹ', 'mọi giờ', 'con dạ', 'quan tâm', 'trước', 'tìm', 'tuổi', 'ấy', 'đáng kể', 'những là', 'tắp tắp', 'nếu vậy', 'coi bộ', 'ai đó', 'cho ăn', 'nhiên hậu', 'nếu có', 'từ loại', 'lúc này', 'ngồi sau', 'đầu tiên', 'thế đó', 'chứ lại', 'số loại', 'họ gần', 'thời gian', 'chúng', 'mà không', 'họ xa', 'rõ thật', 'tức thì', 'của ngọt', 'nghe lại', 'này nọ', 'ơ kìa', 'lại làm', 'sao', 'ô kìa', 'nghe thấy', 'bởi vậy', 'chành chạnh', 'thứ đến', 'nào là', 'thích thuộc', 'chứ sao', 'cùng', 'để được', 'rứa', 'ạ', 'có phải', 'ngay tức thì', 'quá thì', 'nhớ', 'xa tanh', 'chọn bên', 'tênh', 'thích ý', 'chùn chùn', 'bài bác', 'hơn là', 'dù dì', 'khác nhau', 'lại cái', 'nghe hiểu', 'nhỏ', 'thậm chí', 'lúc khác', 'bỗng đâu', 'vô kể', 'xin gặp', 'đâu có', 'sau hết', 'vào khoảng', 'tốt ngày', 'nào đó', 'bỗng nhiên', 'phỉ phui', 'ăn làm', 'một cơn', 'ngồi không', 'ráo trọi', 'trước ngày', 'con tính', 'chúng mình', 'làm tin', 'riêng từng', 'dùng đến', 'vài', 'đầy năm', 'trong ngoài', 'tại vì', 'mà lại', 'cho tới khi', 'cho đến', 'đặt ra', 'hay biết', 'nếu', 'lời nói', 'dì', 'như', 'cao răng', 'hết rồi', 'việc', 'mà cả', 'ngồi trệt', 'thình lình', 'tha hồ', 'cũng thế', 'đặt mình', 'bộ điều', 'xoẳn', 'đầy', 'ờ ờ', 'việc gì', 'ngay khi', 'nhìn nhận', 'ít thôi', 'thấp xuống', 'để giống', 'lần theo', 'tin', 'đã đủ', 'theo', 'rồi xem', 'cùng ăn', 'bằng', 'tiếp theo', 'tại lòng', 'thật', 'tên họ', 'như thế', 'khi nào', 'lúc sáng', 'sao vậy', 'ít thấy', 'xử lý', 'pho', 'quá trình', 'dầu sao', 'mới hay', 'ráo', 'cũng như', 'chủn', 'chính điểm', 'thực hiện đúng', 'trước khi', 'chắc hẳn', 'bỏ lại', 'thi thoảng', 'làm cho', 'chị bộ', 'úi chà', 'rồi thì', 'con nhà', 'thế nào', 'ối giời ơi', 'dở chừng', 'cuộc', 'tránh tình trạng', 'cần số', 'nói qua', 'tại đâu', 'giống nhau', 'thốc', 'tự lượng', 'thuộc cách', 'đại loại', 'sáng rõ', 'vèo', 'bay biến', 'cật lực', 'quá giờ', 'ừ ừ', 'ái', 'riệt', 'đành đạch', 'hết ý', 'ầu ơ', 'thế thôi', 'số là', 'ông từ', 'tính cách', 'phải như', 'đưa tin', 'cao thế', 'lần lần', 'thường thôi', 'tay quay', 'ồ ồ', 'ra người', 'nghe nói', 'phỏng tính', 'nhiều', 'khó chơi', 'bấy lâu', 'dẫu rằng', 'nhà tôi', 'đánh đùng', 'chưa chắc', 'bỏ mất', 'vài điều', 'từng đơn vị', 'giữ ý', 'khoảng cách', 'bển', 'càng', 'nói', 'trả trước', 'thuộc từ', 'tin thêm', 'ba ba', 'không nhận', 'rút cục', 'thường hay', 'làm thế nào', 'tới mức', 'cách không', 'quả là', 'bỗng thấy', 'đâu', 'như là', 'ủa', 'ngày rày', 'hơn hết', 'rồi tay', 'bớ', 'tha hồ ăn', 'cả thể', 'sao cho', 'bằng nào', 'cũng được', 'cũng', 'còn nữa', 'tốc tả', 'bập bà bập bõm', 'tránh ra', 'gần', 'ắt phải', 'chẳng lẽ', 'biết đâu đấy', 'nhớ ra', 'ông nhỏ', 'nghe tin', 'bỏ', 'ba cùng', 'trực tiếp làm', 'khó làm', 'cả ăn', 'chắc người', 'tạo ra', 'nào đâu', 'và', 'thường', 'cơ', 'quan trọng vấn đề', 'biết đâu', 'nên chăng', 'rồi đây', 'để cho', 'lấy xuống', 'không những', 'á', 'không cần', 'các cậu', 'ngày qua', 'phía sau', 'bản bộ', 'làm như', 'ít lâu', 'có nhà', 'ngăn ngắt', 'phía bên', 'dữ cách', 'một', 'yêu cầu', 'đầy phè', 'nghe như', 'ôi chao', 'họ', 'thì ra', 'giảm', 'lâu ngày', 'trong', 'thà rằng', 'gây thêm', 'từ tính', 'đã', 'làm gì', 'làm được', 'áng', 'thực tế', 'nớ', 'ngay bây giờ', 'chớ gì', 'sau đây', 'oái', 'vị trí', 'bất chợt', 'thực ra', 'đủ điểm', 'chung chung', 'bất quá', 'người khác', 'làm tăng', 'chứ gì', 'ngồi', 'khác', 'thậm cấp', 'thửa', 'bằng như', 'chỉ tên', 'thỏm', 'phè phè', 'phía trong', 'sau này', 'cảm ơn', 'nhất luật', 'lấy thêm', 'đáng lẽ', 'đủ điều', 'cho', 'lòng', 'hết của', 'bắt đầu', 'bằng cứ', 'ối dào', 'cùng tuổi', 'đã hay', 'quay', 'bởi sao', 'vào đến', 'toà', 'vì thế', 'ngọn', 'lên số', 'ừ thì', 'để mà', 'nghĩ ra', 'ăn chịu', 'thốc tháo', 'không khỏi', 'người nhận', 'thanh ba', 'một số', 'biết chắc', 'trong số', 'bấy', 'hết', 'tuốt luốt', 'trệt', 'đặc biệt', 'bạn', 'ngày đến', 'dễ nghe', 'cái họ', 'điểm', 'ừ', 'bấy nhiêu', 'tanh tanh', 'hiểu', 'tất cả bao nhiêu', 'răng', 'khi trước', 'vài nơi', 'ơ', 'những như', 'lời chú', 'bỗng không', 'mọi việc', 'coi mòi', 'tất cả', 'nghe', 'ra ý', 'tự ăn', 'cật sức', 'sì', 'ren rén', 'trên dưới', 'rõ là', 'bao nhiêu', 'dùng cho', 'bất đồ', 'tuyệt nhiên', 'nước', 'tất tật', 'bỗng chốc', 'thích', 'rồi', 'nhận thấy', 'bán cấp', 'vùng', 'xin', 'chính giữa', 'không bán', 'vậy là', 'nhận được', 'số phần', 'đạt', 'amen', 'bao nả', 'gì', 'chung', 'ai', 'vừa rồi', 'mọi lúc', 'đối với', 'tiếp đó', 'càng hay', 'chốc chốc', 'được lời', 'bỗng dưng', 'quá tuổi', 'làm ngay', 'đều nhau', 'bất kỳ', 'qua lại', 'lên xuống', 'như thường', 'chẳng những', 'vị tất', 'xuể', 'ở được', 'lượng từ', 'lại đây', 'thoắt', 'đáng số', 'mang về', 'chung quy lại', 'dạ dài', 'điểm gặp', 'cách bức', 'như ý', 'qua khỏi', 'tự', 'nấy', 'nhà chung', 'nên', 'em', 'tất thảy', 'cho tin', 'cô quả', 'rồi sao', 'chưa từng', 'quận', 'rốt cục', 'tạo cơ hội', 'chỉ có', 'mở', 'vung thiên địa', 'đặt mức', 'bước đi', 'bên cạnh', 'thậm', 'ngôi nhà', 'choa', 'thà', 'thốt nhiên', 'dễ khiến', 'nước ăn', 'tránh khỏi', 'đều đều', 'thế ra', 'xoành xoạch', 'rích', 'dẫn', 'than ôi', 'nay', 'lớn', 'đúng ra', 'theo tin', 'tuy nhiên', 'tất tần tật', 'buổi mới', 'ngay', 'đủ', 'ngày càng', 'tạo ý', 'thường sự', 'dễ dùng', 'nhà ngươi', 'thiếu gì', 'chết tiệt', 'đưa xuống', 'là cùng', 'hay nói', 'bởi tại', 'ử', 'ở như', 'chuyển đạt', 'vậy nên', 'thế thì', 'tại', 'nhỉ', 'vô luận', 'vung tàn tán', 'hỏi xem', 'sang sáng', 'xăm xắm', 'ở trên', 'có điều kiện', 'nhiệt liệt', 'suýt nữa', 'lại giống', 'sáng ngày', 'tháng', 'trả ngay', 'buổi ngày', 'cái đó', 'nhất định', 'ăn tay', 'nghe đâu như', 'hết ráo', 'ông', 'dễ sử dụng', 'hay là', 'về không', 'hỗ trợ', 'về nước', 'âu là', 'không gì', 'những', 'mọi khi', 'thuộc', 'sở dĩ', 'thật tốt', 'tuốt tuột', 'mang mang', 'nói trước', 'nói lên', 'lâu nay', 'thích tự', 'cứ việc', 'không điều kiện', 'đến cùng cực', 'nặng mình', 'như nhau', 'phải cái', 'gây ra', 'chắc ăn', 'chắc vào', 'xảy ra', 'tỏ vẻ', 'đâu như', 'đây', 'đưa em', 'không kể', 'đến', 'nhà', 'ít quá', 'thanh thanh', 'sự thế', 'nhờ nhờ', 'giờ này', 'mất còn', 'quả thế', 'tốt bạn', 'từ tại', 'căn cái', 'cô tăng', 'nhất thiết', 'nào phải', 'bản', 'ngay lúc', 'dùng hết', 'áng như', 'còn thời gian', 'lúc lâu', 'của', 'thường thường', 'nói xa', 'nhìn chung', 'nó', 'vào lúc', 'ngoài xa', 'so', 'mỗi', 'tìm ra', 'cu cậu', 'lần', 'nào', 'úi', 'khó nghĩ', 'chắc', 'sáng ý', 'đến thế', 'đang', 'một vài', 'giá trị', 'sớm ngày', 'ngày xưa', 'dạ', 'hiện nay', 'cho chắc', 'nhờ', 'nặng về', 'trong ấy', 'dần dà', 'sang năm', 'là là', 'đặt', 'chung cục', 'lấy', 'dẫu sao', 'lấy ráo', 'tên tự', 'vào vùng', 'tạo nên', 'cụ thể là', 'biết mình', 'nhất nhất', 'gì gì', 'nặng căn', 'càng càng', 'kể cả', 'thì giờ', 'quá bán', 'thường tại', 'có đáng', 'làm bằng', 'dạ khách', 'có chăng', 'phốc', 'hay nhỉ', 'làm tôi', 'sắp', 'chỉ chính', 'có được', 'ít nhất', 'ô hô', 'dẫu mà', 'không phải không', 'sao bằng', 'trước tiên', 'thục mạng', 'đã vậy', 'nói đủ', 'ra lại', 'trước sau', 'bởi', 'đủ dùng', 'làm tại', 'vốn dĩ', 'nhé', 'đúng tuổi', 'vào', 'biết chừng nào', 'chưa cần', 'từ đó', 'về', 'mang nặng', 'hỏi', 'vừa qua', 'dạ bán', 'ngày ấy', 'dễ ngươi', 'nhằm vào', 'tăng giảm', 'nhược bằng', 'thếch', 'cá nhân', 'thực hiện', 'tháng năm', 'tà tà', 'nhất sinh', 'ông ấy', 'vâng', 'cụ thể', 'tính người', 'tìm hiểu', 'em em', 'tay', 'không', 'lên nước', 'không được', 'ờ', 'ngày này', 'quá', 'ít ra', 'khó thấy', 'tên', 'giá trị thực tế', 'tấm bản', 'rằng', 'nói là', 'trếu tráo', 'làm mất', 'ra lời', 'alô', 'rất', 'đang thì', 'chưa kể', 'tấn', 'đến ngày', 'cùng chung', 'mạnh', 'ví dù', 'chú dẫn', 'thay đổi', 'ít nữa', 'cần', 'cho tới', 'lần nào', 'xin vâng', 'tăng', 'trong khi', 'xem ra', 'đã thế', 'có', 'bỏ nhỏ', 'tắp lự', 'hay hay', 'phía dưới', 'chịu lời', 'bài bỏ', 'thộc', 'ba', 'khó tránh', 'người nghe', 'phải người', 'đại nhân', 'thanh điểm', 'chưa', 'ư', 'bỏ quá', 'thuộc bài', 'sa sả', 'số cụ thể', 'cao ráo', 'để lại', 'kể như', 'bán', 'không chỉ', 'đầy tuổi', 'rồi ra', 'đến đâu', 'như vậy', 'bác', 'nghiễm nhiên', 'bà', 'đó', 'ít khi', 'nước lên', 'a ha', 'gặp khó khăn', 'ngõ hầu', 'từ từ', 'vậy thì', 'ba bản', 'cấp', 'chiếc', 'lần trước', 'phóc', 'tại đó', 'làm lại', 'phỏng như', 'bán dạ', 'thế thường', 'nghĩ xa', 'ái dà', 'làm theo', 'toé khói', 'nhóm', 'trời đất ơi', 'xa gần', 'chứ không phải', 'có đâu', 'không cứ', 'ăn sáng', 'nhà ngoài', 'nức nở', 'nhìn thấy', 'cảm thấy', 'nếu như', 'dưới', 'thanh không', 'phần nào', 'bỗng', 'quá nhiều', 'ở lại', 'ở năm', 'đến bao giờ', 'nhìn lại', 'có khi', 'nên tránh', 'nghe trực tiếp', 'phù hợp', 'lúc đến', 'những lúc', 'ăn quá', 'ắt thật', 'giống', 'đặt để', 'ấy là', 'nhìn', 'vượt khỏi', 'ngay tức khắc', 'bỏ ra', 'tự cao', 'thêm giờ', 'chứ như', 'bấy chừ', 'nền', 'xuất kỳ bất ý', 'thoạt nghe', 'chung nhau', 'sẽ biết', 'không tính', 'béng', 'chậc', 'thật chắc', 'theo bước', 'một cách', 'chung quy', 'nghe đâu', 'hỏi lại', 'cha chả', 'bởi nhưng', 'nhận việc', 'đến xem', 'bằng ấy', 'lúc', 'ăn về', 'nghe rõ', 'ở vào', 'quay đi', 'trước nhất', 'đến tuổi', 'làm sao', 'chơi', 'tăng thế', 'ứ ừ', 'thường khi', 'bởi ai', 'cho rằng', 'cây nước', 'xem số', 'lên mạnh', 'công nhiên', 'với', 'lấy lại', 'phụt', 'ăn ngồi', 'vấn đề quan trọng', 'trển', 'nhằm', 'ngay lập tức', 'lên cơn', 'nhớ bập bõm', 'phải chi', 'bức', 'nhiều ít', 'thật lực', 'cho nhau', 'tháng ngày', 'nguồn', 'thật thà', 'về tay', 'dài lời', 'giờ đến', 'cùng cực', 'bấy nay', 'rốt cuộc', 'lần tìm', 'gì đó', 'hay làm', 'tự vì', 'xa cách', 'trước đó', 'điều kiện', 'thấp cơ', 'khó biết', 'ngày nọ', 'phần việc', 'bên', 'do', 'từ ấy', 'để không', 'nữa rồi', 'sốt sột', 'nghĩ', 'gần bên', 'có ăn', 'gây', 'bằng người', 'bèn', 'qua', 'tại tôi', 'thuần ái', 'đủ nơi', 'đưa tới', 'ngay khi đến', 'trở thành', 'lấy sau', 'tăm tắp', 'ào ào', 'bắt đầu từ', 'nói chung', 'do đó', 'trả của', 'thứ', 'phải tay', 'tuy vậy', 'văng tê', 'thành ra', 'tới thì', 'ăn riêng', 'bởi đâu', 'đáo để', 'nhằm lúc', 'thà là', 'con', 'thanh tính', 'vâng vâng', 'bao giờ', 'vụt', 'đại phàm', 'là nhiều', 'nặng', 'ở đó', 'trỏng', 'cả người', 'hay tin', 'tránh', 'nhanh tay', 'bán thế', 'tôi con', 'cần cấp', 'cha', 'tại đây', 'cho đến nỗi', 'đưa vào', 'dễ', 'luôn', 'dạ dạ', 'trừ phi', 'trên', 'bất nhược', 'đại để', 'phải lời', 'cậu', 'nhón nhén', 'thoạt', 'nếu cần', 'không biết', 'thích cứ', 'nữa là', 'trước hết', 'ơi là', 'nhưng mà', 'giờ', 'cơn', 'rén bước', 'chớ', 'có cơ', 'lâu lâu', 'năm', 'một khi', 'phắt', 'đơn vị', 'đến khi', 'thấp', 'ai nấy', 'không bao lâu', 'tin vào', 'tới', 'phải biết', 'bất kì', 'chết nỗi', 'khá tốt', 'nói riêng', 'chưa dùng', 'mỗi lần', 'sau chót', 'từ ái', 'hãy còn', 'nhờ đó', 'cũng vậy', 'khó mở', 'lấy để', 'một ít', 'bài cái', 'nếu được', 'trong đó', 'thế mà', 'bất quá chỉ', 'tít mù', 'của tin', 'phía', 'phải cách', 'chăng chắc', 'nhà việc', 'bất giác', 'từ thế', 'nói với', 'lượng cả', 'ngay lúc này', 'hơn nữa', 'loại', 'ngươi', 'nói nhỏ', 'đến giờ', 'quá tin', 'lấy cả', 'đã là', 'cho về', 'có chăng là', 'tuy', 'thật quả', 'chí chết', 'tự tạo', 'đúng ngày', 'tuy có', 'từ khi', 'buổi làm', 'dần dần', 'những ai', 'veo veo', 'bước khỏi', 'chắc dạ', 'là vì', 'nói ý', 'ví bằng', 'hoặc là', 'cho đến khi', 'như không', 'cả thảy', 'ngay cả', 'gần hết', 'đâu nào', 'khó khăn', 'quá tay', 'tên chính', 'nhận biết', 'chú mày', 'qua thì', 'bao lâu', 'sắp đặt', 'từng', 'chịu tốt', 'vừa', 'khi không', 'căn cắt', 'bằng được', 'ba ngôi', 'xoẹt', 'thêm', 'là thế nào', 'á à', 'đã không', 'xuất kì bất ý', 'mở ra', 'chính thị', 'ngay thật', 'lên', 'ô hay', 'ô kê', 'bị', 'ra tay', 'chợt nghe', 'chị', 'chuyển', 'bông', 'khẳng định', 'thì là', 'quả', 'tuốt tuồn tuột', 'có ý', 'nhỏ người', 'oai oái', 'không để', 'đến lúc', 'có vẻ', 'ý chừng', 'bởi chưng', 'chỉn', 'ở nhờ', 'chia sẻ', 'lại còn', 'mới rồi', 'cô ấy', 'biết mấy', 'ớ này', 'nhờ có', 'chỉ', 'cái ấy', 'mọi sự', 'tù tì', 'điều', 'sau sau', 'sang tay', 'ngoài này', 'cơ mà', 'vâng chịu', 'xem lại', 'từ giờ', 'đánh giá', 'lúc nào', 'phải rồi', 'điều gì', 'bội phần', 'cổ lai', 'sẽ hay', 'ra bài', 'gây cho', 'dù sao', 'luôn cả', 'cao số', 'để phần', 'cứ điểm', 'khó', 'như ai', 'gần đến', 'vừa mới', 'tọt', 'dù cho', 'lâu các', 'mọi', 'ra ngôi', 'người người', 'đưa về', 'nên người', 'gặp phải', 'nhận ra', 'chợt', 'bất kể', 'tập trung', 'nước xuống', 'xăm xúi', 'hãy', 'loại từ', 'ồ', 'bập bõm', 'xa tắp', 'tấm', 'có nhiều', 'nói lại', 'cô', 'hết cả', 'cơ hồ', 'cùng nhau', 'cả', 'ăn hỏi', 'bộ', 'số người', 'ngôi thứ', 'tênh tênh', 'tính phỏng', 'thế là', 'mỗi lúc', 'số cho biết', 'nói rõ', 'lúc ấy', 'thuần', 'cả nhà', 'bởi thế', 'cao sang', 'bây chừ', 'qua chuyện', 'thẩy', 'tên cái', 'trước kia', 'đến gần', 'rón rén', 'bước', 'giữ', 'ra vào', 'xon xón', 'thường số', 'biết trước', 'gần ngày', 'cách đều', 'nghen', 'phần sau', 'cả đến', 'cả nghĩ', 'lại ăn', 'ngay từ', 'nhất tâm', 'mọi thứ', 'chớ kể', 'xềnh xệch', 'chuẩn bị', 'dài', 'tiện thể', 'nhanh lên', 'sau nữa', 'bất cứ', 'tấm các', 'đến cả', 'chẳng phải', 'đến điều', 'lấy làm', 'mà thôi', 'nhận làm', 'ví phỏng', 'xem', 'ở đây', 'sự việc', 'khỏi', 'sau cùng', 'lý do', 'không bao giờ', 'nhỡ ra', 'không phải', 'tột cùng', 'nào hay', 'được nước', 'chưa dễ', 'vả chăng', 'phải không', 'từng nhà', 'đưa tay', 'xa nhà', 'là ít', 'chứ', 'ít nhiều', 'ngày nào', 'phải', 'như trước', 'biết được', 'hiện tại', 'thêm chuyện', 'thế sự', 'bấy lâu nay', 'xa xả', 'vì', 'trệu trạo', 'kể từ', 'tớ', 'sì sì', 'được', 'quả vậy', 'trước đây', 'cấp trực tiếp', 'đảm bảo', 'bộ thuộc', 'ôi thôi', 'từng giờ', 'mới', 'thời gian tính', 'thời điểm', 'cao lâu', 'hơn cả', 'nhân dịp', 'ngày ngày', 'lấy được', 'nói thật', 'cơ chỉ', 'ạ ơi', 'trước tuổi', 'tạo', 'ba tăng', 'mang', 'khó nghe', 'lên ngôi', 'thế thế', 'tìm bạn', 'thôi việc', 'cao', 'thốt nói', 'cũng vậy thôi', 'tuy rằng', 'nhìn xuống', 'tránh xa', 'qua đi', 'để đến nỗi', 'với nhau', 'nơi', 'thật ra', 'qua tay', 'tông tốc', 'cho hay', 'lúc đó', 'người khách', 'ra điều', 'thanh chuyển', 'không có gì', 'cơ chừng', 'bị chú', 'rằng là', 'biết thế', 'qua lần', 'chứ lị', 'bên bị', 'làm ra', 'bấy chầy', 'thường xuất hiện', 'ào', 'biết', 'sau đó', 'nước quả', 'cuối điểm', 'ý', 'số', 'gồm', 'tức tốc', 'vì rằng', 'thế lại', 'nghe nhìn', 'hỏi xin', 'nhận', 'bỏ riêng', 'được tin', 'vài tên', 'có chứ', 'tính căn', 'người hỏi', 'mỗi người', 'tăng cấp', 'vèo vèo', 'chầm chập', 'hoàn toàn', 'chao ôi', 'biết đâu chừng', 'thỉnh thoảng', 'khoảng', 'hơn trước', 'ớ', 'sang', 'bà ấy', 'lại nói', 'răng răng', 'dưới nước', 'nào cũng', 'dẫu', 'hay sao', 'lượng', 'lần sang', 'phỏng', 'là phải', 'đã lâu', 'với lại', 'thực vậy', 'ý hoặc', 'như tuồng', 'nhưng', 'không dùng', 'còn về', 'có số', 'lòng không', 'dạ con', 'quá mức', 'đến nay', 'tanh', 'quá bộ', 'đây rồi', 'phè', 'vậy mà', 'ngọt', 'ngọn nguồn', 'gặp', 'trong này', 'cao thấp', 'thấp thỏm', 'cùng tột', 'chú', 'nhằm khi', 'phứt', 'chính là', 'mức', 'tốt bộ', 'phía bạn', 'vâng dạ', 'quay số', 'nên làm', 'phần nhiều', 'thế à', 'là', 'bởi thế cho nên', 'nhờ chuyển', 'ra đây', 'làm lấy', 'thực sự', 'cái', 'trong mình', 'lúc trước', 'lại quả', 'toẹt', 'hầu hết', 'lại nữa', 'vẫn thế', 'nói toẹt', 'đến nỗi', 'có thể', 'vài ba', 'gần xa', 'chắc chắn', 'nói tốt', 'ơ hay', 'bước tới', 'đến lời', 'thậm từ', 'bất luận', 'riêng', 'có chuyện', 'khỏi nói', 'mới đây', 'chớ như', 'ngày', 'từng phần', 'duy có', 'quá lời', 'theo như', 'bấy giờ', 'làm tắp lự', 'nhất là', 'phỏng theo', 'đưa cho', 'rõ', 'bỏ mình', 'đâu cũng', 'cũng nên', 'thành thử', 'luôn luôn', 'ăn cuộc', 'đều', 'tại sao', 'phải chăng', 'nói đến', 'tuổi tôi', 'giờ lâu', 'nói khó', 'cái gì', 'ắt hẳn', 'cơ dẫn', 'lấy lý do', 'lớn nhỏ', 'bài', 'các', 'cả nghe', 'bằng nấy', 'rồi nữa', 'lên cao', 'lần sau', 'phương chi', 'xiết bao', 'bây nhiêu', 'chết thật', 'chọn ra', 'ráo cả', 'đặt làm', 'chọn', 'bỏ không', 'vài nhà', 'chứ còn', 'đáng lí', 'dành dành', 'lấy có', 'mất', 'qua ngày', 'cách', 'không hay', 'thấy tháng', 'điểm chính', 'thường bị', 'sau cuối', 'thảo nào', 'chớ không', 'câu hỏi', 'nhất', 'mọi người', 'vạn nhất', 'như quả', 'ơi', 'chui cha', 'cả ngày', 'muốn', 'dùng', 'bệt', 'không có', 'quay bước', 'dễ đâu', 'tính từ', 'chẳng nữa', 'nói thêm', 'thay đổi tình trạng', 'chứ không', 'quá ư', 'liên quan', 'giảm chính', 'biết bao', 'ổng', 'khá', 'hết chuyện', 'đưa', 'vùng nước', 'ông ổng', 'thế chuẩn bị', 'bằng không', 'làm lòng', 'cần gì', 'lần khác', 'đưa chuyện', 'không đầy', 'dễ sợ', 'hoặc', 'thiếu điểm', 'bằng vào', 'bỏ bà', 'thế', 'hết nói', 'nếu không', 'xoét', 'anh ấy', 'bỏ cha', 'cuối cùng', 'cuốn', 'nghĩ tới', 'giữ lấy', 'khi khác', 'sáng thế', 'ít có', 'giờ đi', 'ắt là', 'mối', 'đáng lý', 'chúng ta', 'nhung nhăng', 'nhất tề', 'ừ ào', 'bỗng nhưng', 'dễ thường', 'bản ý', 'thiếu', 'không còn', 'đúng', 'chuyển tự', 'úi dào', 'thêm vào', 'chịu chưa', 'điểm đầu tiên', 'khách', 'để', 'trong lúc', 'tốt hơn', 'khiến', 'nghỉm', 'lại người', 'có điều', 'thốt', 'cho được', 'còn', 'so với', 'đó đây', 'đến thì', 'bất thình lình', 'khác nào', 'đến cùng', 'chuyện', 'chưa tính', 'ra sao', 'làm nên', 'đến nơi', 'vượt', 'chưa có', 'từng ấy', 'bỏ cuộc', 'thời gian sử dụng', 'thì thôi', 'từ nay', 'chợt nhìn', 'lên đến', 'chị ấy', 'nhất loạt', 'ngày xửa', 'chung qui', 'ăn hết', 'đưa đến', 'nước bài', 'thường tính', 'nói bông', 'mỗi một', 'cơ hội', 'tự tính', 'còn như', 'không ai', 'nghĩ lại', 'giảm thế', 'lấy vào', 'như thế nào', 'vậy ư', 'thì phải', 'cho rồi', 'tăng chúng', 'lâu', 'để lòng', 'khó nói', 'chăng nữa', 'ba ngày', 'lại bộ', 'cách nhau', 'mỗi ngày', 'tuy thế', 'đáng', 'ai ai', 'trả lại', 'dành', 'chăn chắn', 'vậy', 'nhân tiện', 'ăn người', 'tỏ ra', 'anh', 'lấy số', 'ông tạo', 'chung cho', 'quá đáng', 'bên có', 'có dễ', 'xuống', 'đưa ra', 'dễ như chơi', 'khác thường', 'người', 'thế nên', 'nhằm để', 'nếu thế', 'chắc lòng', 'mang lại', 'ở', 'vào gặp', 'dễ thấy', 'nghe chừng', 'veo', 'dù', 'có ngày', 'nhớ lấy', 'rất lâu', 'a lô', 'đến hay', 'đồng thời', 'nhất mực', 'nước đến', 'giữa', 'cụ thể như', 'thật sự', 'buổi sớm', 'thứ bản', 'khác khác', 'cái đã', 'thật là', 'sao đang', 'nước nặng', 'chùn chũn', 'đâu đâu', 'chung cuộc', 'vì sao', 'tìm cách', 'nhau', 'xa xa', 'trả', 'duy chỉ', 'như chơi', 'chơi họ', 'có người', 'cô mình', 'nghe không', 'tìm việc', 'về phần', 'vở', 'chính bản', 'bất tử', 'ngoài', 'mở nước', 'mọi nơi', 'chăng', 'phỏng nước', 'chúng tôi', 'à này', 'tò te', 'nếu mà', 'cả năm', 'ví thử', 'tháng tháng', 'phải giờ', 'hơn', 'phần lớn', 'trực tiếp', 'vừa vừa', 'buổi', 'xa', 'chú khách', 'có họ', 'hay', 'nơi nơi', 'số thiếu', 'cơ cùng', 'mình', 'đều bước', 'ngồi bệt', 'ngày cấp', 'tối ư', 'riu ríu', 'kể', 'nhớ lại', 'từng cái', 'phần', 'con con', 'sử dụng', 'mà', 'lần này', 'tột', 'vùng lên', 'ăn chung', 'thái quá', 'cho đang', 'ngày tháng', 'thương ôi', 'quay lại', 'tự khi', 'có ai', 'lấy thế', 'do vậy', 'khi nên', 'nọ', 'đâu đó', 'sau', 'cho nên', 'giống người', 'nhanh', 'tuy là', 'bản thân', 'vả lại', 'thím', 'những khi', 'ngày giờ', 'thì', 'ngoải', 'căn', 'nên chi', 'vậy ra', 'tuổi cả', 'có thế', 'ối giời', 'tha hồ chơi', 'chung ái', 'gần đây', 'tựu trung', 'trước nay', 'tuần tự', 'lớn lên', 'vì chưng', 'tới nơi', 'luôn tay', 'gây giống', 'chớ chi', 'rồi sau', 'trên bộ', 'khác gì', 'chú mình', 'chứ ai', 'làm riêng', 'ăn', 'thấy', 'tấn tới', 'ừ nhé', 'nghe được', 'bằng nhau', 'vài người', 'ái chà', 'nhìn theo', 'nói phải', 'như thể', 'không cùng', 'dào', 'phía trên', 'cả tin', 'gần như', 'thuộc lại', 'tính', 'dễ gì', 'nhất đán', 'vượt quá', 'dài ra', 'vô hình trung', 'bị vì', 'sẽ', 'đúng với', 'chính', 'thật vậy', 'cây', 'ăn chắc', 'ứ hự', 'cho thấy', 'bây bẩy', 'này', 'tắp', 'rày', 'từ căn', 'ào vào', 'quả thật', 'làm vì', 'à ơi', 'song le', 'giống như', 'dữ', 'người mình', 'thanh', 'chu cha', 'mở mang', 'nhà làm', 'tự ý', 'cứ', 'cóc khô', 'hay không', 'không thể', 'năm tháng', 'đang tay', 'chịu ăn', 'thanh điều kiện', 'vừa khi', 'biết việc', 'căn tính', 'trong vùng', 'đủ số', 'chỉ là', 'tình trạng', 'ra bộ', 'tôi', 'không ngoài', 'đâu phải', 'đây đó', 'chưa bao giờ', 'tuy đã', 'tới gần', 'bây giờ', 'nữa khi', 'bỏ xa', 'nữa', 'như trên', 'sáng', 'ra', 'tại nơi', 'vấn đề', 'cuối', 'đặt trước', 'giờ đây', 'lại thôi', 'nước cùng', 'suýt', 'ít', 'sất', 'ắt', 'làm', 'đây này', 'vô vàn', 'tốt', 'phía trước', 'ra gì', 'hay đâu', 'sớm', 'dễ ăn', 'thoạt nhiên', 'vì vậy', 'ngoài ra', 'những muốn', 'ra chơi', 'sự', 'tạo điều kiện', 'làm đúng', 'nhất thì', 'nghe ra', 'được cái', 'ráo nước', 'lại', 'rén', 'thường đến', 'ít biết', 'nhận họ', 'ăn trên', 'lấy ra', 'phải lại', 'vâng ý', 'thôi'}\n", "{'do', 're', \"he'd\", 'i', 'after', 'now', 'before', 'at', 'shouldn', 'is', 'too', 'off', \"she'll\", 'each', 'hadn', 'few', \"he's\", 'into', 'didn', 'myself', 'this', 'them', \"doesn't\", 'who', 'if', 'won', 'most', 'both', 'd', 'had', 'further', \"didn't\", 'me', \"we're\", 'no', 'under', 'while', 'have', \"i'd\", 'mightn', 'by', 'above', \"you'd\", \"i'll\", 'and', 'was', \"she's\", 'herself', 'on', 'or', 'just', 'between', \"he'll\", \"you'll\", 'her', 'has', 'hers', 'needn', 'weren', 'will', \"you're\", 'as', 'against', 'ours', 'wouldn', 'these', 'so', 'any', \"she'd\", \"don't\", 'haven', 'y', 'him', \"i've\", 'couldn', 'they', 'but', 'to', \"wouldn't\", \"we'll\", \"shan't\", 'hasn', 'she', \"they've\", 'about', \"needn't\", \"it'd\", 'how', 'can', 'he', \"aren't\", 'my', 'until', \"couldn't\", 'in', \"weren't\", 'which', \"haven't\", \"they'd\", \"they'll\", 'out', \"wasn't\", \"i'm\", 'same', \"we've\", 'because', 'ain', 's', 'when', 'wasn', \"isn't\", \"that'll\", 'their', 'such', 've', 'whom', 'yours', 'all', 'does', 'its', 'nor', 'our', 'are', 'being', \"it's\", 'than', 'that', 'from', 'yourselves', 'be', \"should've\", 'where', 'again', 'having', 'other', 'through', 'with', 'your', 'shan', 'the', 'o', 'own', 'were', 'am', 'more', 'some', 't', 'themselves', 'theirs', 'm', \"we'd\", 'his', 'then', 'ma', 'll', 'ourselves', \"hadn't\", 'isn', 'down', 'for', \"it'll\", 'you', \"won't\", 'during', 'there', \"hasn't\", 'of', \"mustn't\", 'it', \"they're\", 'only', 'doing', 'mustn', \"you've\", 'why', 'don', \"shouldn't\", 'we', 'did', 'here', 'himself', 'itself', 'should', 'once', 'been', 'very', 'those', 'an', 'aren', 'doesn', \"mightn't\", 'up', 'what', 'not', 'below', 'yourself', 'over', 'a'}\n"]}], "source": ["with open(stop_words_vn_txt, 'r', encoding='utf-8') as f:\n", "    stop_words_vi = set(word.strip() for word in f if word.strip())\n", "stop_words_en = set(stopwords.words('english'))\n", "print(stop_words_vi)\n", "print(stop_words_en)"]}, {"cell_type": "code", "execution_count": 17, "id": "818115a9", "metadata": {}, "outputs": [], "source": ["df['description_cleaned'] = df.apply(lambda x: clean_text(x['description_en'] if x['language'] == 'en' else x['description'], x['language']), axis=1)\n", "df['requirements_cleaned'] = df.apply(lambda x: clean_text(x['requirements_en'] if x['language'] == 'en' else x['requirements'], x['language']), axis=1)\n", "df['skills_cleaned'] = df['skills_en'].apply(lambda x: clean_skills(x))"]}, {"cell_type": "code", "execution_count": 18, "id": "d6568db8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['<PERSON>', '<PERSON> <PERSON>', 'Others', '<PERSON> <PERSON> <PERSON>',\n", "       '<PERSON>', '<PERSON>',\n", "       '<PERSON>', '<PERSON>',\n", "       '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "       '<PERSON> Nan<PERSON>', '<PERSON>',\n", "       '<PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON> - Others',\n", "       '<PERSON>', 'Thai Nguyen', '<PERSON><PERSON>ng',\n", "       '<PERSON> - Ha <PERSON> - Others', '<PERSON> - Others',\n", "       '<PERSON> - <PERSON>'], dtype=object)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df['location'].unique()"]}, {"cell_type": "code", "execution_count": 19, "id": "1dcfa1ca", "metadata": {}, "outputs": [], "source": ["locations = np.array([\n", "    '<PERSON>', '<PERSON> <PERSON>', 'Others', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "    '<PERSON>', '<PERSON>',\n", "    '<PERSON>', '<PERSON>',\n", "    '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "    '<PERSON> Nan<PERSON>', '<PERSON>',\n", "    '<PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON> - Others',\n", "    '<PERSON>', 'Thai Nguyen', '<PERSON><PERSON>ng',\n", "    '<PERSON> - Ha <PERSON> - Others', '<PERSON> - Others',\n", "    '<PERSON> - <PERSON> <PERSON>'\n", "])\n", "\n", "main_cities = {'Ha Noi', 'Ho Chi Minh'}\n", "\n", "def classify_location_group(loc_str):\n", "    # Tách và làm sạch địa điểm\n", "    cities = [c.strip() for c in loc_str.split('-')]\n", "    cities_set = set(cities)\n", "\n", "    # Giao giữa cities_set và main_cities\n", "    common_cities = cities_set & main_cities\n", "\n", "    if common_cities == {'Ha Noi'}:\n", "        return 1\n", "    elif common_cities == {'Ho Chi Minh'}:\n", "        return 2\n", "    else:\n", "        return 3"]}, {"cell_type": "code", "execution_count": 20, "id": "a5b5b824", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["location_group\n", "2    551\n", "1    352\n", "3     92\n", "Name: count, dtype: int64\n", "0    2\n", "1    2\n", "2    1\n", "3    1\n", "4    1\n", "5    1\n", "6    2\n", "7    2\n", "8    1\n", "9    2\n", "Name: location_group, dtype: int64\n"]}], "source": ["df['location_group'] = df['location'].apply(lambda x: classify_location_group(x))\n", "print(df['location_group'].value_counts())\n", "print(df['location_group'].head(10))"]}, {"cell_type": "code", "execution_count": 21, "id": "62e17f1a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>company</th>\n", "      <th>location</th>\n", "      <th>salary</th>\n", "      <th>work_type</th>\n", "      <th>description</th>\n", "      <th>requirements</th>\n", "      <th>skills</th>\n", "      <th>language</th>\n", "      <th>skills_en</th>\n", "      <th>description_en</th>\n", "      <th>requirements_en</th>\n", "      <th>description_cleaned</th>\n", "      <th>requirements_cleaned</th>\n", "      <th>skills_cleaned</th>\n", "      <th>location_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>MLops Engineer</td>\n", "      <td>Trusting Social</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>We are looking for qualified MLops Engineer fo...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>en</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>We are looking for qualified MLops Engineer fo...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>look qualified mlop engineer ekyc project help...</td>\n", "      <td>bs ms computer science relate field 1 3 year e...</td>\n", "      <td>[MLOps, Python, Linux, Docker, Data Science, T...</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Senior Dev<PERSON>ps Engineer (Cloud, AWS)</td>\n", "      <td>TymeX</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>We are seeking an experienced Senior DevOps En...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>en</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>We are seeking an experienced Senior DevOps En...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>seek experienced senior devop engineer aw join...</td>\n", "      <td>requirement bachelor master degree computer sc...</td>\n", "      <td>[AWS, DevOps, Cloud, Cloud-native Architecture...</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>VTS - <PERSON><PERSON><PERSON><PERSON><PERSON> (Agile/ Azure)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>Ecotek đang dẫn dắt Ecopark phát triển trở thà...</td>\n", "      <td>T<PERSON> duy logic tốt, tư duy hướ<PERSON> g<PERSON><PERSON>, tư d...</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>vi</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>Ecotek is leading Ecopark to develop a model o...</td>\n", "      <td>Good logical thinking, solution -oriented thin...</td>\n", "      <td>ecotek dẫn dắt ecopark phát triển mô hình thàn...</td>\n", "      <td>tư duy logic tư duy hướng giải pháp tư duy phả...</td>\n", "      <td>[Project Management, Business Analysis, Presal...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>VTS - <PERSON><PERSON> - Presales Engineer</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td><PERSON><PERSON> <PERSON>vũ tr<PERSON> công ng<PERSON> Viettel, n<PERSON>i bạn k...</td>\n", "      <td>Bằng cấp: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (loại Khá trở lên...</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>vi</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>Joining Viettel technology, where you are not ...</td>\n", "      <td>Degree: Graduated from university (good or hig...</td>\n", "      <td>gia nh<PERSON><PERSON> vũ trụ công nghệ viettel đắm chìm hàn...</td>\n", "      <td>bằng cấp tốt nghiệp đại học trở chuyên ngành c...</td>\n", "      <td>[Presale, Business Analysis, Salesforce, Pre-s...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>VHT - Embedded Software Engineer (Linux, C++)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>650 - 2,200 USD</td>\n", "      <td>Not specified</td>\n", "      <td><PERSON><PERSON><PERSON> 1200 nhân sự chất lư<PERSON>o , Tổng Côn...</td>\n", "      <td><PERSON><PERSON><PERSON> nghi<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>vi</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>With more than 1200 high quality personnel, Vi...</td>\n", "      <td>Graduated with regular university or higher sp...</td>\n", "      <td>1200 nhân sự chất lượng tổng công ty công nghi...</td>\n", "      <td>tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...</td>\n", "      <td>[Embedded, C++, Linux, C language, Embedded En...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>990</th>\n", "      <td>11</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> viên (Hỗ trợ ứng dụng OPN)</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>991</th>\n", "      <td>12</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> viên (Vận hành AM/DevOPs CI/CD)</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>992</th>\n", "      <td>13</td>\n", "      <td><PERSON><PERSON> li<PERSON> (DE) - <PERSON> Enginner</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>993</th>\n", "      <td>14</td>\n", "      <td>CV Phát triển DEV BE/FE/Fullstack</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>994</th>\n", "      <td>15</td>\n", "      <td>CV Phân tích nghi<PERSON> vụ và <PERSON>ử</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specifi</td>\n", "      <td>fr</td>\n", "      <td>Not specifi</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specifi]</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>995 rows × 17 columns</p>\n", "</div>"], "text/plain": ["     id                                            title  \\\n", "0     1                                   MLops Engineer   \n", "1     2              Senior DevOps Engineer (Cloud, AWS)   \n", "2     3  VTS - <PERSON><PERSON><PERSON>n <PERSON> (Agile/ Azure)   \n", "3     4       VTS - <PERSON>ư <PERSON>ấn G<PERSON> - Presales Engineer   \n", "4     5    VHT - Embedded Software Engineer (Linux, C++)   \n", "..   ..                                              ...   \n", "990  11                <PERSON><PERSON><PERSON><PERSON> viên (Hỗ trợ ứng dụng OPN)   \n", "991  12           <PERSON><PERSON><PERSON><PERSON> viên (Vận hành AM/DevOPs CI/CD)   \n", "992  13               <PERSON><PERSON> s<PERSON> dữ liệu (DE) - <PERSON>ner   \n", "993  14                CV Phát triển DEV BE/FE/Fullstack   \n", "994  15               CV Phân tích nghi<PERSON> v<PERSON> và <PERSON> thử   \n", "\n", "                                        company     location           salary  \\\n", "0                               Trusting Social  Ho Chi Minh   You'll love it   \n", "1                                         TymeX  Ho Chi Minh   You'll love it   \n", "2                                 Viettel Group       Ha Noi   You'll love it   \n", "3                                 Viettel Group       Ha Noi   You'll love it   \n", "4                                 Viettel Group       Ha Noi  650 - 2,200 USD   \n", "..                                          ...          ...              ...   \n", "990  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "991  <PERSON>ân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "992  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "993  <PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "994  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "\n", "         work_type                                        description  \\\n", "0    Not specified  We are looking for qualified MLops Engineer fo...   \n", "1    Not specified  We are seeking an experienced Senior DevOps En...   \n", "2    Not specified  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3    Not specified  <PERSON><PERSON> “vũ tr<PERSON>” công ng<PERSON> Viettel, n<PERSON>i bạn k...   \n", "4    Not specified  <PERSON><PERSON><PERSON>n 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "..             ...                                                ...   \n", "990  Not specified                           No description available   \n", "991  Not specified                           No description available   \n", "992  Not specified                           No description available   \n", "993  Not specified                           No description available   \n", "994  Not specified                           No description available   \n", "\n", "                                          requirements  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3    Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4    Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "..                                                 ...   \n", "990                          No requirements specified   \n", "991                          No requirements specified   \n", "992                          No requirements specified   \n", "993                          No requirements specified   \n", "994                          No requirements specified   \n", "\n", "                                                skills language  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2    Project Management, Business Analysis, Presale...       vi   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "..                                                 ...      ...   \n", "990                                      Not specified       fr   \n", "991                                      Not specified       fr   \n", "992                                      Not specified       fr   \n", "993                                      Not specified       fr   \n", "994                                        Not specifi       fr   \n", "\n", "                                             skills_en  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...   \n", "2    Project Management, Business Analysis, Presale...   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...   \n", "..                                                 ...   \n", "990                                      Not specified   \n", "991                                      Not specified   \n", "992                                      Not specified   \n", "993                                      Not specified   \n", "994                                        Not specifi   \n", "\n", "                                        description_en  \\\n", "0    We are looking for qualified MLops Engineer fo...   \n", "1    We are seeking an experienced Senior DevOps En...   \n", "2    Ecotek is leading Ecopark to develop a model o...   \n", "3    Joining Viettel technology, where you are not ...   \n", "4    With more than 1200 high quality personnel, Vi...   \n", "..                                                 ...   \n", "990                           No description available   \n", "991                           No description available   \n", "992                           No description available   \n", "993                           No description available   \n", "994                           No description available   \n", "\n", "                                       requirements_en  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Good logical thinking, solution -oriented thin...   \n", "3    Degree: Graduated from university (good or hig...   \n", "4    Graduated with regular university or higher sp...   \n", "..                                                 ...   \n", "990                          No requirements SPECIFied   \n", "991                          No requirements SPECIFied   \n", "992                          No requirements SPECIFied   \n", "993                          No requirements SPECIFied   \n", "994                          No requirements SPECIFied   \n", "\n", "                                   description_cleaned  \\\n", "0    look qualified mlop engineer ekyc project help...   \n", "1    seek experienced senior devop engineer aw join...   \n", "2    ecotek dẫn dắt ecopark phát triển mô hình thàn...   \n", "3    gia nhập vũ trụ công nghệ viettel đắm chìm hàn...   \n", "4    1200 nhân sự chất lượng tổng công ty công nghi...   \n", "..                                                 ...   \n", "990                           no description available   \n", "991                           no description available   \n", "992                           no description available   \n", "993                           no description available   \n", "994                           no description available   \n", "\n", "                                  requirements_cleaned  \\\n", "0    bs ms computer science relate field 1 3 year e...   \n", "1    requirement bachelor master degree computer sc...   \n", "2    tư duy logic tư duy hướng gi<PERSON>i pháp tư duy phả...   \n", "3    bằng cấp tốt nghiệp đại học trở chuyên ngành c...   \n", "4    tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...   \n", "..                                                 ...   \n", "990                          no requirements specified   \n", "991                          no requirements specified   \n", "992                          no requirements specified   \n", "993                          no requirements specified   \n", "994                          no requirements specified   \n", "\n", "                                        skills_cleaned  location_group  \n", "0    [MLOps, Python, Linux, Docker, Data Science, T...               2  \n", "1    [A<PERSON>, DevOps, Cloud, Cloud-native Architecture...               2  \n", "2    [Project Management, Business Analysis, Presal...               1  \n", "3    [Presale, Business Analysis, Salesforce, Pre-s...               1  \n", "4    [Embedded, C++, Linux, C language, Embedded En...               1  \n", "..                                                 ...             ...  \n", "990                                    [Not specified]               1  \n", "991                                    [Not specified]               1  \n", "992                                    [Not specified]               1  \n", "993                                    [Not specified]               1  \n", "994                                      [Not specifi]               1  \n", "\n", "[995 rows x 17 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 22, "id": "a4f07def", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5508\\1309361321.py:2: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value 'JOB_0' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  df.at[i, 'id'] = \"JOB_\" + str(i)\n"]}], "source": ["for i in range(len(df)):\n", "    df.at[i, 'id'] = \"JOB_\" + str(i)\n"]}, {"cell_type": "code", "execution_count": 23, "id": "f8369737", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([\"You'll love it\", '650 - 2,200 USD', 'Very attractive!!!',\n", "       'Up to 18,000,000 vnđ', '800 - 1,000 USD', '3,000 - 6,000 USD',\n", "       '2,000 - 5,000 USD', '900 - 1,600 USD', '700 - 1,800 USD',\n", "       '1,000 - 2,500 USD', '800 - 3,500 USD', '700 - 1,000 USD',\n", "       'Up to 30m', '2,500 - 3,000 USD', '2,000 - 4,000 USD',\n", "       '500 - 1,000 USD', 'Negotiation', '1,000 - 1,400 USD',\n", "       '1,000 - 3,500 USD', 'Not specified', '<PERSON><PERSON><PERSON><PERSON> cao thỏa thuận',\n", "       'Negotiable', '1,000 - 2,000 USD', '1,800 - 3,500 USD',\n", "       'Tới 35 triệu'], dtype=object)"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df['salary'].unique()"]}, {"cell_type": "code", "execution_count": 24, "id": "83d17f33", "metadata": {}, "outputs": [], "source": ["df.drop(columns=['work_type'], inplace=True)"]}, {"cell_type": "code", "execution_count": 25, "id": "2a2c558f", "metadata": {}, "outputs": [], "source": ["def extract_min_salary(s):\n", "    if not isinstance(s, str):\n", "        return 'agreement'\n", "\n", "    s = s.lower()\n", "\n", "    # Nhóm 5: <PERSON><PERSON><PERSON><PERSON> thỏa thuận\n", "    if any(keyword in s for keyword in ['not specified', 'negotiation', 'negotiable', 'thỏa thuận', \"you'll love it\", 'attractive']):\n", "        return 'agreement'\n", "\n", "    # Lương VNĐ có đơn vị triệu hoặc 'm'\n", "    if 'm' in s or 'triệu' in s:\n", "        match = re.findall(r'\\d+', s)\n", "        if match:\n", "            usd = int(match[0]) * 1_000_000 / 24000  # <PERSON><PERSON><PERSON> sang USD\n", "            return usd\n", "\n", "    # Lương USD\n", "    match = re.findall(r'\\d+(?:,\\d+)?', s)\n", "    if match:\n", "        nums = [int(x.replace(',', '')) for x in match]\n", "        return min(nums)\n", "\n", "    return 'agreement'\n", "\n", "def classify_salary(s):\n", "    val = extract_min_salary(s)\n", "\n", "    if val == 'agreement':\n", "        return 5\n", "    elif val < 500:\n", "        return 1\n", "    elif val < 2000:\n", "        return 2\n", "    elif val < 5000:\n", "        return 3\n", "    else:\n", "        return 4"]}, {"cell_type": "code", "execution_count": 26, "id": "7f84b7ed", "metadata": {}, "outputs": [], "source": ["df['type_salary'] = df['salary'].apply(classify_salary)"]}, {"cell_type": "code", "execution_count": 27, "id": "87642498", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 2, 1, 3])"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["df['type_salary'].unique()"]}, {"cell_type": "code", "execution_count": 28, "id": "1110fed7", "metadata": {}, "outputs": [], "source": ["# <PERSON>ẩn hóa chữ thường và lọc các dòng KHÔNG chứa 'no description available'\n", "df = df[~df['description'].str.lower().str.contains('no description available', na=False)]"]}, {"cell_type": "code", "execution_count": 29, "id": "c50bce93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 841 entries, 0 to 989\n", "Data columns (total 17 columns):\n", " #   Column                Non-Null Count  Dtype \n", "---  ------                --------------  ----- \n", " 0   id                    841 non-null    object\n", " 1   title                 841 non-null    object\n", " 2   company               841 non-null    object\n", " 3   location              841 non-null    object\n", " 4   salary                841 non-null    object\n", " 5   description           841 non-null    object\n", " 6   requirements          841 non-null    object\n", " 7   skills                841 non-null    object\n", " 8   language              841 non-null    object\n", " 9   skills_en             841 non-null    object\n", " 10  description_en        841 non-null    object\n", " 11  requirements_en       841 non-null    object\n", " 12  description_cleaned   841 non-null    object\n", " 13  requirements_cleaned  841 non-null    object\n", " 14  skills_cleaned        841 non-null    object\n", " 15  location_group        841 non-null    int64 \n", " 16  type_salary           841 non-null    int64 \n", "dtypes: int64(2), object(15)\n", "memory usage: 118.3+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 30, "id": "8795ce96", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('IT Services and IT Consulting', 314), ('PHP', 237), ('Software Development Outsourcing', 210), ('Java', 209), ('OOP', 196), ('Software Products and Web Services', 166), ('Telecommunication', 164), ('CI/CD', 156), ('NodeJS', 150), ('Backend Developer', 149), ('HTML5', 142), ('CSS', 142), ('Tester', 115), ('DevOps', 113), ('Financial Services', 101), ('Cloud', 95), ('Fullstack', 88), ('JavaScript', 88), ('Lara<PERSON>', 88), ('Fullstack Developer', 88)]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5508\\1394890719.py:13: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n"]}, {"data": {"image/png": "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******************************************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****************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["skills_series = df['skills'].str.split(',').explode().str.strip()\n", "\n", "# Count the most common skills\n", "skill_counts = Counter(skills_series)\n", "top_skills = skill_counts.most_common(20)\n", "print(top_skills)\n", "\n", "# Convert to DataFrame for plotting\n", "skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])\n", "\n", "# Plot\n", "plt.figure(figsize=(12, 8))\n", "sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n", "plt.title('Top 20 Kỹ năng đư<PERSON><PERSON> yêu cầu nhiều nhất')\n", "plt.xlabel('<PERSON><PERSON> lượ<PERSON> công việc yêu cầu')\n", "plt.ylabel('K<PERSON> năng')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 31, "id": "6bc797ae", "metadata": {}, "outputs": [], "source": ["skills_to_remove = [\n", "    'IT Services and IT Consulting', 'Software Development Outsourcing', 'Software Products and Web Services', 'Telecommunication','Financial Services', 'Banking','Bridge System Engineer (BrSE)','Japanese IT Communication'\n", "]\n", "\n", "# Đ<PERSON><PERSON> về dạng chữ thường để so sánh không phân biệt hoa thường\n", "skills_to_remove = [s.lower() for s in skills_to_remove]\n"]}, {"cell_type": "code", "execution_count": 32, "id": "ac361167", "metadata": {}, "outputs": [], "source": ["def remove_unwanted_skills(skill_string):\n", "    if pd.isna(skill_string):\n", "        return skill_string\n", "    skills = [s.strip() for s in skill_string.split(',')]\n", "    filtered = [s for s in skills if s.lower() not in skills_to_remove]\n", "    return ', '.join(filtered)\n", "def extract_primary_skills(text):\n", "    # <PERSON><PERSON> lý trư<PERSON><PERSON> hợp text không phải chuỗi\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in primary_skills if skill in text.lower()]\n", "\n", "def extract_secondary_skills(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in secondary_skills if skill in text.lower()]\n", "\n", "def extract_adjectives(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp_en(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADJ']))\n", "\n", "def extract_adverbs(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp_en(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADV']))\n"]}, {"cell_type": "code", "execution_count": 33, "id": "9e5b5775", "metadata": {}, "outputs": [], "source": ["df['skills'] = df['skills'].apply(remove_unwanted_skills)"]}, {"cell_type": "code", "execution_count": 35, "id": "73094432", "metadata": {}, "outputs": [], "source": ["def read_skills(file_path):\n", "    skills = []\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            if line and not line.startswith('#'):\n", "                skills.append(line.lower())\n", "    return skills\n", "\n", "primary_skills = read_skills('../../data/primary_skills.txt')\n", "secondary_skills = read_skills('../../data/secondary_skills.txt')"]}, {"cell_type": "code", "execution_count": 36, "id": "bf660360", "metadata": {}, "outputs": [], "source": ["df['primary_skills'] = df['requirements_en'].apply(extract_primary_skills)\n", "df['secondary_skills'] = df['requirements_en'].apply(extract_secondary_skills)\n", "df['adjectives'] = df['requirements_en'].apply(extract_adjectives)\n", "df['adverbs'] = df['requirements_en'].apply(extract_adverbs)"]}, {"cell_type": "code", "execution_count": 37, "id": "cfddb74f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5508\\1403453381.py:12: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["skills_series = df['primary_skills'].explode().dropna().str.strip()\n", "\n", "# <PERSON><PERSON><PERSON> kỹ năng phổ biến nhất\n", "skill_counts = Counter(skills_series)\n", "top_skills = skill_counts.most_common(20)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> thành DataFrame để vẽ biểu đồ\n", "skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])\n", "\n", "# Vẽ biểu đồ\n", "plt.figure(figsize=(12, 8))\n", "sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n", "plt.title('Top 20 Kỹ năng đư<PERSON><PERSON> yêu cầu nhiều nhất')\n", "plt.xlabel('<PERSON><PERSON> lượ<PERSON> công việc yêu cầu')\n", "plt.ylabel('K<PERSON> năng')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 39, "id": "c36f9956", "metadata": {}, "outputs": [], "source": ["df.to_csv('../../data/clean/clean_jobs_v2.csv', index=False, encoding='utf-8')"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}