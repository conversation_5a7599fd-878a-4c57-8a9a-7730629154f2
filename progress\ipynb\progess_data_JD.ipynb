!pip install pandas langdetect deep_translator spacy underthesea nltk

!python -m spacy download en_core_web_md

!pip install matplotlib seaborn

# Import các thư viện cần thiết
import pandas as pd
from langdetect import detect
from deep_translator import GoogleTranslator
import re
import spacy
from underthesea import word_tokenize, pos_tag
from nltk.corpus import stopwords
import numpy as np
from collections import defaultdict
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
import seaborn as sns

nlp_en = spacy.load('en_core_web_md')
stop_words_en = None
stop_words_vi = None

# Đường dẫn đến file csv chứa các job description
csv_jd = "../../data/raw/itviec_jobs_undetected.csv"
stop_words_vn_txt = "../../docs/vietnamese-stopwords.txt"

df = pd.read_csv(csv_jd)

def detect_language(text):
    try:
        return detect(text)
    except:
        return 'unknown'
def translate_to_english(text, lang):
    try:
        if lang == 'en' or pd.isna(text) or text.strip() == "":
            return text
        return GoogleTranslator(source=lang, target='en').translate(text)
    except Exception as e:
        print(f"Error translating: {e} | text: {text}")
        return text
def clean_text(text, lang='en'):
    if not isinstance(text, str) or not text.strip():
        return ''
    text = text.lower()
    text = re.sub(r'http\S+|#\S+|@\S+|[^\w\s]|\n|\r|\t|\*|\•', ' ', text)
    text = re.sub(r'\s+', ' ', text).strip()
    if lang == 'en':
        doc = nlp_en(text)
        tokens = [token.lemma_ for token in doc if token.text not in stop_words_en]
        return ' '.join(tokens)
    else:
        tokens = word_tokenize(text)
        tokens = [t for t in tokens if t not in stop_words_vi]
        return ' '.join(tokens)
def clean_skills(text):
    text.lower()
    if pd.isna(text) or text.strip() == "":
        return []
    return [t.strip() for t in text.split(',') if t.strip()]

# Áp dụng cho cột 'description'
df['language'] = df['description'].apply(detect_language)
df['skills_en'] = df['skills']

df['description_en'] = df.apply(lambda x: translate_to_english(x['description'], x['language']), axis=1)
df['requirements_en'] = df.apply(lambda x: translate_to_english(x['requirements'], x['language']), axis=1)

with open(stop_words_vn_txt, 'r', encoding='utf-8') as f:
    stop_words_vi = set(word.strip() for word in f if word.strip())
stop_words_en = set(stopwords.words('english'))
print(stop_words_vi)
print(stop_words_en)

df['description_cleaned'] = df.apply(lambda x: clean_text(x['description_en'] if x['language'] == 'en' else x['description'], x['language']), axis=1)
df['requirements_cleaned'] = df.apply(lambda x: clean_text(x['requirements_en'] if x['language'] == 'en' else x['requirements'], x['language']), axis=1)
df['skills_cleaned'] = df['skills_en'].apply(lambda x: clean_skills(x))

df['location'].unique()

locations = np.array([
    'Ho Chi Minh', 'Ha Noi', 'Others', 'Ha Noi - Ho Chi Minh',
    'Ha Noi - Da Nang', 'Ho Chi Minh - Da Nang',
    'Ho Chi Minh - Ha Noi', 'Ho Chi Minh - Ha Noi - Da Nang',
    'Ha Noi - Ho Chi Minh - Da Nang', 'Ha Noi - Da Nang - Ho Chi Minh',
    'Da Nang', 'Ho Chi Minh - Da Nang - Ha Noi',
    'Da Nang - Ho Chi Minh', 'Ha Noi - Ho Chi Minh - Others',
    'Lam Dong - Ho Chi Minh', 'Thai Nguyen', 'Binh Duong',
    'Ho Chi Minh - Ha Noi - Others', 'Ho Chi Minh - Others',
    'Da Nang - Ha Noi - Ho Chi Minh'
])

main_cities = {'Ha Noi', 'Ho Chi Minh'}

def classify_location_group(loc_str):
    # Tách và làm sạch địa điểm
    cities = [c.strip() for c in loc_str.split('-')]
    cities_set = set(cities)

    # Giao giữa cities_set và main_cities
    common_cities = cities_set & main_cities

    if common_cities == {'Ha Noi'}:
        return 1
    elif common_cities == {'Ho Chi Minh'}:
        return 2
    else:
        return 3

df['location_group'] = df['location'].apply(lambda x: classify_location_group(x))
print(df['location_group'].value_counts())
print(df['location_group'].head(10))

pd.DataFrame(df)

for i in range(len(df)):
    df.at[i, 'id'] = "JOB_" + str(i)


df['salary'].unique()

df.drop(columns=['work_type'], inplace=True)

def extract_min_salary(s):
    if not isinstance(s, str):
        return 'agreement'

    s = s.lower()

    # Nhóm 5: lương thỏa thuận
    if any(keyword in s for keyword in ['not specified', 'negotiation', 'negotiable', 'thỏa thuận', "you'll love it", 'attractive']):
        return 'agreement'

    # Lương VNĐ có đơn vị triệu hoặc 'm'
    if 'm' in s or 'triệu' in s:
        match = re.findall(r'\d+', s)
        if match:
            usd = int(match[0]) * 1_000_000 / 24000  # đổi sang USD
            return usd

    # Lương USD
    match = re.findall(r'\d+(?:,\d+)?', s)
    if match:
        nums = [int(x.replace(',', '')) for x in match]
        return min(nums)

    return 'agreement'

def classify_salary(s):
    val = extract_min_salary(s)

    if val == 'agreement':
        return 5
    elif val < 500:
        return 1
    elif val < 2000:
        return 2
    elif val < 5000:
        return 3
    else:
        return 4

df['type_salary'] = df['salary'].apply(classify_salary)

df['type_salary'].unique()

# Chuẩn hóa chữ thường và lọc các dòng KHÔNG chứa 'no description available'
df = df[~df['description'].str.lower().str.contains('no description available', na=False)]

df.info()

skills_series = df['skills'].str.split(',').explode().str.strip()

# Count the most common skills
skill_counts = Counter(skills_series)
top_skills = skill_counts.most_common(20)
print(top_skills)

# Convert to DataFrame for plotting
skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])

# Plot
plt.figure(figsize=(12, 8))
sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')
plt.title('Top 20 Kỹ năng được yêu cầu nhiều nhất')
plt.xlabel('Số lượng công việc yêu cầu')
plt.ylabel('Kỹ năng')
plt.tight_layout()
plt.show()

skills_to_remove = [
    'IT Services and IT Consulting', 'Software Development Outsourcing', 'Software Products and Web Services', 'Telecommunication','Financial Services', 'Banking','Bridge System Engineer (BrSE)','Japanese IT Communication'
]

# Đưa về dạng chữ thường để so sánh không phân biệt hoa thường
skills_to_remove = [s.lower() for s in skills_to_remove]


def remove_unwanted_skills(skill_string):
    if pd.isna(skill_string):
        return skill_string
    skills = [s.strip() for s in skill_string.split(',')]
    filtered = [s for s in skills if s.lower() not in skills_to_remove]
    return ', '.join(filtered)
def extract_primary_skills(text):
    # Xử lý trường hợp text không phải chuỗi
    if not isinstance(text, str) or pd.isna(text):
        return []
    return [skill for skill in primary_skills if skill in text.lower()]

def extract_secondary_skills(text):
    if not isinstance(text, str) or pd.isna(text):
        return []
    return [skill for skill in secondary_skills if skill in text.lower()]

def extract_adjectives(text):
    if not isinstance(text, str) or pd.isna(text):
        return []
    doc = nlp_en(text)
    return list(set([token.text for token in doc if token.pos_ == 'ADJ']))

def extract_adverbs(text):
    if not isinstance(text, str) or pd.isna(text):
        return []
    doc = nlp_en(text)
    return list(set([token.text for token in doc if token.pos_ == 'ADV']))


df['skills'] = df['skills'].apply(remove_unwanted_skills)

def read_skills(file_path):
    skills = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#'):
                skills.append(line.lower())
    return skills

primary_skills = read_skills('../../data/primary_skills.txt')
secondary_skills = read_skills('../../data/secondary_skills.txt')

df['primary_skills'] = df['requirements_en'].apply(extract_primary_skills)
df['secondary_skills'] = df['requirements_en'].apply(extract_secondary_skills)
df['adjectives'] = df['requirements_en'].apply(extract_adjectives)
df['adverbs'] = df['requirements_en'].apply(extract_adverbs)

skills_series = df['primary_skills'].explode().dropna().str.strip()

# Đếm kỹ năng phổ biến nhất
skill_counts = Counter(skills_series)
top_skills = skill_counts.most_common(20)

# Chuyển thành DataFrame để vẽ biểu đồ
skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])

# Vẽ biểu đồ
plt.figure(figsize=(12, 8))
sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')
plt.title('Top 20 Kỹ năng được yêu cầu nhiều nhất')
plt.xlabel('Số lượng công việc yêu cầu')
plt.ylabel('Kỹ năng')
plt.tight_layout()
plt.show()

df.to_csv('../../data/clean/clean_jobs_v2.csv', index=False, encoding='utf-8')