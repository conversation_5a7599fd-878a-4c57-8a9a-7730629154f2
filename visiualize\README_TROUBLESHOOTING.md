# 🔧 Troubleshooting Four Categories Extraction

## 🚨 Common Error: KeyError 'adjective'

### Problem
```
KeyError: 'adjective'
```

### Root Cause
The error occurs when the `adjectives_df` DataFrame is empty (0 records), so it doesn't have the expected columns.

### Solutions

#### Option 1: Use Debug Notebook (Recommended)
```bash
cd visiualize
jupyter notebook debug_extraction.ipynb
```

**Features:**
- Tests extraction on small sample (10 resumes + 10 jobs)
- Better error handling
- Shows exactly what's being extracted
- Helps identify if the issue is with large dataset processing

#### Option 2: Use Simplified Notebook
```bash
cd visiualize
jupyter notebook extract_four_categories_simple.ipynb
```

**Features:**
- Batch processing to avoid memory issues
- Better error handling
- Limits text length to prevent processing issues
- More robust extraction functions

#### Option 3: Fix Original Notebook
If you want to continue with the original notebook, add this error handling:

```python
# Replace the analysis section with:
print("\n🎯 ADJECTIVES ANALYSIS:")
print(f"Total adjectives extracted: {len(adjectives_df):,}")

if len(adjectives_df) > 0 and 'adjective' in adjectives_df.columns:
    print(f"Unique adjectives: {adjectives_df['adjective'].nunique()}")
    # ... rest of analysis
else:
    print("⚠️ No adjectives extracted or missing columns")
```

---

## 🎯 Recommended Workflow

### Step 1: Debug First
```bash
jupyter notebook debug_extraction.ipynb
```
- Run this to test if extraction works on small sample
- Check if NLTK data is properly downloaded
- Verify text processing functions

### Step 2: Use Simplified Version
```bash
jupyter notebook extract_four_categories_simple.ipynb
```
- More robust for large datasets
- Better memory management
- Comprehensive error handling

### Step 3: Verify Results
```bash
ls ../data/primary_skills.csv
ls ../data/secondary_skills.csv  
ls ../data/adjectives.csv
ls ../data/adverbs.csv
```

---

## 🔍 Why This Happens

### Common Causes:
1. **NLTK Data Missing**: POS tagger not downloaded
2. **Memory Issues**: Large dataset processing
3. **Text Quality**: Empty or malformed text
4. **Processing Errors**: Exceptions during NLP processing

### Prevention:
- Use batch processing for large datasets
- Limit text length (first 2000 characters)
- Add comprehensive error handling
- Test on small samples first

---

## 📊 Expected Results

### Debug Notebook (Small Sample):
```
Primary Skills: 50-200 records
Secondary Skills: 30-150 records  
Adjectives: 100-500 records
Adverbs: 50-200 records
```

### Full Processing:
```
Primary Skills: 15,000+ records
Secondary Skills: 10,000+ records
Adjectives: 25,000+ records  
Adverbs: 8,000+ records
```

---

## 🚀 Quick Fix Commands

### If NLTK Error:
```python
import nltk
nltk.download('punkt')
nltk.download('averaged_perceptron_tagger')
nltk.download('stopwords')
```

### If Memory Error:
- Use `extract_four_categories_simple.ipynb`
- Reduce batch size in processing
- Process fewer records at a time

### If Empty Results:
- Check if clean data exists: `ls ../data/clean_*.csv`
- Verify text quality in clean data
- Run debug notebook first

---

## 📚 File Guide

| File | Purpose | When to Use |
|------|---------|-------------|
| `debug_extraction.ipynb` | Test & debug | First time, troubleshooting |
| `extract_four_categories_simple.ipynb` | Production extraction | Main processing |
| `extract_four_categories.ipynb` | Original (complex) | Advanced users only |

---

## ✅ Success Indicators

### Extraction Working:
- ✅ No KeyError exceptions
- ✅ All 4 CSV files created
- ✅ Reasonable record counts
- ✅ Summary JSON file generated

### Quality Check:
```python
# Load and verify
import pandas as pd
adj_df = pd.read_csv('../data/adjectives.csv')
print(f"Adjectives: {len(adj_df)} records")
print(f"Columns: {list(adj_df.columns)}")
print(f"Sample: {adj_df.head()}")
```

---

**🎯 Start with debug_extraction.ipynb, then use extract_four_categories_simple.ipynb for full processing!**
