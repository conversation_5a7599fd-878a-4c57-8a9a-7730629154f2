{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 🎯 Extract Four Categories (Simplified Version)\n", "## Primary Skills, Secondary Skills, Adjectives, Adverbs\n", "\n", "**Simplified version with better error handling and batch processing**"]}, {"cell_type": "code", "execution_count": 1, "id": "setup", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Setup completed\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import re\n", "from datetime import datetime\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# NLP libraries\n", "import nltk\n", "from nltk.tokenize import word_tokenize\n", "from nltk.tag import pos_tag\n", "from nltk.corpus import stopwords\n", "\n", "# Download NLTK data\n", "nltk_downloads = ['punkt', 'averaged_perceptron_tagger', 'stopwords']\n", "for item in nltk_downloads:\n", "    try:\n", "        if item == 'punkt':\n", "            nltk.data.find('tokenizers/punkt')\n", "        elif item == 'averaged_perceptron_tagger':\n", "            nltk.data.find('taggers/averaged_perceptron_tagger')\n", "        else:\n", "            nltk.data.find(f'corpora/{item}')\n", "    except LookupError:\n", "        print(f\"Downloading {item}...\")\n", "        nltk.download(item)\n", "\n", "print(\"✅ Setup completed\")"]}, {"cell_type": "code", "execution_count": 2, "id": "load-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Loaded 962 resumes and 995 jobs\n"]}], "source": ["# Load clean data\n", "try:\n", "    clean_resumes = pd.read_csv('../data/clean/clean_resumes.csv')\n", "    clean_jobs = pd.read_csv('../data/clean/clean_jobs.csv')\n", "    print(f\"✅ Loaded {len(clean_resumes):,} resumes and {len(clean_jobs):,} jobs\")\n", "except FileNotFoundError as e:\n", "    print(f\"❌ Error: {e}\")\n", "    print(\"Please run data_preprocessing.ipynb first\")"]}, {"cell_type": "code", "execution_count": 3, "id": "skill-definitions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Defined 41 primary skills and 27 secondary skills\n"]}], "source": ["# Define skill categories\n", "PRIMARY_SKILLS = [\n", "    # Programming Languages\n", "    'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift',\n", "    'kotlin', 'typescript', 'scala', 'r', 'matlab',\n", "    \n", "    # Frameworks\n", "    'react', 'angular', 'vue', 'nodejs', 'django', 'flask', 'spring', 'laravel',\n", "    'tensorflow', 'pytorch', 'keras',\n", "    \n", "    # Databases\n", "    'mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sql server', 'sqlite',\n", "    'elasticsearch',\n", "    \n", "    # Cloud & DevOps\n", "    'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'terraform'\n", "]\n", "\n", "SECONDARY_SKILLS = [\n", "    # Soft Skills\n", "    'leadership', 'communication', 'teamwork', 'problem solving', 'analytical thinking',\n", "    'creativity', 'adaptability', 'time management', 'project management',\n", "    \n", "    # Methodologies\n", "    'agile', 'scrum', 'kanban', 'devops', 'ci/cd', 'microservices', 'rest api',\n", "    \n", "    # Tools\n", "    'git', 'jira', 'confluence', 'figma', 'tableau', 'power bi', 'excel',\n", "    \n", "    # OS\n", "    'linux', 'windows', 'macos', 'ubuntu'\n", "]\n", "\n", "print(f\"✅ Defined {len(PRIMARY_SKILLS)} primary skills and {len(SECONDARY_SKILLS)} secondary skills\")"]}, {"cell_type": "code", "execution_count": 4, "id": "extraction-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Extraction functions defined\n"]}], "source": ["def extract_skills(text, skill_list, skill_type):\n", "    \"\"\"Extract skills from text\"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return []\n", "    \n", "    text_lower = str(text).lower()\n", "    found_skills = []\n", "    \n", "    for skill in skill_list:\n", "        if skill in text_lower:\n", "            found_skills.append({\n", "                'skill': skill,\n", "                'type': skill_type\n", "            })\n", "    \n", "    return found_skills\n", "\n", "def extract_adjectives_adverbs(text, max_items=10):\n", "    \"\"\"Extract adjectives and adverbs with error handling\"\"\"\n", "    if pd.isna(text) or text == '':\n", "        return {'adjectives': [], 'adverbs': []}\n", "    \n", "    try:\n", "        # Limit text length to avoid memory issues\n", "        text = str(text)[:2000]  # First 2000 characters\n", "        \n", "        tokens = word_tokenize(text.lower())\n", "        # Limit tokens to avoid processing issues\n", "        tokens = tokens[:200]  # First 200 tokens\n", "        \n", "        pos_tags = pos_tag(tokens)\n", "        \n", "        adjectives = []\n", "        adverbs = []\n", "        stop_words = set(stopwords.words('english'))\n", "        \n", "        for word, pos in pos_tags:\n", "            if word in stop_words or len(word) < 3 or not word.isalpha():\n", "                continue\n", "            \n", "            if pos.startswith('JJ') and len(adjectives) < max_items:\n", "                adjectives.append({\n", "                    'word': word,\n", "                    'pos': pos,\n", "                    'type': 'general'\n", "                })\n", "            elif pos.startswith('RB') and len(adverbs) < max_items:\n", "                adverbs.append({\n", "                    'word': word,\n", "                    'pos': pos,\n", "                    'type': 'general'\n", "                })\n", "        \n", "        return {'adjectives': adjectives, 'adverbs': adverbs}\n", "    \n", "    except Exception as e:\n", "        print(f\"Error processing text: {str(e)[:100]}...\")\n", "        return {'adjectives': [], 'adverbs': []}\n", "\n", "print(\"✅ Extraction functions defined\")"]}, {"cell_type": "code", "execution_count": 5, "id": "process-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔄 Processing data in batches...\n", "📄 Processing 962 resumes...\n", "   Processing batch 1/1\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "✅ Resume processing completed\n", "   Primary skills: 3,577\n", "   Secondary skills: 1,676\n", "   Adjectives: 0\n", "   Adverbs: 0\n"]}], "source": ["# Process data in batches\n", "print(\"🔄 Processing data in batches...\")\n", "\n", "batch_size = 1000\n", "all_primary_skills = []\n", "all_secondary_skills = []\n", "all_adjectives = []\n", "all_adverbs = []\n", "\n", "# Process resumes\n", "print(f\"📄 Processing {len(clean_resumes):,} resumes...\")\n", "for i in range(0, len(clean_resumes), batch_size):\n", "    batch = clean_resumes.iloc[i:i+batch_size]\n", "    print(f\"   Processing batch {i//batch_size + 1}/{(len(clean_resumes)-1)//batch_size + 1}\")\n", "    \n", "    for idx, row in batch.iterrows():\n", "        record_id = f\"resume_{idx}\"\n", "        \n", "        # Extract skills\n", "        primary_skills = extract_skills(row['clean_text'], PRIMARY_SKILLS, 'primary')\n", "        secondary_skills = extract_skills(row['clean_text'], SECONDARY_SKILLS, 'secondary')\n", "        \n", "        # Extract adjectives and adverbs\n", "        adj_adv = extract_adjectives_adverbs(row['clean_text'])\n", "        \n", "        # Add to collections\n", "        for skill in primary_skills:\n", "            all_primary_skills.append({\n", "                'record_id': record_id,\n", "                'source_type': 'resume',\n", "                'skill': skill['skill'],\n", "                'skill_type': skill['type'],\n", "                'category': row['category_clean']\n", "            })\n", "        \n", "        for skill in secondary_skills:\n", "            all_secondary_skills.append({\n", "                'record_id': record_id,\n", "                'source_type': 'resume',\n", "                'skill': skill['skill'],\n", "                'skill_type': skill['type'],\n", "                'category': row['category_clean']\n", "            })\n", "        \n", "        for adj in adj_adv['adjectives']:\n", "            all_adjectives.append({\n", "                'record_id': record_id,\n", "                'source_type': 'resume',\n", "                'adjective': adj['word'],\n", "                'pos_tag': adj['pos'],\n", "                'adj_type': adj['type'],\n", "                'category': row['category_clean']\n", "            })\n", "        \n", "        for adv in adj_adv['adverbs']:\n", "            all_adverbs.append({\n", "                'record_id': record_id,\n", "                'source_type': 'resume',\n", "                'adverb': adv['word'],\n", "                'pos_tag': adv['pos'],\n", "                'adv_type': adv['type'],\n", "                'category': row['category_clean']\n", "            })\n", "\n", "print(f\"✅ Resume processing completed\")\n", "print(f\"   Primary skills: {len(all_primary_skills):,}\")\n", "print(f\"   Secondary skills: {len(all_secondary_skills):,}\")\n", "print(f\"   Adjectives: {len(all_adjectives):,}\")\n", "print(f\"   Adverbs: {len(all_adverbs):,}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "process-jobs", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🏢 Processing 995 jobs...\n", "   Processing batch 1/2\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "   Processing batch 2/2\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "Error processing text: \n", "**********************************************************************\n", "  Resource \u001b[93mpunkt_tab\u001b[0...\n", "✅ Job processing completed\n", "\n", "📊 FINAL TOTALS:\n", "   Primary skills: 7,760\n", "   Secondary skills: 3,769\n", "   Adjectives: 0\n", "   Adverbs: 0\n"]}], "source": ["# Process jobs (smaller batch for jobs due to longer text)\n", "print(f\"\\n🏢 Processing {len(clean_jobs):,} jobs...\")\n", "job_batch_size = 500\n", "\n", "for i in range(0, len(clean_jobs), job_batch_size):\n", "    batch = clean_jobs.iloc[i:i+job_batch_size]\n", "    print(f\"   Processing batch {i//job_batch_size + 1}/{(len(clean_jobs)-1)//job_batch_size + 1}\")\n", "    \n", "    for idx, row in batch.iterrows():\n", "        record_id = f\"job_{row.get('id', idx)}\"\n", "        \n", "        # Extract skills\n", "        primary_skills = extract_skills(row['clean_text'], PRIMARY_SKILLS, 'primary')\n", "        secondary_skills = extract_skills(row['clean_text'], SECONDARY_SKILLS, 'secondary')\n", "        \n", "        # Extract adjectives and adverbs\n", "        adj_adv = extract_adjectives_adverbs(row['clean_text'])\n", "        \n", "        # Add to collections\n", "        for skill in primary_skills:\n", "            all_primary_skills.append({\n", "                'record_id': record_id,\n", "                'source_type': 'job',\n", "                'skill': skill['skill'],\n", "                'skill_type': skill['type'],\n", "                'job_title': row['title_clean'],\n", "                'company': row['company_clean']\n", "            })\n", "        \n", "        for skill in secondary_skills:\n", "            all_secondary_skills.append({\n", "                'record_id': record_id,\n", "                'source_type': 'job',\n", "                'skill': skill['skill'],\n", "                'skill_type': skill['type'],\n", "                'job_title': row['title_clean'],\n", "                'company': row['company_clean']\n", "            })\n", "        \n", "        for adj in adj_adv['adjectives']:\n", "            all_adjectives.append({\n", "                'record_id': record_id,\n", "                'source_type': 'job',\n", "                'adjective': adj['word'],\n", "                'pos_tag': adj['pos'],\n", "                'adj_type': adj['type'],\n", "                'job_title': row['title_clean'],\n", "                'company': row['company_clean']\n", "            })\n", "        \n", "        for adv in adj_adv['adverbs']:\n", "            all_adverbs.append({\n", "                'record_id': record_id,\n", "                'source_type': 'job',\n", "                'adverb': adv['word'],\n", "                'pos_tag': adv['pos'],\n", "                'adv_type': adv['type'],\n", "                'job_title': row['title_clean'],\n", "                'company': row['company_clean']\n", "            })\n", "\n", "print(f\"✅ Job processing completed\")\n", "print(f\"\\n📊 FINAL TOTALS:\")\n", "print(f\"   Primary skills: {len(all_primary_skills):,}\")\n", "print(f\"   Secondary skills: {len(all_secondary_skills):,}\")\n", "print(f\"   Adjectives: {len(all_adjectives):,}\")\n", "print(f\"   Adverbs: {len(all_adverbs):,}\")"]}, {"cell_type": "code", "execution_count": 7, "id": "create-dataframes", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Creating DataFrames...\n", "✅ DataFrames created:\n", "   Primary Skills DF: 7,760 records\n", "   Secondary Skills DF: 3,769 records\n", "   Adjectives DF: 0 records\n", "   Adverbs DF: 0 records\n", "\n", "📋 Primary Skills columns: ['record_id', 'source_type', 'skill', 'skill_type', 'category', 'job_title', 'company']\n"]}], "source": ["# Create DataFrames with error handling\n", "print(\"📊 Creating DataFrames...\")\n", "\n", "# Create DataFrames\n", "primary_skills_df = pd.DataFrame(all_primary_skills)\n", "secondary_skills_df = pd.DataFrame(all_secondary_skills)\n", "adjectives_df = pd.DataFrame(all_adjectives)\n", "adverbs_df = pd.DataFrame(all_adverbs)\n", "\n", "print(f\"✅ DataFrames created:\")\n", "print(f\"   Primary Skills DF: {len(primary_skills_df):,} records\")\n", "print(f\"   Secondary Skills DF: {len(secondary_skills_df):,} records\")\n", "print(f\"   Adjectives DF: {len(adjectives_df):,} records\")\n", "print(f\"   Adverbs DF: {len(adverbs_df):,} records\")\n", "\n", "# Show column structure\n", "if len(primary_skills_df) > 0:\n", "    print(f\"\\n📋 Primary Skills columns: {list(primary_skills_df.columns)}\")\n", "if len(adjectives_df) > 0:\n", "    print(f\"📋 Adjectives columns: {list(adjectives_df.columns)}\")\n", "if len(adverbs_df) > 0:\n", "    print(f\"📋 Adverbs columns: {list(adverbs_df.columns)}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 Performing analysis...\n", "\n", "🎯 PRIMARY SKILLS ANALYSIS:\n", "Total primary skills: 7,760\n", "Unique skills: 39\n", "From resumes: 3,577\n", "From jobs: 4,183\n", "\n", "🔥 Top 10 Primary Skills:\n", "  • r: 1,957\n", "  • go: 993\n", "  • java: 756\n", "  • python: 379\n", "  • javascript: 323\n", "  • aws: 321\n", "  • php: 312\n", "  • scala: 276\n", "  • mysql: 250\n", "  • nodejs: 210\n", "\n", "🎯 SECONDARY SKILLS ANALYSIS:\n", "Total secondary skills: 3,769\n", "Unique skills: 25\n", "\n", "🔥 Top 10 Secondary Skills:\n", "  • communication: 695\n", "  • windows: 380\n", "  • agile: 373\n", "  • excel: 320\n", "  • git: 306\n", "  • scrum: 233\n", "  • linux: 228\n", "  • devops: 197\n", "  • problem solving: 159\n", "  • jira: 153\n", "\n", "🎯 ADJECTIVES ANALYSIS:\n", "No adjectives extracted or missing columns\n", "\n", "🎯 ADVERBS ANALYSIS:\n", "No adverbs extracted or missing columns\n", "\n", "✅ Analysis completed\n"]}], "source": ["# Analysis with proper error handling\n", "print(\"📈 Performing analysis...\")\n", "\n", "# Primary Skills Analysis\n", "print(\"\\n🎯 PRIMARY SKILLS ANALYSIS:\")\n", "if len(primary_skills_df) > 0:\n", "    print(f\"Total primary skills: {len(primary_skills_df):,}\")\n", "    print(f\"Unique skills: {primary_skills_df['skill'].nunique()}\")\n", "    print(f\"From resumes: {len(primary_skills_df[primary_skills_df['source_type'] == 'resume']):,}\")\n", "    print(f\"From jobs: {len(primary_skills_df[primary_skills_df['source_type'] == 'job']):,}\")\n", "    \n", "    print(\"\\n🔥 Top 10 Primary Skills:\")\n", "    top_primary = primary_skills_df['skill'].value_counts().head(10)\n", "    for skill, count in top_primary.items():\n", "        print(f\"  • {skill}: {count:,}\")\n", "else:\n", "    print(\"No primary skills extracted\")\n", "\n", "# Secondary Skills Analysis\n", "print(\"\\n🎯 SECONDARY SKILLS ANALYSIS:\")\n", "if len(secondary_skills_df) > 0:\n", "    print(f\"Total secondary skills: {len(secondary_skills_df):,}\")\n", "    print(f\"Unique skills: {secondary_skills_df['skill'].nunique()}\")\n", "    \n", "    print(\"\\n🔥 Top 10 Secondary Skills:\")\n", "    top_secondary = secondary_skills_df['skill'].value_counts().head(10)\n", "    for skill, count in top_secondary.items():\n", "        print(f\"  • {skill}: {count:,}\")\n", "else:\n", "    print(\"No secondary skills extracted\")\n", "\n", "# Adjectives Analysis\n", "print(\"\\n🎯 ADJECTIVES ANALYSIS:\")\n", "if len(adjectives_df) > 0 and 'adjective' in adjectives_df.columns:\n", "    print(f\"Total adjectives: {len(adjectives_df):,}\")\n", "    print(f\"Unique adjectives: {adjectives_df['adjective'].nunique()}\")\n", "    \n", "    print(\"\\n🔥 Top 10 Adjectives:\")\n", "    top_adj = adjectives_df['adjective'].value_counts().head(10)\n", "    for adj, count in top_adj.items():\n", "        print(f\"  • {adj}: {count:,}\")\n", "else:\n", "    print(\"No adjectives extracted or missing columns\")\n", "\n", "# Adverbs Analysis\n", "print(\"\\n🎯 ADVERBS ANALYSIS:\")\n", "if len(adverbs_df) > 0 and 'adverb' in adverbs_df.columns:\n", "    print(f\"Total adverbs: {len(adverbs_df):,}\")\n", "    print(f\"Unique adverbs: {adverbs_df['adverb'].nunique()}\")\n", "    \n", "    print(\"\\n🔥 Top 10 Adverbs:\")\n", "    top_adv = adverbs_df['adverb'].value_counts().head(10)\n", "    for adv, count in top_adv.items():\n", "        print(f\"  • {adv}: {count:,}\")\n", "else:\n", "    print(\"No adverbs extracted or missing columns\")\n", "\n", "print(\"\\n✅ Analysis completed\")"]}, {"cell_type": "code", "execution_count": 9, "id": "export", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["💾 Exporting results...\n", "✅ Exported 7,760 primary skills\n", "✅ Exported 3,769 secondary skills\n", "✅ Summary saved to: data/four_categories_summary.json\n", "\n", "🎉 FOUR CATEGORIES EXTRACTION COMPLETED!\n", "==================================================\n", "📊 Results:\n", "   • Primary Skills: 7,760 records\n", "   • Secondary Skills: 3,769 records\n", "   • Adjectives: 0 records\n", "   • Adverbs: 0 records\n", "\n", "🚀 Files saved to data/ folder - ready for analysis!\n"]}], "source": ["# Export results\n", "print(\"💾 Exporting results...\")\n", "\n", "processing_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "\n", "# Add processing date to all DataFrames\n", "for df in [primary_skills_df, secondary_skills_df, adjectives_df, adverbs_df]:\n", "    if len(df) > 0:\n", "        df['extracted_date'] = processing_date\n", "\n", "# Export to CSV\n", "if len(primary_skills_df) > 0:\n", "    primary_skills_df.to_csv('../data/primary_skills.csv', index=False, encoding='utf-8')\n", "    print(f\"✅ Exported {len(primary_skills_df):,} primary skills\")\n", "\n", "if len(secondary_skills_df) > 0:\n", "    secondary_skills_df.to_csv('../data/secondary_skills.csv', index=False, encoding='utf-8')\n", "    print(f\"✅ Exported {len(secondary_skills_df):,} secondary skills\")\n", "\n", "if len(adjectives_df) > 0:\n", "    adjectives_df.to_csv('../data/adjectives.csv', index=False, encoding='utf-8')\n", "    print(f\"✅ Exported {len(adjectives_df):,} adjectives\")\n", "\n", "if len(adverbs_df) > 0:\n", "    adverbs_df.to_csv('../data/adverbs.csv', index=False, encoding='utf-8')\n", "    print(f\"✅ Exported {len(adverbs_df):,} adverbs\")\n", "\n", "# Create summary\n", "summary = {\n", "    'processing_date': processing_date,\n", "    'total_records_processed': len(clean_resumes) + len(clean_jobs),\n", "    'results': {\n", "        'primary_skills': len(primary_skills_df),\n", "        'secondary_skills': len(secondary_skills_df),\n", "        'adjectives': len(adjectives_df),\n", "        'adverbs': len(adverbs_df)\n", "    }\n", "}\n", "\n", "import json\n", "with open('../data/four_categories_summary.json', 'w', encoding='utf-8') as f:\n", "    json.dump(summary, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"✅ Summary saved to: data/four_categories_summary.json\")\n", "\n", "print(\"\\n🎉 FOUR CATEGORIES EXTRACTION COMPLETED!\")\n", "print(\"=\"*50)\n", "print(f\"📊 Results:\")\n", "print(f\"   • Primary Skills: {len(primary_skills_df):,} records\")\n", "print(f\"   • Secondary Skills: {len(secondary_skills_df):,} records\")\n", "print(f\"   • Adjectives: {len(adjectives_df):,} records\")\n", "print(f\"   • Adverbs: {len(adverbs_df):,} records\")\n", "print(f\"\\n🚀 Files saved to data/ folder - ready for analysis!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}