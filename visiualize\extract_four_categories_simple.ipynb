import pandas as pd
import numpy as np
import re
from datetime import datetime
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# NLP libraries
import nltk
from nltk.tokenize import word_tokenize
from nltk.tag import pos_tag
from nltk.corpus import stopwords

# Download NLTK data
nltk_downloads = ['punkt', 'averaged_perceptron_tagger', 'stopwords']
for item in nltk_downloads:
    try:
        if item == 'punkt':
            nltk.data.find('tokenizers/punkt')
        elif item == 'averaged_perceptron_tagger':
            nltk.data.find('taggers/averaged_perceptron_tagger')
        else:
            nltk.data.find(f'corpora/{item}')
    except LookupError:
        print(f"Downloading {item}...")
        nltk.download(item)

print("✅ Setup completed")

# Load clean data
try:
    clean_resumes = pd.read_csv('../data/clean/clean_resumes.csv')
    clean_jobs = pd.read_csv('../data/clean/clean_jobs.csv')
    print(f"✅ Loaded {len(clean_resumes):,} resumes and {len(clean_jobs):,} jobs")
except FileNotFoundError as e:
    print(f"❌ Error: {e}")
    print("Please run data_preprocessing.ipynb first")

# Define skill categories
PRIMARY_SKILLS = [
    # Programming Languages
    'python', 'java', 'javascript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift',
    'kotlin', 'typescript', 'scala', 'r', 'matlab',
    
    # Frameworks
    'react', 'angular', 'vue', 'nodejs', 'django', 'flask', 'spring', 'laravel',
    'tensorflow', 'pytorch', 'keras',
    
    # Databases
    'mysql', 'postgresql', 'mongodb', 'redis', 'oracle', 'sql server', 'sqlite',
    'elasticsearch',
    
    # Cloud & DevOps
    'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'terraform'
]

SECONDARY_SKILLS = [
    # Soft Skills
    'leadership', 'communication', 'teamwork', 'problem solving', 'analytical thinking',
    'creativity', 'adaptability', 'time management', 'project management',
    
    # Methodologies
    'agile', 'scrum', 'kanban', 'devops', 'ci/cd', 'microservices', 'rest api',
    
    # Tools
    'git', 'jira', 'confluence', 'figma', 'tableau', 'power bi', 'excel',
    
    # OS
    'linux', 'windows', 'macos', 'ubuntu'
]

print(f"✅ Defined {len(PRIMARY_SKILLS)} primary skills and {len(SECONDARY_SKILLS)} secondary skills")

def extract_skills(text, skill_list, skill_type):
    """Extract skills from text"""
    if pd.isna(text) or text == '':
        return []
    
    text_lower = str(text).lower()
    found_skills = []
    
    for skill in skill_list:
        if skill in text_lower:
            found_skills.append({
                'skill': skill,
                'type': skill_type
            })
    
    return found_skills

def extract_adjectives_adverbs(text, max_items=10):
    """Extract adjectives and adverbs with error handling"""
    if pd.isna(text) or text == '':
        return {'adjectives': [], 'adverbs': []}
    
    try:
        # Limit text length to avoid memory issues
        text = str(text)[:2000]  # First 2000 characters
        
        tokens = word_tokenize(text.lower())
        # Limit tokens to avoid processing issues
        tokens = tokens[:200]  # First 200 tokens
        
        pos_tags = pos_tag(tokens)
        
        adjectives = []
        adverbs = []
        stop_words = set(stopwords.words('english'))
        
        for word, pos in pos_tags:
            if word in stop_words or len(word) < 3 or not word.isalpha():
                continue
            
            if pos.startswith('JJ') and len(adjectives) < max_items:
                adjectives.append({
                    'word': word,
                    'pos': pos,
                    'type': 'general'
                })
            elif pos.startswith('RB') and len(adverbs) < max_items:
                adverbs.append({
                    'word': word,
                    'pos': pos,
                    'type': 'general'
                })
        
        return {'adjectives': adjectives, 'adverbs': adverbs}
    
    except Exception as e:
        print(f"Error processing text: {str(e)[:100]}...")
        return {'adjectives': [], 'adverbs': []}

print("✅ Extraction functions defined")

# Process data in batches
print("🔄 Processing data in batches...")

batch_size = 1000
all_primary_skills = []
all_secondary_skills = []
all_adjectives = []
all_adverbs = []

# Process resumes
print(f"📄 Processing {len(clean_resumes):,} resumes...")
for i in range(0, len(clean_resumes), batch_size):
    batch = clean_resumes.iloc[i:i+batch_size]
    print(f"   Processing batch {i//batch_size + 1}/{(len(clean_resumes)-1)//batch_size + 1}")
    
    for idx, row in batch.iterrows():
        record_id = f"resume_{idx}"
        
        # Extract skills
        primary_skills = extract_skills(row['clean_text'], PRIMARY_SKILLS, 'primary')
        secondary_skills = extract_skills(row['clean_text'], SECONDARY_SKILLS, 'secondary')
        
        # Extract adjectives and adverbs
        adj_adv = extract_adjectives_adverbs(row['clean_text'])
        
        # Add to collections
        for skill in primary_skills:
            all_primary_skills.append({
                'record_id': record_id,
                'source_type': 'resume',
                'skill': skill['skill'],
                'skill_type': skill['type'],
                'category': row['category_clean']
            })
        
        for skill in secondary_skills:
            all_secondary_skills.append({
                'record_id': record_id,
                'source_type': 'resume',
                'skill': skill['skill'],
                'skill_type': skill['type'],
                'category': row['category_clean']
            })
        
        for adj in adj_adv['adjectives']:
            all_adjectives.append({
                'record_id': record_id,
                'source_type': 'resume',
                'adjective': adj['word'],
                'pos_tag': adj['pos'],
                'adj_type': adj['type'],
                'category': row['category_clean']
            })
        
        for adv in adj_adv['adverbs']:
            all_adverbs.append({
                'record_id': record_id,
                'source_type': 'resume',
                'adverb': adv['word'],
                'pos_tag': adv['pos'],
                'adv_type': adv['type'],
                'category': row['category_clean']
            })

print(f"✅ Resume processing completed")
print(f"   Primary skills: {len(all_primary_skills):,}")
print(f"   Secondary skills: {len(all_secondary_skills):,}")
print(f"   Adjectives: {len(all_adjectives):,}")
print(f"   Adverbs: {len(all_adverbs):,}")

# Process jobs (smaller batch for jobs due to longer text)
print(f"\n🏢 Processing {len(clean_jobs):,} jobs...")
job_batch_size = 500

for i in range(0, len(clean_jobs), job_batch_size):
    batch = clean_jobs.iloc[i:i+job_batch_size]
    print(f"   Processing batch {i//job_batch_size + 1}/{(len(clean_jobs)-1)//job_batch_size + 1}")
    
    for idx, row in batch.iterrows():
        record_id = f"job_{row.get('id', idx)}"
        
        # Extract skills
        primary_skills = extract_skills(row['clean_text'], PRIMARY_SKILLS, 'primary')
        secondary_skills = extract_skills(row['clean_text'], SECONDARY_SKILLS, 'secondary')
        
        # Extract adjectives and adverbs
        adj_adv = extract_adjectives_adverbs(row['clean_text'])
        
        # Add to collections
        for skill in primary_skills:
            all_primary_skills.append({
                'record_id': record_id,
                'source_type': 'job',
                'skill': skill['skill'],
                'skill_type': skill['type'],
                'job_title': row['title_clean'],
                'company': row['company_clean']
            })
        
        for skill in secondary_skills:
            all_secondary_skills.append({
                'record_id': record_id,
                'source_type': 'job',
                'skill': skill['skill'],
                'skill_type': skill['type'],
                'job_title': row['title_clean'],
                'company': row['company_clean']
            })
        
        for adj in adj_adv['adjectives']:
            all_adjectives.append({
                'record_id': record_id,
                'source_type': 'job',
                'adjective': adj['word'],
                'pos_tag': adj['pos'],
                'adj_type': adj['type'],
                'job_title': row['title_clean'],
                'company': row['company_clean']
            })
        
        for adv in adj_adv['adverbs']:
            all_adverbs.append({
                'record_id': record_id,
                'source_type': 'job',
                'adverb': adv['word'],
                'pos_tag': adv['pos'],
                'adv_type': adv['type'],
                'job_title': row['title_clean'],
                'company': row['company_clean']
            })

print(f"✅ Job processing completed")
print(f"\n📊 FINAL TOTALS:")
print(f"   Primary skills: {len(all_primary_skills):,}")
print(f"   Secondary skills: {len(all_secondary_skills):,}")
print(f"   Adjectives: {len(all_adjectives):,}")
print(f"   Adverbs: {len(all_adverbs):,}")

# Create DataFrames with error handling
print("📊 Creating DataFrames...")

# Create DataFrames
primary_skills_df = pd.DataFrame(all_primary_skills)
secondary_skills_df = pd.DataFrame(all_secondary_skills)
adjectives_df = pd.DataFrame(all_adjectives)
adverbs_df = pd.DataFrame(all_adverbs)

print(f"✅ DataFrames created:")
print(f"   Primary Skills DF: {len(primary_skills_df):,} records")
print(f"   Secondary Skills DF: {len(secondary_skills_df):,} records")
print(f"   Adjectives DF: {len(adjectives_df):,} records")
print(f"   Adverbs DF: {len(adverbs_df):,} records")

# Show column structure
if len(primary_skills_df) > 0:
    print(f"\n📋 Primary Skills columns: {list(primary_skills_df.columns)}")
if len(adjectives_df) > 0:
    print(f"📋 Adjectives columns: {list(adjectives_df.columns)}")
if len(adverbs_df) > 0:
    print(f"📋 Adverbs columns: {list(adverbs_df.columns)}")

# Analysis with proper error handling
print("📈 Performing analysis...")

# Primary Skills Analysis
print("\n🎯 PRIMARY SKILLS ANALYSIS:")
if len(primary_skills_df) > 0:
    print(f"Total primary skills: {len(primary_skills_df):,}")
    print(f"Unique skills: {primary_skills_df['skill'].nunique()}")
    print(f"From resumes: {len(primary_skills_df[primary_skills_df['source_type'] == 'resume']):,}")
    print(f"From jobs: {len(primary_skills_df[primary_skills_df['source_type'] == 'job']):,}")
    
    print("\n🔥 Top 10 Primary Skills:")
    top_primary = primary_skills_df['skill'].value_counts().head(10)
    for skill, count in top_primary.items():
        print(f"  • {skill}: {count:,}")
else:
    print("No primary skills extracted")

# Secondary Skills Analysis
print("\n🎯 SECONDARY SKILLS ANALYSIS:")
if len(secondary_skills_df) > 0:
    print(f"Total secondary skills: {len(secondary_skills_df):,}")
    print(f"Unique skills: {secondary_skills_df['skill'].nunique()}")
    
    print("\n🔥 Top 10 Secondary Skills:")
    top_secondary = secondary_skills_df['skill'].value_counts().head(10)
    for skill, count in top_secondary.items():
        print(f"  • {skill}: {count:,}")
else:
    print("No secondary skills extracted")

# Adjectives Analysis
print("\n🎯 ADJECTIVES ANALYSIS:")
if len(adjectives_df) > 0 and 'adjective' in adjectives_df.columns:
    print(f"Total adjectives: {len(adjectives_df):,}")
    print(f"Unique adjectives: {adjectives_df['adjective'].nunique()}")
    
    print("\n🔥 Top 10 Adjectives:")
    top_adj = adjectives_df['adjective'].value_counts().head(10)
    for adj, count in top_adj.items():
        print(f"  • {adj}: {count:,}")
else:
    print("No adjectives extracted or missing columns")

# Adverbs Analysis
print("\n🎯 ADVERBS ANALYSIS:")
if len(adverbs_df) > 0 and 'adverb' in adverbs_df.columns:
    print(f"Total adverbs: {len(adverbs_df):,}")
    print(f"Unique adverbs: {adverbs_df['adverb'].nunique()}")
    
    print("\n🔥 Top 10 Adverbs:")
    top_adv = adverbs_df['adverb'].value_counts().head(10)
    for adv, count in top_adv.items():
        print(f"  • {adv}: {count:,}")
else:
    print("No adverbs extracted or missing columns")

print("\n✅ Analysis completed")

# Export results
print("💾 Exporting results...")

processing_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# Add processing date to all DataFrames
for df in [primary_skills_df, secondary_skills_df, adjectives_df, adverbs_df]:
    if len(df) > 0:
        df['extracted_date'] = processing_date

# Export to CSV
if len(primary_skills_df) > 0:
    primary_skills_df.to_csv('../data/primary_skills.csv', index=False, encoding='utf-8')
    print(f"✅ Exported {len(primary_skills_df):,} primary skills")

if len(secondary_skills_df) > 0:
    secondary_skills_df.to_csv('../data/secondary_skills.csv', index=False, encoding='utf-8')
    print(f"✅ Exported {len(secondary_skills_df):,} secondary skills")

if len(adjectives_df) > 0:
    adjectives_df.to_csv('../data/adjectives.csv', index=False, encoding='utf-8')
    print(f"✅ Exported {len(adjectives_df):,} adjectives")

if len(adverbs_df) > 0:
    adverbs_df.to_csv('../data/adverbs.csv', index=False, encoding='utf-8')
    print(f"✅ Exported {len(adverbs_df):,} adverbs")

# Create summary
summary = {
    'processing_date': processing_date,
    'total_records_processed': len(clean_resumes) + len(clean_jobs),
    'results': {
        'primary_skills': len(primary_skills_df),
        'secondary_skills': len(secondary_skills_df),
        'adjectives': len(adjectives_df),
        'adverbs': len(adverbs_df)
    }
}

import json
with open('../data/four_categories_summary.json', 'w', encoding='utf-8') as f:
    json.dump(summary, f, indent=2, ensure_ascii=False)

print(f"✅ Summary saved to: data/four_categories_summary.json")

print("\n🎉 FOUR CATEGORIES EXTRACTION COMPLETED!")
print("="*50)
print(f"📊 Results:")
print(f"   • Primary Skills: {len(primary_skills_df):,} records")
print(f"   • Secondary Skills: {len(secondary_skills_df):,} records")
print(f"   • Adjectives: {len(adjectives_df):,} records")
print(f"   • Adverbs: {len(adverbs_df):,} records")
print(f"\n🚀 Files saved to data/ folder - ready for analysis!")