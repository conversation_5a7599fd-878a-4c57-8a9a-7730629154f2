#!/usr/bin/env python3
"""
HR Demo Setup
Tạo demo data để HR có thể test ngay hệ thống
"""

import os
import json
from datetime import datetime
from pathlib import Path

def create_demo_folders():
    """Tạo các folder cần thiết cho demo"""
    folders = [
        "job_requirements",
        "candidate_uploads", 
        "hr_results"
    ]
    
    for folder in folders:
        os.makedirs(folder, exist_ok=True)
        print(f"✅ Created folder: {folder}")

def create_sample_job_requirements():
    """Tạo sample job requirements"""
    
    # Job 1: Python Developer
    python_job = {
        "job_info": {
            "job_id": "JOB_2024_PYTHON_001",
            "job_title": "Senior Python Developer",
            "department": "Engineering",
            "location": "Ho Chi Minh City",
            "employment_type": "Full-time",
            "salary_range": "25-35M VND",
            "posting_date": datetime.now().strftime("%Y-%m-%d")
        },
        "requirements": {
            "required_skills": [
                "Python", "Django", "PostgreSQL", "REST API", "Git"
            ],
            "preferred_skills": [
                "React", "Docker", "AWS", "Redis", "CI/CD"
            ],
            "required_experience": 3,
            "required_education": 2,
            "language_requirements": ["English - Intermediate"],
            "certifications": ["AWS Certified Developer (preferred)"]
        },
        "job_description": """
We are looking for a Senior Python Developer to join our engineering team.
You will be responsible for developing scalable web applications using Django,
working with PostgreSQL databases, and building REST APIs.

Key Responsibilities:
- Develop and maintain Python/Django applications
- Design and optimize database schemas
- Build and maintain REST APIs
- Collaborate with frontend developers
- Participate in code reviews and technical discussions
- Mentor junior developers

Requirements:
- 3+ years of Python development experience
- Strong knowledge of Django framework
- Experience with PostgreSQL and database optimization
- Familiarity with REST API design
- Good English communication skills
        """,
        "company_culture": {
            "work_style": "Hybrid",
            "team_size": "10-15 people",
            "company_stage": "Scale-up",
            "benefits": ["Health insurance", "13th month salary", "Flexible hours", "Learning budget"]
        },
        "screening_preferences": {
            "minimum_confidence": 0.7,
            "max_candidates_to_review": 15,
            "priority_factors": ["skill_similarity", "experience_match"],
            "exclude_overqualified": False
        }
    }
    
    # Job 2: Data Scientist
    data_job = {
        "job_info": {
            "job_id": "JOB_2024_DATA_002",
            "job_title": "Data Scientist",
            "department": "Data & Analytics",
            "location": "Hanoi",
            "employment_type": "Full-time",
            "salary_range": "30-45M VND",
            "posting_date": datetime.now().strftime("%Y-%m-%d")
        },
        "requirements": {
            "required_skills": [
                "Python", "Machine Learning", "SQL", "Pandas", "Scikit-learn"
            ],
            "preferred_skills": [
                "TensorFlow", "PyTorch", "Tableau", "AWS", "Spark"
            ],
            "required_experience": 2,
            "required_education": 3,
            "language_requirements": ["English - Advanced"],
            "certifications": ["Google Cloud ML Engineer (preferred)"]
        },
        "job_description": """
We are seeking a Data Scientist to join our analytics team.
You will work on machine learning projects, data analysis, and building predictive models.

Key Responsibilities:
- Develop machine learning models and algorithms
- Analyze large datasets to extract business insights
- Build data pipelines and ETL processes
- Create data visualizations and reports
- Collaborate with business stakeholders
- Present findings to management

Requirements:
- 2+ years of data science experience
- Strong Python and SQL skills
- Experience with machine learning libraries
- Knowledge of statistical analysis
- Advanced English for international collaboration
        """,
        "company_culture": {
            "work_style": "Remote-friendly",
            "team_size": "5-8 people",
            "company_stage": "Startup",
            "benefits": ["Stock options", "Health insurance", "Remote work", "Conference budget"]
        },
        "screening_preferences": {
            "minimum_confidence": 0.75,
            "max_candidates_to_review": 10,
            "priority_factors": ["skill_similarity", "education_match"],
            "exclude_overqualified": False
        }
    }
    
    # Save job requirements
    with open("job_requirements/python_developer_job.json", 'w', encoding='utf-8') as f:
        json.dump(python_job, f, indent=2, ensure_ascii=False)
    
    with open("job_requirements/data_scientist_job.json", 'w', encoding='utf-8') as f:
        json.dump(data_job, f, indent=2, ensure_ascii=False)
    
    print("✅ Created sample job requirements:")
    print("   📁 job_requirements/python_developer_job.json")
    print("   📁 job_requirements/data_scientist_job.json")

def create_sample_cv_files():
    """Tạo sample CV files để demo"""
    
    cv_samples = [
        # Python Developers
        {
            "filename": "nguyen_van_a_python_django_senior.txt",
            "content": """
NGUYEN VAN A
Senior Python Developer
Email: <EMAIL>
Phone: +84 901 234 567
Location: Ho Chi Minh City

EXPERIENCE:
- 4 years Python development
- 3 years Django framework
- 2 years PostgreSQL database
- REST API development
- Git version control

SKILLS:
Python, Django, PostgreSQL, REST API, Git, Docker, AWS, Redis

EDUCATION:
Bachelor of Computer Science - HCMUT (2019)

PROJECTS:
- E-commerce platform using Django and PostgreSQL
- REST API for mobile application
- Microservices with Docker deployment
            """
        },
        {
            "filename": "tran_thi_b_python_flask_developer.txt", 
            "content": """
TRAN THI B
Python Developer
Email: <EMAIL>
Phone: +84 902 345 678
Location: Ho Chi Minh City

EXPERIENCE:
- 3 years Python development
- 2 years Flask framework
- 1 year Django experience
- MySQL and PostgreSQL
- AWS deployment

SKILLS:
Python, Flask, Django, MySQL, PostgreSQL, AWS, Docker, Git

EDUCATION:
Bachelor of Information Technology - UIT (2020)

PROJECTS:
- Web application with Flask and MySQL
- Data processing pipeline with Python
- AWS Lambda functions
            """
        },
        {
            "filename": "le_van_c_fullstack_javascript.txt",
            "content": """
LE VAN C
Full-stack Developer
Email: <EMAIL>
Phone: +84 903 456 789
Location: Ho Chi Minh City

EXPERIENCE:
- 2 years JavaScript development
- 1 year React frontend
- 1 year Node.js backend
- MongoDB database
- Basic Python knowledge

SKILLS:
JavaScript, React, Node.js, MongoDB, HTML, CSS, Git, Python

EDUCATION:
Bachelor of Computer Science - HCMUS (2021)

PROJECTS:
- React web application with Node.js backend
- E-commerce site with MongoDB
- RESTful API development
            """
        },
        # Data Scientists
        {
            "filename": "pham_thi_d_data_scientist_ml.txt",
            "content": """
PHAM THI D
Data Scientist
Email: <EMAIL>
Phone: +84 904 567 890
Location: Hanoi

EXPERIENCE:
- 3 years data science experience
- 2 years machine learning projects
- Python and R programming
- SQL database analysis
- Statistical modeling

SKILLS:
Python, Machine Learning, SQL, Pandas, Scikit-learn, TensorFlow, R, Tableau

EDUCATION:
Master of Data Science - VNU (2020)
Bachelor of Mathematics - VNU (2018)

PROJECTS:
- Customer churn prediction model
- Sales forecasting with time series
- Recommendation system using collaborative filtering
            """
        },
        {
            "filename": "hoang_van_e_python_analyst.txt",
            "content": """
HOANG VAN E
Data Analyst
Email: <EMAIL>
Phone: +84 905 678 901
Location: Hanoi

EXPERIENCE:
- 2 years data analysis
- 1 year Python programming
- SQL query optimization
- Excel and PowerBI
- Basic machine learning

SKILLS:
Python, SQL, Pandas, Excel, PowerBI, Machine Learning, Statistics

EDUCATION:
Bachelor of Economics - NEU (2021)

PROJECTS:
- Sales dashboard with PowerBI
- Customer segmentation analysis
- Market research data analysis
            """
        },
        # Other profiles
        {
            "filename": "vo_thi_f_java_developer.txt",
            "content": """
VO THI F
Java Developer
Email: <EMAIL>
Phone: +84 906 789 012
Location: Da Nang

EXPERIENCE:
- 5 years Java development
- 3 years Spring framework
- 2 years microservices
- Oracle and MySQL
- Maven and Gradle

SKILLS:
Java, Spring, Hibernate, Oracle, MySQL, Maven, Gradle, Git

EDUCATION:
Bachelor of Software Engineering - DUT (2018)

PROJECTS:
- Enterprise application with Spring Boot
- Microservices architecture
- RESTful web services
            """
        },
        {
            "filename": "dao_van_g_devops_engineer.txt",
            "content": """
DAO VAN G
DevOps Engineer
Email: <EMAIL>
Phone: +84 907 890 123
Location: Ho Chi Minh City

EXPERIENCE:
- 4 years DevOps experience
- 3 years AWS cloud
- 2 years Docker and Kubernetes
- CI/CD pipeline setup
- Infrastructure as Code

SKILLS:
AWS, Docker, Kubernetes, Jenkins, Terraform, Python, Bash, Git

EDUCATION:
Bachelor of Computer Engineering - HCMUT (2019)

PROJECTS:
- AWS infrastructure automation
- Kubernetes cluster management
- CI/CD pipeline with Jenkins
            """
        }
    ]
    
    # Create CV files
    for cv in cv_samples:
        file_path = f"candidate_uploads/{cv['filename']}"
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cv['content'])
    
    print(f"✅ Created {len(cv_samples)} sample CV files in candidate_uploads/")
    for cv in cv_samples:
        print(f"   📄 {cv['filename']}")

def create_demo_instructions():
    """Tạo file hướng dẫn demo"""
    
    instructions = """
# 🎯 HR RECRUITMENT SYSTEM - DEMO GUIDE

## 🚀 QUICK DEMO STEPS

### 1. Start the system:
```bash
python hr_recruitment_system.py
```

### 2. Try screening for Python Developer position:
- Select option 2 (Screen candidates for a position)
- Choose job file: python_developer_job.json
- Use folder: candidate_uploads (press Enter for default)
- Review the results!

### 3. Try screening for Data Scientist position:
- Select option 2 again
- Choose job file: data_scientist_job.json  
- Use same candidate folder
- Compare the different results!

## 📊 EXPECTED RESULTS

### For Python Developer Job:
- **Top matches**: Nguyen Van A, Tran Thi B (high skill similarity)
- **Moderate matches**: Le Van C (some relevant skills)
- **Low matches**: Data scientists, Java developer

### For Data Scientist Job:
- **Top matches**: Pham Thi D (perfect match)
- **Moderate matches**: Hoang Van E (some relevant skills)
- **Low matches**: Pure developers without ML experience

## 🎯 WHAT TO OBSERVE

1. **Skill Matching**: How well the AI matches candidate skills to job requirements
2. **Experience Weighting**: How experience level affects ranking
3. **Education Consideration**: Impact of education requirements
4. **Location Factors**: Geographic compatibility scoring
5. **Confidence Levels**: AI's certainty in its predictions

## 💡 TIPS FOR TESTING

- Try adjusting `minimum_confidence` in job files (0.6, 0.7, 0.8)
- Modify `max_candidates_to_review` to see more/fewer results
- Add or remove skills from job requirements
- Change experience requirements and see impact

Enjoy testing the AI recruitment system! 🤖
"""
    
    with open("DEMO_INSTRUCTIONS.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Created demo instructions: DEMO_INSTRUCTIONS.md")

def main():
    """Setup complete demo environment"""
    print("🎯 SETTING UP HR RECRUITMENT SYSTEM DEMO")
    print("="*50)
    
    # Create folders
    create_demo_folders()
    print()
    
    # Create sample job requirements
    create_sample_job_requirements()
    print()
    
    # Create sample CV files
    create_sample_cv_files()
    print()
    
    # Create demo instructions
    create_demo_instructions()
    print()
    
    print("🎉 DEMO SETUP COMPLETE!")
    print("="*30)
    print("📋 What was created:")
    print("   📁 job_requirements/ - Sample job postings")
    print("   📁 candidate_uploads/ - Sample CV files")
    print("   📁 hr_results/ - Results will be saved here")
    print("   📄 DEMO_INSTRUCTIONS.md - How to run demo")
    print()
    print("🚀 Next steps:")
    print("   1. Run: python hr_recruitment_system.py")
    print("   2. Follow DEMO_INSTRUCTIONS.md")
    print("   3. Test with different job requirements!")
    print()
    print("💡 Tip: Start with the Python Developer job for best demo results!")

if __name__ == "__main__":
    main()
