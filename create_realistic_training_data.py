#!/usr/bin/env python3
"""
Create Realistic Training Data for Job-Resume Matching
Fix the data leakage and perfect correlation issues
"""

import pandas as pd
import numpy as np
import random
from datetime import datetime

# Set random seed
random.seed(42)
np.random.seed(42)

def create_realistic_pairs():
    """Create realistic job-candidate pairs with proper variation"""
    
    print("🔄 Creating realistic training data...")
    
    # Load original data
    try:
        pairs_df = pd.read_csv('data/job_candidate_pairs_from_clean.csv')
        print(f"✅ Loaded {len(pairs_df):,} original pairs")
    except FileNotFoundError:
        print("❌ Original pairs file not found")
        return
    
    # Create realistic variations
    realistic_pairs = []
    
    for idx, row in pairs_df.iterrows():
        # Add noise to features to break perfect correlations
        skills_jaccard = max(0, min(1, row['skills_jaccard'] + np.random.normal(0, 0.1)))
        text_similarity = max(0, min(1, row['text_similarity'] + np.random.normal(0, 0.05)))
        
        # Create realistic experience match (not always 1.0)
        job_exp = random.randint(0, 8)  # 0-8 years required
        candidate_exp = random.randint(0, 10)  # 0-10 years experience
        
        if job_exp == 0:
            experience_match = 1.0  # No requirement
        elif candidate_exp >= job_exp:
            experience_match = 1.0  # Meets requirement
        else:
            experience_match = candidate_exp / job_exp  # Partial match
        
        # Create realistic category match
        category_match = random.choice([0.0, 0.0, 0.0, 0.5, 1.0])  # Mostly 0, some matches
        
        # Calculate overall suitability WITHOUT using it for labels
        overall_suitability = (
            skills_jaccard * 0.4 +
            text_similarity * 0.2 +
            experience_match * 0.2 +
            category_match * 0.2
        )
        
        # Create labels based on INDEPENDENT criteria (not overall_suitability)
        if skills_jaccard >= 0.6 and experience_match >= 0.8 and category_match >= 0.5:
            label = 2  # Highly suitable
        elif skills_jaccard >= 0.4 and experience_match >= 0.6:
            label = 1  # Moderately suitable  
        elif skills_jaccard >= 0.2 or experience_match >= 0.4:
            label = 0  # Not suitable
        else:
            label = 0  # Not suitable
        
        # Add some randomness to create more realistic distribution
        if random.random() < 0.1:  # 10% chance to flip label
            if label == 2:
                label = 1
            elif label == 0 and random.random() < 0.3:
                label = 1
        
        realistic_pairs.append({
            'pair_id': idx + 1,
            'job_id': row['job_id'],
            'candidate_id': row['candidate_id'],
            'skills_jaccard': round(skills_jaccard, 3),
            'text_similarity': round(text_similarity, 3),
            'experience_match': round(experience_match, 3),
            'category_match': round(category_match, 3),
            'overall_suitability': round(overall_suitability, 3),
            'suitability_label': label,
            'job_title': row['job_title'],
            'candidate_category': row['candidate_category'],
            'job_required_exp': job_exp,
            'candidate_exp': candidate_exp,
            'created_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        
        if idx % 100 == 0:
            print(f"   Processed {idx:,}/{len(pairs_df):,} pairs")
    
    # Create DataFrame
    realistic_df = pd.DataFrame(realistic_pairs)
    
    # Show distribution
    print(f"\n📊 New Label Distribution:")
    label_counts = realistic_df['suitability_label'].value_counts().sort_index()
    for label, count in label_counts.items():
        label_name = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}[label]
        print(f"   Class {label} ({label_name}): {count:,} samples ({count/len(realistic_df)*100:.1f}%)")
    
    # Show feature statistics
    print(f"\n📈 Feature Statistics:")
    for col in ['skills_jaccard', 'text_similarity', 'experience_match', 'category_match', 'overall_suitability']:
        mean_val = realistic_df[col].mean()
        std_val = realistic_df[col].std()
        print(f"   {col}: mean={mean_val:.3f}, std={std_val:.3f}")
    
    # Check correlations
    print(f"\n🔍 Correlation Check:")
    corr_overall_label = realistic_df['overall_suitability'].corr(realistic_df['suitability_label'])
    corr_exp_match = realistic_df['experience_match'].std()
    print(f"   overall_suitability vs suitability_label: {corr_overall_label:.3f}")
    print(f"   experience_match std deviation: {corr_exp_match:.3f}")
    
    # Export
    output_file = 'data/realistic_job_candidate_pairs.csv'
    realistic_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n✅ Exported {len(realistic_df):,} realistic pairs to: {output_file}")
    
    return realistic_df

def create_additional_features(df):
    """Add additional engineered features"""
    
    print("🔧 Adding engineered features...")
    
    # Skills-experience interaction
    df['skills_exp_interaction'] = df['skills_jaccard'] * df['experience_match']
    
    # Category boost
    df['category_boost'] = df['category_match'] * 0.5
    
    # Experience penalty for over-qualification
    df['overqualified_penalty'] = np.where(
        (df['candidate_exp'] > df['job_required_exp'] + 3) & (df['job_required_exp'] > 0),
        -0.1, 0.0
    )
    
    # Skills threshold features
    df['skills_high'] = (df['skills_jaccard'] >= 0.5).astype(int)
    df['skills_medium'] = ((df['skills_jaccard'] >= 0.3) & (df['skills_jaccard'] < 0.5)).astype(int)
    
    # Experience level features
    df['exp_junior'] = (df['candidate_exp'] <= 2).astype(int)
    df['exp_mid'] = ((df['candidate_exp'] > 2) & (df['candidate_exp'] <= 5)).astype(int)
    df['exp_senior'] = (df['candidate_exp'] > 5).astype(int)
    
    print(f"✅ Added {df.shape[1] - 13} engineered features")
    return df

def main():
    """Main function"""
    print("🎯 CREATING REALISTIC TRAINING DATA")
    print("=" * 50)
    
    # Create realistic pairs
    realistic_df = create_realistic_pairs()
    
    if realistic_df is not None:
        # Add engineered features
        realistic_df = create_additional_features(realistic_df)
        
        # Export final version
        final_output = 'data/realistic_job_candidate_pairs_final.csv'
        realistic_df.to_csv(final_output, index=False, encoding='utf-8')
        print(f"✅ Final dataset exported to: {final_output}")
        
        print(f"\n🎉 COMPLETED!")
        print(f"📊 Final dataset: {len(realistic_df):,} pairs with {realistic_df.shape[1]} features")
        print(f"📁 Ready for training with four_models_training.py")
        
        # Show sample
        print(f"\n📋 Sample data:")
        print(realistic_df[['skills_jaccard', 'experience_match', 'category_match', 'suitability_label']].head())

if __name__ == "__main__":
    main()
