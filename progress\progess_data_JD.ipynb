{"cells": [{"cell_type": "code", "execution_count": 53, "id": "d81e86d7", "metadata": {}, "outputs": [], "source": ["# Import c<PERSON><PERSON> thư viện c<PERSON>n thiết\n", "import pandas as pd\n", "from langdetect import detect\n", "from deep_translator import GoogleTranslator\n", "import re\n", "import spacy\n", "from underthesea import word_tokenize, pos_tag\n", "from nltk.corpus import stopwords\n", "import numpy as np\n", "from collections import defaultdict"]}, {"cell_type": "code", "execution_count": 42, "id": "23a0e1ff", "metadata": {}, "outputs": [], "source": ["nlp_en = spacy.load('en_core_web_sm')\n", "stop_words_en = None\n", "stop_words_vi = None"]}, {"cell_type": "code", "execution_count": 36, "id": "04cdb790", "metadata": {}, "outputs": [], "source": ["# Đường dẫn đến file csv chứa các job description\n", "csv_jd = \"../data/raw/itviec_jobs_undetected.csv\"\n", "stop_words_vn_txt = \"../docs/vietnamese-stopwords.txt\""]}, {"cell_type": "code", "execution_count": 4, "id": "2c5413fc", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(csv_jd)"]}, {"cell_type": "code", "execution_count": null, "id": "165836c4", "metadata": {}, "outputs": [], "source": ["print(df.head(10))"]}, {"cell_type": "code", "execution_count": null, "id": "adf045bf", "metadata": {}, "outputs": [], "source": ["def detect_language(text):\n", "    try:\n", "        return detect(text)\n", "    except:\n", "        return 'unknown'\n", "def translate_to_english(text, lang):\n", "    try:\n", "        if lang == 'en' or pd.isna(text) or text.strip() == \"\":\n", "            return text\n", "        return GoogleTranslator(source=lang, target='en').translate(text)\n", "    except Exception as e:\n", "        print(f\"Error translating: {e} | text: {text}\")\n", "        return text\n", "def clean_text(text, lang):\n", "    text = text.lower()\n", "    text = re.sub(r'http\\S+|#\\S+|@\\S+|[^\\w\\s]', '', text)\n", "    if lang == 'en':\n", "        doc = nlp_en(text)\n", "        tokens = [token.lemma_ for token in doc if token.text not in stop_words_en]\n", "        return ' '.join(tokens)\n", "    else:\n", "        tokens = word_tokenize(text)\n", "        tokens = [t for t in tokens if t not in stop_words_vi]\n", "        return ' '.join(tokens)\n", "def clean_skills(text):\n", "    if pd.isna(text) or text.strip() == \"\":\n", "        return []\n", "    return [t.strip() for t in text.split(',') if t.strip()]"]}, {"cell_type": "code", "execution_count": 27, "id": "2f57f34e", "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> dụng cho cột 'description'\n", "df['language'] = df['description'].apply(detect_language)\n", "df['skills_en'] = df['skills']"]}, {"cell_type": "code", "execution_count": 26, "id": "c5520aac", "metadata": {}, "outputs": [], "source": ["df['description_en'] = df.apply(lambda x: translate_to_english(x['description'], x['language']), axis=1)\n", "df['requirements_en'] = df.apply(lambda x: translate_to_english(x['requirements'], x['language']), axis=1)"]}, {"cell_type": "code", "execution_count": 28, "id": "322b6d68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   id                                            title          company  \\\n", "0   1                                   MLops Engineer  Trusting Social   \n", "1   2              Senior DevOps Engineer (Cloud, AWS)            TymeX   \n", "2   3  VTS - <PERSON><PERSON>ên <PERSON> (Agile/ Azure)    Viettel Group   \n", "3   4       VTS - <PERSON><PERSON> G<PERSON> - Presales Engineer    Viettel Group   \n", "4   5    VHT - Embedded Software Engineer (Linux, C++)    Viettel Group   \n", "\n", "      location           salary      work_type  \\\n", "0  <PERSON> <PERSON> Minh   You'll love it  Not specified   \n", "1  <PERSON> Chi Minh   You'll love it  Not specified   \n", "2       Ha Noi   You'll love it  Not specified   \n", "3       Ha Noi   You'll love it  Not specified   \n", "4       Ha Noi  650 - 2,200 USD  Not specified   \n", "\n", "                                         description  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3  <PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON>vũ tr<PERSON>” c<PERSON><PERSON> Viettel, n<PERSON><PERSON> bạn k...   \n", "4  <PERSON><PERSON><PERSON> hơn 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "\n", "                                        requirements  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3  Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4  Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "\n", "                                              skills language  \\\n", "0  MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2  Project Management, Business Analysis, Presale...       vi   \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4  Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "\n", "                                      description_en  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek is leading Ecopark to develop a model o...   \n", "3  Joining Viettel technology, where you are not ...   \n", "4  With more than 1200 high quality personnel, Vi...   \n", "\n", "                                     requirements_en  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Good logical thinking, solution -oriented thin...   \n", "3  Degree: Graduated from university (good or hig...   \n", "4  Graduated with regular university or higher sp...   \n", "\n", "                                           skills_en  \n", "0  MLOps, Python, Linux, Docker, Data Science, Te...  \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...  \n", "2  Project Management, Business Analysis, Presale...  \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...  \n", "4  Embedded, C++, Linux, C language, Embedded Eng...  \n"]}], "source": ["print(df.head())"]}, {"cell_type": "code", "execution_count": 43, "id": "d832b02d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'lý do', '<PERSON> <PERSON>', 'nhờ đó', '<PERSON>', 'ăn riêng', 'điểm đầu tiên', 'kể cả', 'chưa tính', 'ngày nào', 'mới hay', 'thôi', 'lần theo', 'bởi ai', 'dùng làm', 'nói toẹt', 'nhỏ', 'tại lòng', 'vở', 'đã không', 'thường hay', 'nơi', 'xa', 'về tay', 'tự tạo', 'việc', 'chứ sao', 'đưa chuyện', 'dào', 'nhìn theo', 'khi không', 'xoẹt', 'với nhau', 'khi nên', 'đưa tay', 'rồi sau', 'ăn chung', 'tự lượng', 'ăn hết', 'số phần', 'hay làm', 'nói riêng', 'cao lâu', 'sốt sột', 'tr<PERSON>h khỏi', '<PERSON> nhé', 'c<PERSON> hồ', 'l<PERSON>y ra', 'chưa từng', 'biết việc', 'không được', 'ông nhỏ', 'nước đến', 'lần sang', 'nữa rồi', 'rõ', 'đâu cũng', 'tăng chúng', 'vài tên', 'chứ không phải', 'ăn về', 'đồng thời', 'sao', 'cổ lai', 'nhiều ít', 'mang lại', 'trước đó', 'sau này', 'ngay từ', 'thì ra', 'hơn nữa', 'là vì', 'ắt hẳn', 'về nước', 'phót', 'ở vào', 'chứ không', 'nhà ngươi', 'thảo hèn', 'anh', 'vài', 'trước khi', 'khó chơi', 'nhận việc', 'trước ngày', 'cho chắc', 'tắp', 'cơ hội', 'ồ', 'lúc trước', 'nào là', 'thêm vào', 'tháng ngày', 'thích cứ', 'từng cái', 'hay hay', 'ở lại', 'vâng chịu', 'nghe đâu', 'nào', 'tới mức', 'chứ còn', 'cũng như', 'cha', 'ngôi nhà', 'nhất là', 'sẽ hay', 'vùng lên', 'vậy là', 'đã thế', 'nền', 'dễ nghe', 'ráo trọi', 'có được', 'ắt là', 'vào', 'xuất kì bất ý', 'đủ điểm', 'nhau', 'cách không', 'toé khói', 'dưới', 'xa tắp', 'ra bộ', 'ít nhất', 'chỉ là', 'lấy vào', 'mỗi lần', 'phía bên', 'của', 'phải người', 'đến thì', 'đã hay', 'để không', 'bập bõm', 'buổi ngày', 'cuộc', 'còn nữa', 'nhà', 'tỏ ra', 'đây rồi', 'ư', 'chưa', 'lên nước', 'phỏng nước', 'nhỏ người', 'phắt', 'phần lớn', 'riêng từng', 'xem lại', 'dễ ngươi', 'tôi', 'tự tính', 'đặt làm', 'chùn chũn', 'chú mày', 'thế sự', 'ngoải', 'có chăng', 'nhung nhăng', 'số cho biết', 'cả người', 'rõ là', 'chính là', 'ớ', 'khó tránh', 'phốc', 'buổi làm', 'bằng được', 'như vậy', 'ít nữa', 'đặt trước', 'cô quả', 'dạ con', 'rén', 'loại từ', 'mà vẫn', 'ăn ngồi', 'chầm chập', 'ứ hự', 'song le', 'bằng người', 'phè phè', 'nghen', 'trong khi', 'tuốt tuột', 'sang tay', 'chớ gì', 'không khỏi', 'đánh giá', 'nghe không', 'duy', 'bởi', 'làm như', 'đặt ra', 'tìm hiểu', 'trả lại', 'xử lý', 'bỏ lại', 'chuyển', 'dễ sợ', 'nhà ngoài', 'nói', 'chứ ai', 'đảm bảo', 'chú khách', 'vì rằng', 'một lúc', 'nên', 'vượt', 'khó', 'đáng kể', 'a lô', 'không đầy', 'bây bẩy', 'ráo nước', 'đến cùng', 'có phải', 'thỉnh thoảng', 'mọi lúc', 'nặng về', 'bởi tại', 'á', 'có thể', 'lâu ngày', 'ào vào', 'nhằm', 'đâu phải', 'ngồi', 'coi bộ', 'để giống', 'đến nỗi', 'tin vào', 'bắt đầu', 'lên mạnh', 'hiện tại', 'chú', 'bất tử', 'gần như', 'ngọt', 'đâu có', 'chúng mình', 'tù tì', 'hay nói', 'quay bước', 'dễ ăn', 'theo tin', 'đang thì', 'có nhà', 'biết', 'như tuồng', 'xăm xắm', 'phần việc', 'sau sau', 'mà lại', 'tốc tả', 'nói phải', 'nọ', 'rích', 'chứ lại', 'lâu các', 'nghe tin', 'nên chi', 'vậy thì', 'đạt', 'phương chi', 'quá mức', 'có', 'dùng đến', 'lớn lên', 'khó khăn', 'giữ ý', 'à', 'cả', 'vì chưng', 'cơ chừng', 'là nhiều', 'chung quy lại', 'phía bạn', 'nơi nơi', 'vì vậy', 'thì là', 'chung cho', 'tin', 'ý', 'nhanh lên', 'nhận làm', 'cao xa', 'bập bà bập bõm', 'từ thế', 'người mình', 'nặng mình', 'cá nhân', 'ối giời ơi', 'đây đó', 'người', 'lên', 'nếu có', 'lấy để', 'theo như', 'thành thử', 'cũng vậy', 'dùng hết', 'ngày càng', 'chị ấy', 'sử dụng', 'đánh đùng', 'không điều kiện', 'ngoài xa', 'nay', 'vào lúc', 'nghe như', 'lấy số', 'vậy mà', 'vài nhà', 'thay đổi', 'đều', 'áng như', 'dễ sử dụng', 'ba ba', 'nước lên', 'xệp', 'làm tôi', 'lại cái', 'bất thình lình', 'này', 'ăn hỏi', 'thẩy', 'nhìn nhận', 'nhất loạt', 'vâng', 'nớ', 'tạo ý', 'trước tuổi', 'tay quay', 'ăn người', 'ngày', 'đưa em', 'chứ lị', 'làm thế nào', 'nếu mà', 'nước nặng', 'bằng cứ', 'cách', 'cả tin', 'khi nào', 'là cùng', 'bỗng nhiên', 'làm nên', 'lên cao', 'bất kể', 'nào phải', 'ra chơi', 'bỏ việc', 'chu cha', 'chui cha', 'không biết', 'vào vùng', 'chị bộ', 'làm cho', 'tên cái', 'bác', 'một cơn', 'tại sao', 'không phải', 'chẳng lẽ', 'bất đồ', 'cao số', 'bao nhiêu', 'bởi thế', 'chẳng phải', 'người khác', 'tông tốc', 'người hỏi', 'có số', 'dạ khách', 'giá trị thực tế', 'hãy còn', 'cây', 'cái', 'hết ý', 'số', 'tại đây', 'gì', 'đã', 'bởi vì', 'cao thế', 'tênh', 'vả lại', 'ít lâu', 'chung ái', 'thiếu', 'tăng thêm', 'nhớ ra', 'nhưng mà', 'cho đến khi', 'đại loại', 'mang', 'ren rén', 'bên', 'nói ý', 'được cái', 'đến bao giờ', 'tại tôi', 'phần nhiều', 'đại để', 'ái chà', 'tới', 'bên cạnh', 'tạo nên', 'tấm bản', 'tự cao', 'nghiễm nhiên', 'sự', 'loại', 'có đâu', 'đặt để', 'không bao giờ', 'qua thì', 'bây chừ', 'sớm ngày', 'giờ lâu', 'thế lại', 'trước', 'bước khỏi', 'hầu hết', 'nhân tiện', 'nhanh', 'đến gần', 'làm lại', 'vài điều', 'thời điểm', 'tập trung', 'căn cắt', 'lấy lý do', 'tự vì', 'gây thêm', 'gần ngày', 'nhằm vào', 'tự ăn', 'trong mình', 'ý hoặc', 'vèo', 'từ ấy', 'dễ gì', 'bán thế', 'một số', 'dẫu mà', 'xăm xúi', 'xa xả', 'quá tay', 'ơ kìa', 'quay', 'đến thế', 'ừ thì', 'không những', 'ái', 'điều gì', 'ào', 'ô kê', 'dành dành', 'ai', 'từ ái', 'thứ', 'làm mất', 'trên dưới', 'vung thiên địa', 'ba', 'ăn cuộc', 'bản', 'thấy', 'sì sì', 'luôn tay', 'không ngoài', 'nhờ có', 'rằng', 'con tính', 'mọi khi', 'bỏ ra', 'bạn', 'chăn chắn', 'xon xón', 'thật lực', 'nghĩ tới', 'thật chắc', 'ôi thôi', 'chợt nhìn', 'hay nhỉ', 'người nhận', 'cái họ', 'làm gì', 'ở trên', 'phải cái', 'lâu nay', 'ít', 'thực sự', 'lại làm', 'hỏi lại', 'kể như', 'vèo vèo', 'nức nở', 'đến giờ', 'lần khác', 'ông tạo', 'tốt ngày', 'này nọ', 'cả đến', 'cái ấy', 'chịu ăn', 'bao lâu', 'chăng nữa', 'tên', 'trước đây', 'thích ý', 'pho', 'đưa', 'được tin', 'lại bộ', 'ba họ', 'nên người', 'cô mình', 'quan trọng', 'ạ ơi', 'hoặc là', 'trỏng', 'phải biết', 'tìm', 'lần sau', 'phải', 'ngay cả', 'tự', 'trời đất ơi', 'cả nhà', 'trệu trạo', 'riu ríu', 'từ đó', 'ứ ừ', 'vừa qua', 'nói tốt', 'về', 'qua chuyện', 'tột', 'phỉ phui', 'chung quy', 'bấy chầy', 'bỗng', 'vả chăng', 'vừa', 'còn', 'về không', 'vô hình trung', 'ai ai', 'gây giống', 'cụ thể như', 'đến tuổi', 'chắc ăn', 'ít có', 'nhìn lại', 'xem số', 'bên có', 'liên quan', 'nói thêm', 'nặng', 'nghe ra', 'lời', 'làm riêng', 'nghỉm', 'gây ra', 'như sau', 'trước kia', 'phù hợp', 'ra gì', 'đâu đây', 'từ tính', 'qua lại', 'em', 'nhất quyết', 'đưa cho', 'thì thôi', 'sau hết', 'vị tất', 'đều đều', 'hoặc', 'ngay lập tức', 'không nhận', 'khác nào', 'đều nhau', 'xin gặp', 'giống nhau', 'cho hay', 'ngày rày', 'nhằm khi', 'quan tâm', 'phải chi', 'khi', 'một cách', 'mọi việc', 'không hay', 'riêng', 'số thiếu', 'mức', 'nói lên', 'cậu', 'lượng cả', 'nhược bằng', 'những', 'lớn', 'dễ', 'bất luận', 'veo veo', 'phải chăng', 'bởi sao', 'phỏng tính', 'vào gặp', 'bất cứ', 'bỏ không', 'tính căn', 'tháng tháng', 'cơ mà', 'và', 'tênh tênh', 'lấy', 'lúc này', 'sang', 'sau đó', 'giá trị', 'điểm chính', 'khiến', 'quay lại', 'có đáng', 'trước sau', 'lấy sau', 'khác', 'biết bao nhiêu', 'bộ', 'để lại', 'ô kìa', 'đâu nào', 'khách', 'nhỡ ra', 'bản bộ', 'tuần tự', 'nghe hiểu', 'cùng với', 'hay', 'nào hay', 'do vì', 'úi', 'dưới nước', 'cao thấp', 'hỏi xem', 'mỗi một', 'những muốn', 'những ai', 'có tháng', 'phía dưới', 'ra tay', 'thứ đến', 'ngoài', 'quá', 'đâu', 'nhà việc', 'ngồi trệt', 'mà cả', 'mọi người', 'nói xa', 'lượng', 'thốt nhiên', 'chốc chốc', 'giảm thế', 'lại người', 'bị', 'nhờ', 'xềnh xệch', 'nên chăng', 'so', 'phụt', 'tìm việc', 'ở đây', 'ngõ hầu', 'có cơ', 'sở dĩ', 'bông', 'thế mà', 'tốt bạn', 'vâng dạ', 'tất cả bao nhiêu', 'vừa rồi', 'điều kiện', 'ráo cả', 'dùng', 'cứ', 'qua', 'hết chuyện', 'lần nào', 'xảy ra', 'cu cậu', 'càng hay', 'chịu tốt', 'không ai', 'cả năm', 'thiếu gì', 'trước nay', 'không có gì', 'khó nghe', 'như thể', 'ví phỏng', 'chớ chi', 'rồi đây', 'giờ đến', 'chành chạnh', 'tà tà', 'thời gian sử dụng', 'ngày qua', 'nhà tôi', 'trên', 'vượt quá', 'công nhiên', 'thuần', 'bỏ', 'oai oái', 'thế à', 'từ loại', 'tuy đã', 'khác nhau', 'giảm thấp', 'nhìn xuống', 'giảm', 'sất', 'một vài', 'vẫn', 'ạ', 'căn', 'trở thành', 'ngộ nhỡ', 'tuy có', 'không để', 'chịu chưa', 'nhớ lấy', 'chung qui', 'không cứ', 'đủ', 'sắp đặt', 'chết nỗi', 'bằng ấy', 'úi dào', 'lượng từ', 'chúng ông', 'ba tăng', 'tuốt luốt', 'lần lần', 'đặt mình', 'cho đang', 'ăn chắc', 'ngăn ngắt', 'trực tiếp', 'chuyển tự', 'sang năm', 'nếu cần', 'từng thời gian', 'lại nữa', 'cần cấp', 'thếch', 'chợt nghe', 'như ai', 'sau cuối', 'ít biết', 'dữ cách', 'qua lần', 'phía trong', 'bị chú', 'tên tự', 'đưa vào', 'sao vậy', 'đến lúc', 'chú dẫn', 'hơn', 'gây cho', 'thường khi', 'ai đó', 'nhất tề', 'trong lúc', 'bấy nay', 'nhỉ', 'nói qua', 'cái gì', 'nhận nhau', 'đã là', 'cô ấy', 'bây giờ', 'vốn dĩ', 'bởi nhưng', 'từ giờ', 'từ nay', 'sáng rõ', 'quả', 'ái dà', 'chắc', 'không phải không', 'có chứ', 'bấy lâu', 'ối dào', 'tha hồ ăn', 'lúc đó', 'chắc dạ', 'nhưng', 'nhìn chung', 'nhớ lại', 'mở nước', 'thường số', 'lên cơn', 'thộc', 'rồi thì', 'toà', 'tuốt tuồn tuột', 'nhằm lúc', 'sáng ý', 'đưa tin', 'đại phàm', 'lại đây', 'dài lời', 'tắp lự', 'phỏng như', 'ớ này', 'sao đang', 'thoắt', 'thương ôi', 'thốt', 'răng', 'ngày ấy', 'nhớ', 'thúng thắng', 'cách đều', 'vào đến', 'chơi họ', 'lấy cả', 'rồi', 'tuy là', 'bài bác', 'tiếp đó', 'ngay tức khắc', 'giờ đi', 'thành ra', 'vào khoảng', 'cho tới khi', 'nếu', 'giờ đây', 'bản riêng', 'tránh tình trạng', 'phứt', 'thanh điều kiện', 'ra ngôi', 'bỏ mình', 'khi khác', 'chứ gì', 'thêm chuyện', 'thỏm', 'hiểu', 'cả ngày', 'tự ý', 'khỏi nói', 'tăng', 'có thế', 'thà là', 'ra', 'chung chung', 'bài', 'nước', 'sau đây', 'thật là', 'để mà', 'bao giờ', 'nếu được', 'trong vùng', 'tuy vậy', 'bỏ cha', 'còn thời gian', 'là là', 'từ', 'ra điều', 'việc gì', 'căn cái', 'gần bên', 'than ôi', 'cấp số', 'chao ôi', 'bỗng nhưng', 'dành', 'bển', 'hay không', 'cũng', 'chú mình', 'đều bước', 'qua đi', 'lần trước', 'mang mang', 'thanh thanh', 'chết tiệt', 'chẳng những', 'mình', 'xuất kỳ bất ý', 'bộ thuộc', 'không cùng', 'làm bằng', 'thì', 'các cậu', 'nhà khó', 'đến nay', 'của ngọt', 'chúng tôi', 'số người', 'nhờ chuyển', 'có vẻ', 'chắc chắn', 'thuộc lại', 'không bán', 'coi mòi', 'dần dần', 'là', 'quả là', 'xin vâng', 'bức', 'xa gần', 'lớn nhỏ', 'đưa tới', 'khẳng định', 'tuổi', 'ơi là', 'nếu không', 'trong ấy', 'nói đến', 'chỉ có', 'bay biến', 'trước hết', 'văng tê', 'tựu trung', 'nếu vậy', 'tại đâu', 'do vậy', 'tên chính', 'lấy thế', 'tốt bộ', 'đến hay', 'giảm chính', 'tránh xa', 'do đó', 'cho được', 'bà', 'chọn', 'thốc', 'dạ bán', 'vậy ư', 'biết đâu', 'thuần ái', 'tình trạng', 'ngày xưa', 'ồ ồ', 'ô hay', 'suýt nữa', 'vượt khỏi', 'nói khó', 'phỏng theo', 'sẽ biết', 'khá tốt', 'đặt', 'tháng năm', 'phải tay', 'ông', 'dù sao', 'nói ra', 'phía sau', 'cần gì', 'số loại', 'tăng cấp', 'lúc sáng', 'chưa dùng', 'đặc biệt', 'ngay thật', 'rất lâu', 'trên bộ', 'quá đáng', 'thốt nói', 'nghe lại', 'thay đổi tình trạng', 'dẫn', 'ngay khi', 'thốc tháo', 'âu là', 'ông từ', 'nói với', 'càng càng', 'lên xuống', 'tuy', 'thuộc cách', 'ở năm', 'dù cho', 'nhà làm', 'đúng ra', 'chợt', 'người người', 'như trên', 'thích', 'tới thì', 'chịu', 'cũng nên', 'nhất sinh', 'thậm từ', 'mới rồi', 'ra ý', 'xuể', 'đúng', 'bán cấp', 'bất quá', 'đến đâu', 'đủ số', 'xoẳn', 'nước ăn', 'được lời', 'đó đây', 'gì đó', 'ở nhờ', 'thường tính', 'chính bản', 'đưa về', 'hoàn toàn', 'hơn là', 'sau nữa', 'khác xa', 'bỏ bà', 'rồi xem', 'đến cả', 'tính từ', 'lại ăn', 'đến khi', 'thật ra', 'xoành xoạch', 'có điều kiện', 'nhà chung', 'tránh', 'bây nhiêu', 'khoảng không', 'dẫu', 'từ điều', 'chớ kể', 'thím', 'vậy nên', 'lấy làm', 'nhân dịp', 'thực hiện đúng', 'thôi việc', 'hay sao', 'mới', 'vô kể', 'dần dà', 'quá ư', 'lại giống', 'có chuyện', 'tức thì', 'tanh tanh', 'xăm xăm', 'ba ngày', 'buổi', 'chọn bên', 'không có', 'chớ không', 'kể', 'sáng thế', 'đúng với', 'làm lấy', 'con dạ', 'xa cách', 'cùng nhau', 'chớ như', 'trả ngay', 'vừa vừa', 'làm tăng', 'bỏ cuộc', 'chậc', 'gặp', 'vì thế', 'chính', 'người nghe', 'chắc người', 'cũng được', 'thuộc từ', 'ít thấy', 'giống người', 'nhất tâm', 'thường bị', 'xuống', 'giống như', 'vừa lúc', 'chắc lòng', 'cho', 'cần', 'đủ dùng', 'tránh ra', 'đáng lý', 'rứa', 'tạo cơ hội', 'lần này', 'nước cùng', 'bấy lâu nay', 'ngày cấp', 'mở ra', 'dùng cho', 'từng ấy', 'đầy phè', 'ngôi', 'điểm gặp', 'gần hết', 'cha chả', 'tại đó', 'cứ điểm', 'tuy thế', 'phóc', 'quá nhiều', 'bằng nào', 'thế ra', 'cụ thể là', 'phía', 'đáng', 'biết chừng nào', 'vài người', 'cách bức', 'thích tự', 'lần', 'rày', 'sao cho', 'bỗng đâu', 'hết của', 'có nhiều', 'cuối', 'phải giờ', 'ôi chao', 'trong này', 'họ', 'bấy chừ', 'từ từ', 'như ý', 'cơ chỉ', 'cảm ơn', 'vậy', 'ngọn nguồn', 'cho nhau', 'hỗ trợ', 'ắt thật', 'tạo', 'gần', 'xa xa', 'đó', 'ngay lúc', 'ăn quá', 'nguồn', 'từ căn', 'từ tại', 'nhất luật', 'tất thảy', 'không bao lâu', 'thậm chí', 'chung cục', 'ở được', 'lấy thêm', 'sau', 'sớm', 'ngay bây giờ', 'chưa dễ', 'từng đơn vị', 'bỏ mẹ', 'mỗi', 'gồm', 'thấy tháng', 'luôn', 'mạnh', 'chớ', 'cao răng', 'cuối cùng', 'ví thử', 'ngày xửa', 'chị', 'có ai', 'tại vì', 'nhất định', 'không chỉ', 'không cần', 'thật vậy', 'mỗi lúc', 'ít ra', 'thửa', 'thà', 'quá trình', 'răng răng', 'lượng số', 'tăm tắp', 'tức tốc', 'vô vàn', 'đã lâu', 'bài bỏ', 'quả vậy', 'một ít', 'rồi ra', 'ổng', 'thục mạng', 'tăng thế', 'nhé', 'chiếc', 'chính điểm', 'đã vậy', 'thế thôi', 'gần đến', 'ví bằng', 'không thể', 'à này', 'căn tính', 'điều', 'nhất thiết', 'tăng giảm', 'được nước', 'nghe rõ', 'tuổi cả', 'mợ', 'để đến nỗi', 'bị vì', 'trực tiếp làm', 'gì gì', 'lâu lâu', 'cả ăn', 'rồi tay', 'tỏ vẻ', 'cật sức', 'thế nào', 'ngọn', 'thanh không', 'bất nhược', 'không gì', 'dì', 'chỉn', 'biết chắc', 'dễ thấy', 'chưa cần', 'hay đâu', 'biết được', 'đúng tuổi', 'thanh tính', 'hiện nay', 'muốn', 'tạo điều kiện', 'ý chừng', 'khác gì', 'theo', 'sự thế', 'rén bước', 'yêu cầu', 'trả của', 'ba ngôi', 'đại nhân', 'không tính', 'ào ào', 'ơ hay', 'đâu đâu', 'mỗi người', 'những là', 'câu hỏi', 'thường thôi', 'tiếp theo', 'nhất mực', 'nhận biết', 'theo bước', 'chăng', 'làm được', 'thái quá', 'lên đến', 'chỉ chính', 'trả trước', 'đủ điều', 'phía trước', 'lúc lâu', 'cho biết', 'phần', 'cật lực', 'ơ', 'thật thà', 'thoạt nhiên', 'tại', 'bỏ nhỏ', 'cảm thấy', 'để được', 'ít quá', 'rồi sao', 'bất quá chỉ', 'để phần', 'úi chà', 'có ý', 'làm ngay', 'có khi', 'ít khi', 'amen', 'phải khi', 'ở như', 'quá giờ', 'tìm bạn', 'ông ấy', 'đầu tiên', 'hơn cả', 'cao', 'là thế nào', 'đưa đến', 'năm', 'nữa khi', 'dài ra', 'mà không', 'giữa lúc', 'thế là', 'thế thế', 'bất giác', 'thế chuẩn bị', 'đã đủ', 'thực tế', 'chứ', 'tính người', 'ra lại', 'thực vậy', 'nói đủ', 'chia sẻ', 'quá tuổi', 'giờ', 'khó nói', 'một', 'đáng lí', 'bấy nhiêu', 'quay đi', 'vừa khi', 'mỗi ngày', 'vấn đề', 'biết thế', 'là ít', 'là phải', 'bằng nấy', 'lấy giống', 'thật', 'xem ra', 'nghe được', 'dù gì', 'phải lại', 'ngoài này', 'như', 'ối giời', 'bước tới', 'phần sau', 'cóc khô', 'như chơi', 'khoảng', 'phăn phắt', 'phải lời', 'nói lại', 'quá bộ', 'sau cùng', 'ủa', 'vung tán tàn', 'dù dì', 'nhất đán', 'chưa bao giờ', 'nghe nói', 'thình lình', 'thoạt', 'bởi đâu', 'ngồi không', 'tít mù', 'cả thảy', 'hết', 'mà', 'nhón nhén', 'tất cả', 'đến ngày', 'thế thường', 'giống', 'thiếu điểm', 'bằng nhau', 'rốt cục', 'con nhà', 'quá lời', 'nói chung', 'mất', 'nhận được', 'từ khi', 'trong', 'tấm', 'tạo ra', 'nóc', 'đang', 'thêm', 'oái', 'từng phần', 'bởi vậy', 'suýt', 'rồi nữa', 'thì giờ', 'nghĩ đến', 'ăn sáng', 'dài', 'đơn vị', 'vì sao', 'đến lời', 'để', 'khó thấy', 'béng', 'để lòng', 'nhất nhất', 'cách nhau', 'bên bị', 'chuẩn bị', 'nói nhỏ', 'biết đâu chừng', 'cần số', 'đáng lẽ', 'nước bài', 'gặp khó khăn', 'lại nói', 'ô hô', 'trong đó', 'nếu như', 'từng nhà', 'không kể', 'khác khác', 'nghe thấy', 'thời gian', 'tha hồ chơi', 'bắt đầu từ', 'hay biết', 'gần xa', 'lúc nào', 'phần nào', 'để cho', 'bằng vào', 'số là', 'dạ dài', 'hết ráo', 'đây', 'cô tăng', 'trệt', 'trừ phi', 'họ xa', 'sao bản', 'như thế nào', 'tại nơi', 'lúc đi', 'bán', 'nấy', 'ờ', 'thì phải', 'tất tần tật', 'ừ ừ', 'quả thế', 'thường sự', 'cuối điểm', 'ai nấy', 'luôn cả', 'bỏ riêng', 'như thường', 'giờ này', 'làm tắp lự', 'ông ổng', 'nữa là', 'mọi thứ', 'ra đây', 'ngay khi đến', 'dễ thường', 'làm ra', 'ba cùng', 'đúng ngày', 'chỉ', 'ừ ào', 'trếu tráo', 'năm tháng', 'giữ lấy', 'vung tàn tán', 'vùng nước', 'bỗng không', 'làm vì', 'đưa ra', 'sau chót', 'hết rồi', 'thực hiện', 'tìm cách', 'qua khỏi', 'duy chỉ', 'chưa có', 'nhất thì', 'cho tin', 'ví dù', 'dạ dạ', 'hơn trước', 'bất ngờ', 'mọi giờ', 'có họ', 'bỏ xa', 'mở mang', 'ăn chịu', 'trả', 'tìm ra', 'đầy', 'dù rằng', 'làm tại', 'thật sự', 'dạ', 'thế đó', 'mà thôi', 'thảo nào', 'riệt', 'biết đâu đấy', 'bớ', 'tới gần', 'cứ như', 'khoảng cách', 'nhận thấy', 'quá tin', 'đầy tuổi', 'lúc', 'nếu thế', 'chính giữa', 'giữa', 'lấy lại', 'chuyện', 'phải như', 'có điều', 'tắp tắp', 'ngôi thứ', 'anh ấy', 'cái đã', 'ở đó', 'lòng', 'luôn luôn', 'có người', 'phải không', 'tính phỏng', 'làm dần dần', 'lại còn', 'cho đến nỗi', 'cũng vậy thôi', 'bỗng dưng', 'bản thân', 'nên làm', 'còn về', 'chơi', 'thế thì', 'bán dạ', 'ấy là', 'dễ khiến', 'ít hơn', 'tốt', 'tốt hơn', 'lần tìm', 'lúc ấy', 'trước nhất', 'đáo để', 'bộ điều', 'lấy xuống', 'ầu ơ', 'khó biết', 'nào đâu', 'ngươi', 'tất tật', 'ít thôi', 'như quả', 'vấn đề quan trọng', 'ắt phải', 'vài ba', 'phải cách', 'em em', 'bước', 'tuyệt nhiên', 'cùng cực', 'lúc khác', 'mất còn', 'nghe nhìn', 'bèn', 'bà ấy', 'nhất', 'thực ra', 'biết trước', 'cùng', 'qua ngày', 'thấp xuống', 'buổi mới', 'xa nhà', 'lâu', 'chuyển đạt', 'cho tới', 'nghe trực tiếp', 'cao ráo', 'nhận ra', 'cả nghe', 'nói rõ', 'dẫu rằng', 'cơ', 'ngày nọ', 'trển', 'được', 'đang tay', 'tháng', 'về sau', 'thanh', 'sáng ngày', 'làm', 'đến nơi', 'tha hồ', 'dầu sao', 'nhớ bập bõm', 'sang sáng', 'tấn', 'dù', 'nghe', 'ngay tức thì', 'ra lời', 'tự khi', 'cho rồi', 'à ơi', 'lại thôi', 'có dễ', 'ở', 'dữ', 'nhanh tay', 'sa sả', 'những như', 'khi trước', 'khó làm', 'thời gian tính', 'mang về', 'càng', 'cho rằng', 'nữa', 'khỏi', 'con', 'trong ngoài', 'thường đến', 'a ha', 'hết nói', 'từng giờ', 'làm theo', 'ngay lúc này', 'xa tanh', 'vô luận', 'đến xem', 'thường xuất hiện', 'xoét', 'cho thấy', 'xuất hiện', 'cho về', 'vậy ra', 'tọt', 'chọn ra', 'nào cũng', 'sáng', 'bản ý', 'hỏi xin', 'ra người', 'bao nả', 'hết cả', 'bằng như', 'mở', 'nghĩ', 'tuy rằng', 'đối với', 'tấn tới', 'tấm các', 'nói là', 'lại', 'xiết bao', 'chúng', 'cây nước', 'đưa xuống', 'bấy', 'rón rén', 'cô', 'ra vào', 'ấy', 'nên tránh', 'biết mình', 'đến', 'như là', 'lại quả', 'đủ nơi', 'sự việc', 'tiếp tục', 'ử', 'cho ăn', 'từng', 'không còn', 'đến cùng cực', 'ba bản', 'sắp', 'chưa kể', 'rằng là', 'vâng vâng', 'dở chừng', 'sao bằng', 'nghe đâu như', 'qua tay', 'cho nên', 'đáng số', 'nhìn thấy', 'thật tốt', 'thế', 'mới đây', 'xin', 'làm lòng', 'như trước', 'nhằm để', 'những khi', 'cái đó', 'cấp trực tiếp', 'bất kì', 'nhiều', 'lên ngôi', 'cùng tột', 'phía trên', 'rất', 'con con', 'chăng chắc', 'khác thường', 'thấp cơ', 'bởi thế cho nên', 'có ngày', 'bệt', 'quá bán', 'xem', 'các', 'thậm cấp', 'ăn trên', 'người khách', 'không dùng', 'thuộc', 'mang nặng', 'thứ bản', 'vâng ý', 'ăn làm', 'vạn nhất', 'choa', 'có ăn', 'trong số', 'nhận họ', 'cấp', 'ngồi bệt', 'thuộc bài', 'với', 'thấp', 'thanh điểm', 'ngay', 'tay', 'lên số', 'vài nơi', 'trước tiên', 'gây', 'bội phần', 'nước quả', 'rút cục', 'cũng thế', 'rõ thật', 'tốt mối', 'có chăng là', 'nghe chừng', 'đầy năm', 'gặp phải', 'khá', 'phải rồi', 'tối ư', 'nghĩ lại', 'bấy giờ', 'một khi', 'bằng không', 'cơ dẫn', 'thấp thỏm', 'bước đi', 'thích thuộc', 'quá thì', 'với lại', 'như thế', 'chết thật', 'làm đúng', 'điểm', 'mọi nơi', 'biết mấy', 'tớ', 'hỏi', 'bỏ mất', 'lấy được', 'nhiệt liệt', 'vừa mới', 'chung', 'cả nghĩ', 'alô', 'cùng tuổi', 'thế nên', 'thường thường', 'ra bài', 'tuổi tôi', 'thật quả', 'nặng căn', 'lời chú', 'ngày tháng', 'dễ đâu', 'cứ việc', 'phỏng', 'ắt', 'cùng chung', 'quay số', 'ngày này', 'hãy', 'hay tin', 'như nhau', 'tột cùng', 'lòng không', 'tới nơi', 'những lúc', 'số cụ thể', 'dễ dùng', 'bằng', 'làm sao', 'chẳng nữa', 'kể tới', 'cùng ăn', 'thêm giờ', 'buổi sớm', 'chắc vào', 'chịu lời', 'tính', 'bởi chưng', 'thanh ba', 'ra sao', 'làm tin', 'cơn', 'lấy có', 'mối', 'toẹt', 'tin thêm', 'thi thoảng', 'quận', 'tính cách', 'cao sang', 'của tin', 'hay là', 'vùng', 'nghĩ ra', 'dẫu sao', 'nghĩ xa', 'về phần', 'duy có', 'bất kỳ', 'cả thể', 'tuy nhiên', 'á à', 'lời nói', 'so với', 'tên họ', 'chung cuộc', 'veo', 'gần đây', 'nước xuống', 'còn như', 'sẽ', 'nói thật', 'chắc hẳn', 'họ gần', 'ăn', 'cơ cùng', 'đây này', 'ngồi sau', 'nhờ nhờ', 'chung nhau', 'thường tại', 'đâu như', 'như không', 'tiện thể', 'tanh', 'cực lực', 'nhận', 'mọi sự', 'do', 'tôi con', 'chưa chắc', 'rốt cuộc', 'mọi', 'nó', 'chùn chùn', 'bất chợt', 'nào đó', 'không', 'ngày ngày', 'chứ như', 'giữ', 'thà rằng', 'chúng ta', 'khó nghĩ', 'quan trọng vấn đề', 'thanh chuyển', 'phè', 'khó mở', 'chỉ tên', 'ăn tay', 'thoạt nghe', 'vị trí', 'ráo', 'sì', 'ngày giờ', 'ơi', 'nhiên hậu', 'kể từ', 'chủn', 'bỗng thấy', 'ý da', 'chính thị', 'ngày đến', 'nói bông', 'lấy ráo', 'lúc đến', 'nhóm', 'chí chết', 'tò te', 'vì', 'đến điều', 'cuốn', 'vẫn thế', 'thậm', 'cho đến', 'dễ như chơi', 'bỗng chốc', 'vụt', 'bỏ quá', 'cụ thể', 'thường', 'ít nhiều', 'đành đạch', 'hơn hết', 'thốt thôi', 'đặt mức', 'nói trước', 'biết bao', 'ngoài ra', 'bài cái', 'đâu đó', 'áng', 'quả thật', 'nhìn'}\n", "{'o', \"they'd\", 'yours', 'all', 'was', 'while', 'in', 'there', 'them', 'being', 'down', \"they've\", 'wasn', \"wasn't\", 'ma', 'm', \"it'd\", \"i've\", 'what', 'but', \"he's\", \"we'll\", \"won't\", 'through', 'an', 'isn', 'you', \"doesn't\", 'is', 'ain', 'were', 'against', 'having', 'over', 'again', 'herself', 'not', \"she's\", 'am', 'be', 'does', 'until', 'the', 'as', 'are', \"she'll\", 'whom', 'won', 're', 'than', \"it'll\", 'which', \"that'll\", 'where', 'her', 'aren', 'about', 'or', 'why', 'has', 'such', \"we'd\", 'other', 'him', \"haven't\", \"shan't\", 'himself', 'nor', 'shouldn', 'had', 'now', 'from', \"she'd\", 'no', 'further', 'its', 'up', 'only', 'a', 'and', 'how', 'for', 'haven', 'out', 'myself', 'yourself', \"mustn't\", \"couldn't\", 'after', \"he'll\", 'with', 'off', 'once', \"i'm\", 'have', 'd', \"hasn't\", 'then', 'yourselves', 'these', 'at', \"mightn't\", 'mightn', 'it', \"they'll\", 'theirs', 'here', 'under', \"isn't\", 'most', 'both', 'do', 'some', 'll', 'more', 'too', 'his', 'been', \"wouldn't\", \"we've\", 'y', 'just', 'their', 'don', 'i', 'couldn', 'to', 'on', \"aren't\", \"it's\", 'my', 'very', 'each', 'wouldn', 't', 'will', 'should', 'below', 'did', \"you'll\", 'me', 'those', \"he'd\", 'own', \"they're\", \"you'd\", \"i'll\", 'needn', 'of', 'when', 'hers', 'because', 'shan', 'same', 'themselves', 'can', \"we're\", 'hasn', \"shouldn't\", 'your', 'our', 'that', \"weren't\", 'itself', 'doing', \"you've\", 'by', 'above', 'didn', 'during', 'so', 'hadn', 'doesn', \"didn't\", 'she', 's', 'between', 'weren', \"should've\", \"i'd\", 'they', \"don't\", 'into', 'ourselves', 'mustn', \"you're\", 'if', 'who', 've', 'he', 'before', 'we', 'any', 'this', 'ours', 'few', \"needn't\", \"hadn't\"}\n"]}], "source": ["with open(stop_words_vn_txt, 'r', encoding='utf-8') as f:\n", "    stop_words_vi = set(word.strip() for word in f if word.strip())\n", "stop_words_en = set(stopwords.words('english'))\n", "print(stop_words_vi)\n", "print(stop_words_en)"]}, {"cell_type": "code", "execution_count": null, "id": "818115a9", "metadata": {}, "outputs": [], "source": ["df['description_cleaned'] = df.apply(lambda x: clean_text(x['description_en'] if x['language'] == 'en' else x['description'], x['language']), axis=1)\n", "df['requirements_cleaned'] = df.apply(lambda x: clean_text(x['requirements_en'] if x['language'] == 'en' else x['requirements'], x['language']), axis=1)\n", "df['skills_cleaned'] = df['skills_en'].apply(lambda x: clean_skills(x))"]}, {"cell_type": "code", "execution_count": 45, "id": "8f61ec69", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   id                                            title  \\\n", "0   1                                   MLops Engineer   \n", "1   2              Senior DevOps Engineer (Cloud, AWS)   \n", "2   3  VTS - <PERSON><PERSON><PERSON>n <PERSON> (Agile/ Azure)   \n", "3   4       VTS - <PERSON>ư <PERSON>ấn G<PERSON> - Presales Engineer   \n", "4   5    VHT - Embedded Software Engineer (Linux, C++)   \n", "5   6                  Quality Assurance Manager (QAM)   \n", "6   7                Platform Manager (CDN ecosystems)   \n", "7   8   Bridge Project Manager (BrSE/ IT Communicator)   \n", "8   9    Senior Process Quality Assurance (PQA, QA QC)   \n", "9  10     Hybrid - Ruby On Rails Developer (Ruby, SQL)   \n", "\n", "                                             company     location  \\\n", "0                                    Trusting Social  Ho <PERSON>   \n", "1                                              TymeX  Ho Chi Minh   \n", "2                                      Viettel Group       Ha Noi   \n", "3                                      Viettel Group       Ha Noi   \n", "4                                      Viettel Group       Ha Noi   \n", "5  Viettel Software Services (A Member of Viettel...       Ha Noi   \n", "6                                         DatVietVAC  Ho Chi Minh   \n", "7                                      Vitalify Asia  Ho <PERSON>   \n", "8                                           VNDIRECT       Ha Noi   \n", "9                                          MEALSUITE  Ho Chi Minh   \n", "\n", "               salary      work_type  \\\n", "0      You'll love it  Not specified   \n", "1      You'll love it  Not specified   \n", "2      You'll love it  Not specified   \n", "3      You'll love it  Not specified   \n", "4     650 - 2,200 USD  Not specified   \n", "5      You'll love it  Not specified   \n", "6      You'll love it  Not specified   \n", "7      You'll love it  Not specified   \n", "8  Very attractive!!!  Not specified   \n", "9      You'll love it  Not specified   \n", "\n", "                                         description  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3  <PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON>vũ tr<PERSON>” c<PERSON><PERSON> Viettel, n<PERSON><PERSON> bạn k...   \n", "4  <PERSON><PERSON><PERSON> hơn 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "5  X<PERSON>y dựng khung quy trình công ty và chủ trì cả...   \n", "6  X<PERSON><PERSON> dựng khung quy trình công ty và chủ trì cả...   \n", "7  <PERSON><PERSON> ch<PERSON> and こんにちは！, chúng tôi là Vitalify Asi...   \n", "8  1. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> trị khung kiểm soát dự án\\n...   \n", "9  Trung tâm Công nghệ thông tin là đơn vị xây dự...   \n", "\n", "                                        requirements  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3  Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4  Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "5  Tốt nghi<PERSON><PERSON> đạ<PERSON> học về các lĩnh vực <PERSON> nghệ t...   \n", "6  Tốt nghi<PERSON><PERSON> đạ<PERSON> h<PERSON> về các lĩnh vực <PERSON> nghệ t...   \n", "7  SKILL & EXPERIENCE REQUIREMENTS:\\n- Experience...   \n", "8  Tốt nghiệp đại học trở lên.\\nTối thiểu 3 năm k...   \n", "9  Tốt nghiệ<PERSON> họ<PERSON> loại khá trở lên chuyên ngà...   \n", "\n", "                                              skills language  \\\n", "0  MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2  Project Management, Business Analysis, Presale...       vi   \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4  Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "5  PQA, Team Management, QA QC, ISO 27001, IT Aud...       vi   \n", "6  PQA, Team Management, QA QC, ISO 27001, IT Aud...       vi   \n", "7  Bridge Project Management, Japanese, Agile, Pr...       vi   \n", "8  PQA, QA QC, Tester, ISO 27001, IT Audit, Gover...       vi   \n", "9  NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...       vi   \n", "\n", "                                      description_en  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek is leading Ecopark to develop a model o...   \n", "3  Joining Viettel technology, where you are not ...   \n", "4  With more than 1200 high quality personnel, Vi...   \n", "5  Building a company process framework and presi...   \n", "6  Building a company process framework and presi...   \n", "7  Hello and こんにちは！, we are Vitalify Asia, an IT ...   \n", "8  1. Design & Project Control Frame\\nBuilding, o...   \n", "9  The Information Technology Center is a unit in...   \n", "\n", "                                     requirements_en  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Good logical thinking, solution -oriented thin...   \n", "3  Degree: Graduated from university (good or hig...   \n", "4  Graduated with regular university or higher sp...   \n", "5  Graduated from university in the fields of inf...   \n", "6  Graduated from university in the fields of inf...   \n", "7  Skill & Experience Requirements:\\n- Experience...   \n", "8  Graduated from university or higher.\\nAt least...   \n", "9  Graduated from university or higher specialize...   \n", "\n", "                                           skills_en  \\\n", "0  MLOps, Python, Linux, Docker, Data Science, Te...   \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...   \n", "2  Project Management, Business Analysis, Presale...   \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...   \n", "4  Embedded, C++, Linux, C language, Embedded Eng...   \n", "5  PQA, Team Management, QA QC, ISO 27001, IT Aud...   \n", "6  PQA, Team Management, QA QC, ISO 27001, IT Aud...   \n", "7  Bridge Project Management, Japanese, Agile, Pr...   \n", "8  PQA, QA QC, Tester, ISO 27001, IT Audit, Gover...   \n", "9  NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...   \n", "\n", "                                 description_cleaned  \\\n", "0  look qualified mlop engineer ekyc project help...   \n", "1  seek experienced senior devop engineer aw join...   \n", "2  ecotek dẫn dắt ecopark phát triển mô hình thàn...   \n", "3  gia nhập vũ trụ công nghệ viettel đắm chìm hàn...   \n", "4  1200 nhân sự chất lượng tổng công ty công nghi...   \n", "5  xây dựng khung quy trình công ty chủ trì cải t...   \n", "6  xây dựng khung quy trình công ty chủ trì cải t...   \n", "7  chào and こんにちは vitalify asia công ty it trụ sở...   \n", "8  1 thiết kế quản trị khung kiểm soát dự án xây ...   \n", "9  trung tâm công nghệ thông tin xây dựng triển k...   \n", "\n", "                                requirements_cleaned  \\\n", "0  bs ms computer science related field \\n 13 yea...   \n", "1  requirement \\n bachelor master degree computer...   \n", "2  tư duy logic tư duy hướng gi<PERSON>i pháp tư duy phả...   \n", "3  bằng cấp tốt nghiệp đại học trở chuyên ngành c...   \n", "4  tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...   \n", "5  tốt nghiệp đại học lĩnh vực công nghệ thông ti...   \n", "6  tốt nghiệ<PERSON> đại học lĩnh vực công nghệ thông ti...   \n", "7  skill experience requirements experience proje...   \n", "8  tốt nghiệp đại học trở tối thiểu 3 kinh nghiệm...   \n", "9  tốt nghiệp đại học trở chuyên ngành cntt khmt ...   \n", "\n", "                                      skills_cleaned  \n", "0  mlop python linux docker data science tensorfl...  \n", "1  aw devop cloud cloudnative architecture aw clo...  \n", "2  project management business analysis presale p...  \n", "3  presale business analysis salesforce presale e...  \n", "4  embed c linux c language embed engineer teleco...  \n", "5  pqa team management qa qc iso 27001 audit mana...  \n", "6  pqa team management qa qc iso 27001 audit mana...  \n", "7  bridge project management japanese agile proje...  \n", "8  pqa qa qc tester iso 27001 audit governance ri...  \n", "9  nodejs java php oop cicd backend developer tel...  \n"]}], "source": ["print(df.head(10))"]}, {"cell_type": "code", "execution_count": 49, "id": "d6568db8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['<PERSON>', '<PERSON> <PERSON>', 'Others', '<PERSON> <PERSON> <PERSON>',\n", "       '<PERSON>', '<PERSON>',\n", "       '<PERSON>', '<PERSON>',\n", "       '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "       '<PERSON> Nan<PERSON>', '<PERSON>',\n", "       '<PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON> - Others',\n", "       '<PERSON>', 'Thai Nguyen', '<PERSON><PERSON>ng',\n", "       '<PERSON> - Ha <PERSON> - Others', '<PERSON> - Others',\n", "       '<PERSON> - <PERSON>'], dtype=object)"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df['location'].unique()"]}, {"cell_type": "code", "execution_count": 61, "id": "1dcfa1ca", "metadata": {}, "outputs": [], "source": ["locations = np.array([\n", "    '<PERSON>', '<PERSON> <PERSON>', 'Others', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "    '<PERSON>', '<PERSON>',\n", "    '<PERSON>', '<PERSON>',\n", "    '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "    '<PERSON> Nan<PERSON>', '<PERSON>',\n", "    '<PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON> - Others',\n", "    '<PERSON>', 'Thai Nguyen', '<PERSON><PERSON>ng',\n", "    '<PERSON> - Ha <PERSON> - Others', '<PERSON> - Others',\n", "    '<PERSON> - <PERSON> <PERSON>'\n", "])\n", "\n", "main_cities = {'Ha Noi', 'Ho Chi Minh'}\n", "\n", "def classify_location_group(loc_str):\n", "    # Tách và làm sạch địa điểm\n", "    cities = [c.strip() for c in loc_str.split('-')]\n", "    cities_set = set(cities)\n", "\n", "    # Giao giữa cities_set và main_cities\n", "    common_cities = cities_set & main_cities\n", "\n", "    if common_cities == {'Ha Noi'}:\n", "        return 1\n", "    elif common_cities == {'Ho Chi Minh'}:\n", "        return 2\n", "    else:\n", "        return 3"]}, {"cell_type": "code", "execution_count": 64, "id": "a5b5b824", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["location_group\n", "2    551\n", "1    352\n", "3     92\n", "Name: count, dtype: int64\n", "0    2\n", "1    2\n", "2    1\n", "3    1\n", "4    1\n", "5    1\n", "6    2\n", "7    2\n", "8    1\n", "9    2\n", "Name: location_group, dtype: int64\n"]}], "source": ["df['location_group'] = df['location'].apply(lambda x: classify_location_group(x))\n", "print(df['location_group'].value_counts())\n", "print(df['location_group'].head(10))"]}, {"cell_type": "code", "execution_count": 65, "id": "62e17f1a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>company</th>\n", "      <th>location</th>\n", "      <th>salary</th>\n", "      <th>work_type</th>\n", "      <th>description</th>\n", "      <th>requirements</th>\n", "      <th>skills</th>\n", "      <th>language</th>\n", "      <th>description_en</th>\n", "      <th>requirements_en</th>\n", "      <th>skills_en</th>\n", "      <th>description_cleaned</th>\n", "      <th>requirements_cleaned</th>\n", "      <th>skills_cleaned</th>\n", "      <th>location_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>MLops Engineer</td>\n", "      <td>Trusting Social</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>We are looking for qualified MLops Engineer fo...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>en</td>\n", "      <td>We are looking for qualified MLops Engineer fo...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>look qualified mlop engineer ekyc project help...</td>\n", "      <td>bs ms computer science related field \\n 13 yea...</td>\n", "      <td>mlop python linux docker data science tensorfl...</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Senior Dev<PERSON>ps Engineer (Cloud, AWS)</td>\n", "      <td>TymeX</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>We are seeking an experienced Senior DevOps En...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>en</td>\n", "      <td>We are seeking an experienced Senior DevOps En...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>seek experienced senior devop engineer aw join...</td>\n", "      <td>requirement \\n bachelor master degree computer...</td>\n", "      <td>aw devop cloud cloudnative architecture aw clo...</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>VTS - <PERSON><PERSON><PERSON><PERSON><PERSON> (Agile/ Azure)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>Ecotek đang dẫn dắt Ecopark phát triển trở thà...</td>\n", "      <td>T<PERSON> duy logic tốt, tư duy hướ<PERSON> g<PERSON><PERSON>, tư d...</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>vi</td>\n", "      <td>Ecotek is leading Ecopark to develop a model o...</td>\n", "      <td>Good logical thinking, solution -oriented thin...</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>ecotek dẫn dắt ecopark phát triển mô hình thàn...</td>\n", "      <td>tư duy logic tư duy hướng giải pháp tư duy phả...</td>\n", "      <td>project management business analysis presale p...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>VTS - <PERSON><PERSON> - Presales Engineer</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td><PERSON><PERSON> <PERSON>vũ tr<PERSON> công ng<PERSON> Viettel, n<PERSON>i bạn k...</td>\n", "      <td>Bằng cấp: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (loại Khá trở lên...</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>vi</td>\n", "      <td>Joining Viettel technology, where you are not ...</td>\n", "      <td>Degree: Graduated from university (good or hig...</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>gia nh<PERSON><PERSON> vũ trụ công nghệ viettel đắm chìm hàn...</td>\n", "      <td>bằng cấp tốt nghiệp đại học trở chuyên ngành c...</td>\n", "      <td>presale business analysis salesforce presale e...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>VHT - Embedded Software Engineer (Linux, C++)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>650 - 2,200 USD</td>\n", "      <td>Not specified</td>\n", "      <td><PERSON><PERSON><PERSON> 1200 nhân sự chất lư<PERSON>o , Tổng Côn...</td>\n", "      <td><PERSON><PERSON><PERSON> nghi<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>vi</td>\n", "      <td>With more than 1200 high quality personnel, Vi...</td>\n", "      <td>Graduated with regular university or higher sp...</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>1200 nhân sự chất lượng tổng công ty công nghi...</td>\n", "      <td>tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...</td>\n", "      <td>embed c linux c language embed engineer teleco...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>990</th>\n", "      <td>11</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> viên (Hỗ trợ ứng dụng OPN)</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>Not specified</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>specify</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>991</th>\n", "      <td>12</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> viên (Vận hành AM/DevOPs CI/CD)</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>Not specified</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>specify</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>992</th>\n", "      <td>13</td>\n", "      <td><PERSON><PERSON> li<PERSON> (DE) - <PERSON> Enginner</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>Not specified</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>specify</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>993</th>\n", "      <td>14</td>\n", "      <td>CV Phát triển DEV BE/FE/Fullstack</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>Not specified</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>specify</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>994</th>\n", "      <td>15</td>\n", "      <td>CV Phân tích nghi<PERSON> vụ và <PERSON>ử</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specifi</td>\n", "      <td>fr</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>Not specifi</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>specifi</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>995 rows × 17 columns</p>\n", "</div>"], "text/plain": ["     id                                            title  \\\n", "0     1                                   MLops Engineer   \n", "1     2              Senior DevOps Engineer (Cloud, AWS)   \n", "2     3  VTS - <PERSON><PERSON><PERSON>n <PERSON> (Agile/ Azure)   \n", "3     4       VTS - <PERSON>ư <PERSON>ấn G<PERSON> - Presales Engineer   \n", "4     5    VHT - Embedded Software Engineer (Linux, C++)   \n", "..   ..                                              ...   \n", "990  11                <PERSON><PERSON><PERSON><PERSON> viên (Hỗ trợ ứng dụng OPN)   \n", "991  12           <PERSON><PERSON><PERSON><PERSON> viên (Vận hành AM/DevOPs CI/CD)   \n", "992  13               <PERSON><PERSON> s<PERSON> dữ liệu (DE) - <PERSON>ner   \n", "993  14                CV Phát triển DEV BE/FE/Fullstack   \n", "994  15               CV Phân tích nghi<PERSON> v<PERSON> và <PERSON> thử   \n", "\n", "                                        company     location           salary  \\\n", "0                               Trusting Social  Ho Chi Minh   You'll love it   \n", "1                                         TymeX  Ho Chi Minh   You'll love it   \n", "2                                 Viettel Group       Ha Noi   You'll love it   \n", "3                                 Viettel Group       Ha Noi   You'll love it   \n", "4                                 Viettel Group       Ha Noi  650 - 2,200 USD   \n", "..                                          ...          ...              ...   \n", "990  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "991  <PERSON>ân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "992  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "993  <PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "994  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "\n", "         work_type                                        description  \\\n", "0    Not specified  We are looking for qualified MLops Engineer fo...   \n", "1    Not specified  We are seeking an experienced Senior DevOps En...   \n", "2    Not specified  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3    Not specified  <PERSON><PERSON> “vũ tr<PERSON>” công ng<PERSON> Viettel, n<PERSON>i bạn k...   \n", "4    Not specified  <PERSON><PERSON><PERSON>n 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "..             ...                                                ...   \n", "990  Not specified                           No description available   \n", "991  Not specified                           No description available   \n", "992  Not specified                           No description available   \n", "993  Not specified                           No description available   \n", "994  Not specified                           No description available   \n", "\n", "                                          requirements  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3    Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4    Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "..                                                 ...   \n", "990                          No requirements specified   \n", "991                          No requirements specified   \n", "992                          No requirements specified   \n", "993                          No requirements specified   \n", "994                          No requirements specified   \n", "\n", "                                                skills language  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2    Project Management, Business Analysis, Presale...       vi   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "..                                                 ...      ...   \n", "990                                      Not specified       fr   \n", "991                                      Not specified       fr   \n", "992                                      Not specified       fr   \n", "993                                      Not specified       fr   \n", "994                                        Not specifi       fr   \n", "\n", "                                        description_en  \\\n", "0    We are looking for qualified MLops Engineer fo...   \n", "1    We are seeking an experienced Senior DevOps En...   \n", "2    Ecotek is leading Ecopark to develop a model o...   \n", "3    Joining Viettel technology, where you are not ...   \n", "4    With more than 1200 high quality personnel, Vi...   \n", "..                                                 ...   \n", "990                           No description available   \n", "991                           No description available   \n", "992                           No description available   \n", "993                           No description available   \n", "994                           No description available   \n", "\n", "                                       requirements_en  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Good logical thinking, solution -oriented thin...   \n", "3    Degree: Graduated from university (good or hig...   \n", "4    Graduated with regular university or higher sp...   \n", "..                                                 ...   \n", "990                          No requirements SPECIFied   \n", "991                          No requirements SPECIFied   \n", "992                          No requirements SPECIFied   \n", "993                          No requirements SPECIFied   \n", "994                          No requirements SPECIFied   \n", "\n", "                                             skills_en  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...   \n", "2    Project Management, Business Analysis, Presale...   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...   \n", "..                                                 ...   \n", "990                                      Not specified   \n", "991                                      Not specified   \n", "992                                      Not specified   \n", "993                                      Not specified   \n", "994                                        Not specifi   \n", "\n", "                                   description_cleaned  \\\n", "0    look qualified mlop engineer ekyc project help...   \n", "1    seek experienced senior devop engineer aw join...   \n", "2    ecotek dẫn dắt ecopark phát triển mô hình thàn...   \n", "3    gia nhập vũ trụ công nghệ viettel đắm chìm hàn...   \n", "4    1200 nhân sự chất lượng tổng công ty công nghi...   \n", "..                                                 ...   \n", "990                           no description available   \n", "991                           no description available   \n", "992                           no description available   \n", "993                           no description available   \n", "994                           no description available   \n", "\n", "                                  requirements_cleaned  \\\n", "0    bs ms computer science related field \\n 13 yea...   \n", "1    requirement \\n bachelor master degree computer...   \n", "2    tư duy logic tư duy hướng gi<PERSON>i pháp tư duy phả...   \n", "3    bằng cấp tốt nghiệp đại học trở chuyên ngành c...   \n", "4    tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...   \n", "..                                                 ...   \n", "990                          no requirements specified   \n", "991                          no requirements specified   \n", "992                          no requirements specified   \n", "993                          no requirements specified   \n", "994                          no requirements specified   \n", "\n", "                                        skills_cleaned  location_group  \n", "0    mlop python linux docker data science tensorfl...               2  \n", "1    aw devop cloud cloudnative architecture aw clo...               2  \n", "2    project management business analysis presale p...               1  \n", "3    presale business analysis salesforce presale e...               1  \n", "4    embed c linux c language embed engineer teleco...               1  \n", "..                                                 ...             ...  \n", "990                                            specify               1  \n", "991                                            specify               1  \n", "992                                            specify               1  \n", "993                                            specify               1  \n", "994                                            specifi               1  \n", "\n", "[995 rows x 17 columns]"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}