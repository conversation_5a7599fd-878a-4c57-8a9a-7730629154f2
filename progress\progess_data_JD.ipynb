{"cells": [{"cell_type": "markdown", "id": "e953e889", "metadata": {}, "source": ["## Python version 11."]}, {"cell_type": "code", "execution_count": 103, "id": "943b40e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pandas in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (2.3.0)\n", "Requirement already satisfied: langdetect in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (1.0.9)\n", "Requirement already satisfied: deep_translator in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (1.11.4)\n", "Requirement already satisfied: spacy in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (3.8.7)\n", "Requirement already satisfied: underthesea in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (6.8.4)\n", "Requirement already satisfied: nltk in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (3.9.1)\n", "Requirement already satisfied: numpy>=1.23.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2.3.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas) (2025.2)\n", "Requirement already satisfied: six in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from langdetect) (1.17.0)\n", "Requirement already satisfied: beautifulsoup4<5.0.0,>=4.9.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from deep_translator) (4.13.4)\n", "Requirement already satisfied: requests<3.0.0,>=2.23.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from deep_translator) (2.32.4)\n", "Requirement already satisfied: soupsieve>1.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from beautifulsoup4<5.0.0,>=4.9.1->deep_translator) (2.7)\n", "Requirement already satisfied: typing-extensions>=4.0.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from beautifulsoup4<5.0.0,>=4.9.1->deep_translator) (4.14.0)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from requests<3.0.0,>=2.23.0->deep_translator) (2025.6.15)\n", "Requirement already satisfied: spacy-legacy<3.1.0,>=3.0.11 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.0.12)\n", "Requirement already satisfied: spacy-loggers<2.0.0,>=1.0.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (1.0.5)\n", "Requirement already satisfied: murmurhash<1.1.0,>=0.28.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (1.0.13)\n", "Requirement already satisfied: cymem<2.1.0,>=2.0.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.0.11)\n", "Requirement already satisfied: preshed<3.1.0,>=3.0.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.0.10)\n", "Requirement already satisfied: thinc<8.4.0,>=8.3.4 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (8.3.6)\n", "Requirement already satisfied: wasabi<1.2.0,>=0.9.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (1.1.3)\n", "Requirement already satisfied: srsly<3.0.0,>=2.4.3 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.5.1)\n", "Requirement already satisfied: catalogue<2.1.0,>=2.0.6 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.0.10)\n", "Requirement already satisfied: weasel<0.5.0,>=0.1.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (0.4.1)\n", "Requirement already satisfied: typer<1.0.0,>=0.3.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (0.16.0)\n", "Requirement already satisfied: tqdm<5.0.0,>=4.38.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (4.67.1)\n", "Requirement already satisfied: pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (2.11.7)\n", "Requirement already satisfied: jinja2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.1.6)\n", "Requirement already satisfied: setuptools in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (65.5.0)\n", "Requirement already satisfied: packaging>=20.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (25.0)\n", "Requirement already satisfied: langcodes<4.0.0,>=3.2.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from spacy) (3.5.0)\n", "Requirement already satisfied: language-data>=1.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from langcodes<4.0.0,>=3.2.0->spacy) (1.3.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pydantic!=1.8,!=1.8.1,<3.0.0,>=1.7.4->spacy) (0.4.1)\n", "Requirement already satisfied: blis<1.4.0,>=1.3.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from thinc<8.4.0,>=8.3.4->spacy) (1.3.0)\n", "Requirement already satisfied: confection<1.0.0,>=0.0.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from thinc<8.4.0,>=8.3.4->spacy) (0.1.5)\n", "Requirement already satisfied: colorama in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from tqdm<5.0.0,>=4.38.0->spacy) (0.4.6)\n", "Requirement already satisfied: click>=8.0.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from typer<1.0.0,>=0.3.0->spacy) (8.2.1)\n", "Requirement already satisfied: shellingham>=1.3.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from typer<1.0.0,>=0.3.0->spacy) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from typer<1.0.0,>=0.3.0->spacy) (14.0.0)\n", "Requirement already satisfied: cloudpathlib<1.0.0,>=0.7.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from weasel<0.5.0,>=0.1.0->spacy) (0.21.1)\n", "Requirement already satisfied: smart-open<8.0.0,>=5.2.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from weasel<0.5.0,>=0.1.0->spacy) (7.1.0)\n", "Requirement already satisfied: wrapt in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from smart-open<8.0.0,>=5.2.1->weasel<0.5.0,>=0.1.0->spacy) (1.17.2)\n", "Requirement already satisfied: python-crfsuite>=0.9.6 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (0.9.11)\n", "Requirement already satisfied: joblib in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (1.5.1)\n", "Requirement already satisfied: scikit-learn in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (1.7.0)\n", "Requirement already satisfied: PyYAML in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (6.0.2)\n", "Requirement already satisfied: underthesea-core==1.0.4 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from underthesea) (1.0.4)\n", "Requirement already satisfied: regex>=2021.8.3 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from nltk) (2024.11.6)\n", "Requirement already satisfied: marisa-trie>=1.1.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from language-data>=1.2->langcodes<4.0.0,>=3.2.0->spacy) (1.2.1)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy) (2.19.1)\n", "Requirement already satisfied: mdurl~=0.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0.0,>=0.3.0->spacy) (0.1.2)\n", "Requirement already satisfied: MarkupSafe>=2.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from jinja2->spacy) (3.0.2)\n", "Requirement already satisfied: scipy>=1.8.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from scikit-learn->underthesea) (1.15.3)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from scikit-learn->underthesea) (3.6.0)\n"]}], "source": ["!pip install pandas langdetect deep_translator spacy underthesea nltk"]}, {"cell_type": "code", "execution_count": 104, "id": "1c57442c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting en-core-web-md==3.8.0\n", "  Downloading https://github.com/explosion/spacy-models/releases/download/en_core_web_md-3.8.0/en_core_web_md-3.8.0-py3-none-any.whl (33.5 MB)\n", "     ---------------------------------------- 0.0/33.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 3.9/33.5 MB 33.7 MB/s eta 0:00:01\n", "     ------------------ -------------------- 16.3/33.5 MB 48.8 MB/s eta 0:00:01\n", "     ------------------------------ -------- 26.2/33.5 MB 48.9 MB/s eta 0:00:01\n", "     --------------------------------------  33.3/33.5 MB 49.2 MB/s eta 0:00:01\n", "     --------------------------------------- 33.5/33.5 MB 43.4 MB/s eta 0:00:00\n", "\u001b[38;5;2m✔ Download and installation successful\u001b[0m\n", "You can now load the package via spacy.load('en_core_web_md')\n"]}], "source": ["!python -m spacy download en_core_web_md"]}, {"cell_type": "code", "execution_count": 105, "id": "f912b9c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: matplotlib in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (3.10.3)\n", "Requirement already satisfied: seaborn in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (0.13.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (1.4.8)\n", "Requirement already satisfied: numpy>=1.23 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (2.3.0)\n", "Requirement already satisfied: packaging>=20.0 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (25.0)\n", "Requirement already satisfied: pillow>=8 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from matplotlib) (2.9.0.post0)\n", "Requirement already satisfied: pandas>=1.2 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from seaborn) (2.3.0)\n", "Requirement already satisfied: pytz>=2020.1 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from pandas>=1.2->seaborn) (2025.2)\n", "Requirement already satisfied: six>=1.5 in k:\\customer\\hoangtu\\.venv\\lib\\site-packages (from python-dateutil>=2.7->matplotlib) (1.17.0)\n"]}], "source": ["!pip install matp<PERSON><PERSON>b seaborn"]}, {"cell_type": "code", "execution_count": 106, "id": "d81e86d7", "metadata": {}, "outputs": [], "source": ["# Import c<PERSON><PERSON> thư viện c<PERSON>n thiết\n", "import pandas as pd\n", "from langdetect import detect\n", "from deep_translator import GoogleTranslator\n", "import re\n", "import spacy\n", "from underthesea import word_tokenize, pos_tag\n", "from nltk.corpus import stopwords\n", "import numpy as np\n", "from collections import defaultdict\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from collections import Counter\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 107, "id": "23a0e1ff", "metadata": {}, "outputs": [], "source": ["nlp_en = spacy.load('en_core_web_md')\n", "stop_words_en = None\n", "stop_words_vi = None"]}, {"cell_type": "code", "execution_count": 108, "id": "04cdb790", "metadata": {}, "outputs": [], "source": ["# Đường dẫn đến file csv chứa các job description\n", "csv_jd = \"../data/raw/itviec_jobs_undetected.csv\"\n", "stop_words_vn_txt = \"../docs/vietnamese-stopwords.txt\""]}, {"cell_type": "code", "execution_count": 109, "id": "2c5413fc", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(csv_jd)"]}, {"cell_type": "code", "execution_count": 110, "id": "165836c4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   id                                            title  \\\n", "0   1                                   MLops Engineer   \n", "1   2              Senior DevOps Engineer (Cloud, AWS)   \n", "2   3  VTS - <PERSON><PERSON><PERSON>n <PERSON> (Agile/ Azure)   \n", "3   4       VTS - <PERSON>ư <PERSON>ấn G<PERSON> - Presales Engineer   \n", "4   5    VHT - Embedded Software Engineer (Linux, C++)   \n", "5   6                  Quality Assurance Manager (QAM)   \n", "6   7                Platform Manager (CDN ecosystems)   \n", "7   8   Bridge Project Manager (BrSE/ IT Communicator)   \n", "8   9    Senior Process Quality Assurance (PQA, QA QC)   \n", "9  10     Hybrid - Ruby On Rails Developer (Ruby, SQL)   \n", "\n", "                                             company     location  \\\n", "0                                    Trusting Social  Ho <PERSON>   \n", "1                                              TymeX  Ho Chi Minh   \n", "2                                      Viettel Group       Ha Noi   \n", "3                                      Viettel Group       Ha Noi   \n", "4                                      Viettel Group       Ha Noi   \n", "5  Viettel Software Services (A Member of Viettel...       Ha Noi   \n", "6                                         DatVietVAC  Ho Chi Minh   \n", "7                                      Vitalify Asia  Ho <PERSON>   \n", "8                                           VNDIRECT       Ha Noi   \n", "9                                          MEALSUITE  Ho Chi Minh   \n", "\n", "               salary      work_type  \\\n", "0      You'll love it  Not specified   \n", "1      You'll love it  Not specified   \n", "2      You'll love it  Not specified   \n", "3      You'll love it  Not specified   \n", "4     650 - 2,200 USD  Not specified   \n", "5      You'll love it  Not specified   \n", "6      You'll love it  Not specified   \n", "7      You'll love it  Not specified   \n", "8  Very attractive!!!  Not specified   \n", "9      You'll love it  Not specified   \n", "\n", "                                         description  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3  <PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON>vũ tr<PERSON>” c<PERSON><PERSON> Viettel, n<PERSON><PERSON> bạn k...   \n", "4  <PERSON><PERSON><PERSON> hơn 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "5  X<PERSON>y dựng khung quy trình công ty và chủ trì cả...   \n", "6  X<PERSON><PERSON> dựng khung quy trình công ty và chủ trì cả...   \n", "7  <PERSON><PERSON> ch<PERSON> and こんにちは！, chúng tôi là Vitalify Asi...   \n", "8  1. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> trị khung kiểm soát dự án\\n...   \n", "9  Trung tâm Công nghệ thông tin là đơn vị xây dự...   \n", "\n", "                                        requirements  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3  Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4  Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "5  Tốt nghi<PERSON><PERSON> đạ<PERSON> học về các lĩnh vực <PERSON> nghệ t...   \n", "6  Tốt nghi<PERSON><PERSON> đạ<PERSON> h<PERSON> về các lĩnh vực <PERSON> nghệ t...   \n", "7  SKILL & EXPERIENCE REQUIREMENTS:\\n- Experience...   \n", "8  Tốt nghiệp đại học trở lên.\\nTối thiểu 3 năm k...   \n", "9  Tốt nghiệ<PERSON> họ<PERSON> loại khá trở lên chuyên ngà...   \n", "\n", "                                              skills  \n", "0  MLOps, Python, Linux, Docker, Data Science, Te...  \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...  \n", "2  Project Management, Business Analysis, Presale...  \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...  \n", "4  Embedded, C++, Linux, C language, Embedded Eng...  \n", "5  PQA, Team Management, QA QC, ISO 27001, IT Aud...  \n", "6  PQA, Team Management, QA QC, ISO 27001, IT Aud...  \n", "7  Bridge Project Management, Japanese, Agile, Pr...  \n", "8  PQA, QA QC, Tester, ISO 27001, IT Audit, Gover...  \n", "9  NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...  \n"]}], "source": ["print(df.head(10))"]}, {"cell_type": "code", "execution_count": 111, "id": "adf045bf", "metadata": {}, "outputs": [], "source": ["def detect_language(text):\n", "    try:\n", "        return detect(text)\n", "    except:\n", "        return 'unknown'\n", "def translate_to_english(text, lang):\n", "    try:\n", "        if lang == 'en' or pd.isna(text) or text.strip() == \"\":\n", "            return text\n", "        return GoogleTranslator(source=lang, target='en').translate(text)\n", "    except Exception as e:\n", "        print(f\"Error translating: {e} | text: {text}\")\n", "        return text\n", "def clean_text(text, lang='en'):\n", "    if not isinstance(text, str) or not text.strip():\n", "        return ''\n", "    text = text.lower()\n", "    text = re.sub(r'http\\S+|#\\S+|@\\S+|[^\\w\\s]|\\n|\\r|\\t|\\*|\\•', ' ', text)\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    if lang == 'en':\n", "        doc = nlp_en(text)\n", "        tokens = [token.lemma_ for token in doc if token.text not in stop_words_en]\n", "        return ' '.join(tokens)\n", "    else:\n", "        tokens = word_tokenize(text)\n", "        tokens = [t for t in tokens if t not in stop_words_vi]\n", "        return ' '.join(tokens)\n", "def clean_skills(text):\n", "    text.lower()\n", "    if pd.isna(text) or text.strip() == \"\":\n", "        return []\n", "    return [t.strip() for t in text.split(',') if t.strip()]"]}, {"cell_type": "code", "execution_count": 112, "id": "2f57f34e", "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> dụng cho cột 'description'\n", "df['language'] = df['description'].apply(detect_language)\n", "df['skills_en'] = df['skills']"]}, {"cell_type": "code", "execution_count": 113, "id": "c5520aac", "metadata": {}, "outputs": [], "source": ["df['description_en'] = df.apply(lambda x: translate_to_english(x['description'], x['language']), axis=1)\n", "df['requirements_en'] = df.apply(lambda x: translate_to_english(x['requirements'], x['language']), axis=1)"]}, {"cell_type": "code", "execution_count": 114, "id": "322b6d68", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   id                                            title          company  \\\n", "0   1                                   MLops Engineer  Trusting Social   \n", "1   2              Senior DevOps Engineer (Cloud, AWS)            TymeX   \n", "2   3  VTS - <PERSON><PERSON>ên <PERSON> (Agile/ Azure)    Viettel Group   \n", "3   4       VTS - <PERSON><PERSON> G<PERSON> - Presales Engineer    Viettel Group   \n", "4   5    VHT - Embedded Software Engineer (Linux, C++)    Viettel Group   \n", "\n", "      location           salary      work_type  \\\n", "0  <PERSON> <PERSON> Minh   You'll love it  Not specified   \n", "1  <PERSON> Chi Minh   You'll love it  Not specified   \n", "2       Ha Noi   You'll love it  Not specified   \n", "3       Ha Noi   You'll love it  Not specified   \n", "4       Ha Noi  650 - 2,200 USD  Not specified   \n", "\n", "                                         description  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3  <PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON>vũ tr<PERSON>” c<PERSON><PERSON> Viettel, n<PERSON><PERSON> bạn k...   \n", "4  <PERSON><PERSON><PERSON> hơn 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "\n", "                                        requirements  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3  Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4  Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "\n", "                                              skills language  \\\n", "0  MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2  Project Management, Business Analysis, Presale...       vi   \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4  Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "\n", "                                           skills_en  \\\n", "0  MLOps, Python, Linux, Docker, Data Science, Te...   \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...   \n", "2  Project Management, Business Analysis, Presale...   \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...   \n", "4  Embedded, C++, Linux, C language, Embedded Eng...   \n", "\n", "                                      description_en  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek is leading Ecopark to develop a model o...   \n", "3  Joining Viettel technology, where you are not ...   \n", "4  With more than 1200 high quality personnel, Vi...   \n", "\n", "                                     requirements_en  \n", "0  BS or MS in Computer Science or related fields...  \n", "1  Requirements:\\nBachelor's or Master's degree i...  \n", "2  Good logical thinking, solution -oriented thin...  \n", "3  Degree: Graduated from university (good or hig...  \n", "4  Graduated with regular university or higher sp...  \n"]}], "source": ["print(df.head())"]}, {"cell_type": "code", "execution_count": 115, "id": "d832b02d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'nhóm', 'trong đó', 'hơn là', 'hay sao', 'lần tìm', 'trướ<PERSON>', 'tính từ', 'bằng', 'nếu được', 'ôi chao', 'lớn', 'đúng ra', 'trệt', 'không cứ', 'đang thì', 'bỏ', 'làm như', 'thế là', 'cho biết', 'không khỏi', 'thục mạng', 'ngày rày', 'cô mình', 'tới gần', 'bước đi', 'chung cục', 'nữa là', 'thường bị', 'ra ý', 'từ loại', 'giờ này', 'tanh tanh', 'lấy có', 'cùng với', 'bên có', 'mọi lúc', 'bởi sao', 'cậu', 'đầy phè', 'ơ', 'rõ', 'ngọn nguồn', 'lại giống', 'cao lâu', 'lấy thế', 'chết nỗi', 'thi thoảng', 'cho ăn', 'thửa', 'l<PERSON>y vào', 'thật thà', 'bằng không', 'duy', 'tất cả', 'thấp cơ', 'hãy', 'xệp', 'một khi', 'bấy giờ', 'suýt nữa', 'nói với', 'ít thôi', 'dì', 'ngày', 'ngày này', 'à này', 'không tính', 'đến', 'bất chợt', 'hay nhỉ', 'dành dành', 'đánh giá', 'nói trước', 'ngay lúc này', 'gặp', 'rồi đây', 'ba ngày', 'nhân tiện', 'cùng cực', 'lấy được', 'chỉ tên', 'gần hết', 'khác gì', 'ba ba', 'thế chuẩn bị', 'có điều kiện', 'ngoài ra', 'tại sao', 'cho đến khi', 'loại', 'cô ấy', 'trực tiếp', 'thái quá', 'nói thật', 'ăn làm', 'phần sau', 'đặt để', 'bởi đâu', 'cái gì', 'cao', 'nhà', 'không có gì', 'từng phần', 'ô hô', 'giờ', 'khá tốt', 'áng như', 'có nhà', 'béng', 'ông tạo', 'thực vậy', 'nhìn', 'vở', 'khỏi nói', 'vào vùng', 'cao thấp', 'bộ thuộc', 'đến khi', 'chợt nhìn', 'ư', 'bản ý', 'rốt cuộc', 'ơi', 'cho đến', 'đến đâu', 'bỗng dưng', 'này nọ', 'không những', 'cách không', 'biết thế', 'người', 'sau chót', 'thấy', 'thảo nào', 'làm tắp lự', 'quá tin', 'vậy ra', 'ừ ừ', 'ít biết', 'xa cách', 'vô vàn', 'trước tiên', 'chính bản', 'anh', 'ngày nào', 'đầy', 'trước tuổi', 'mà không', 'lấy sau', 'căn cái', 'mang mang', 'ra người', 'cứ điểm', 'thỏm', 'nhà việc', 'ngay cả', 'chưa dễ', 'mỗi lúc', 'bây nhiêu', 'bản thân', 'chăng nữa', 'sáng ý', 'nói qua', 'thường thôi', 'tọt', 'là cùng', 'phứt', 'khi nên', 'sẽ hay', 'tỏ vẻ', 'bắt đầu', 'tạo cơ hội', 'bấy nay', 'lâu', 'cây nước', 'vèo vèo', 'lại cái', 'gần', 'bỗng nhưng', 'bị', 'phía trong', 'phải chi', 'dễ', 'trỏng', 'đâu phải', 'nớ', 'xử lý', 'thếch', 'song le', 'ngoài này', 'ở được', 'chính giữa', 'thường đến', 'vài điều', 'vốn dĩ', 'thuộc bài', 'do vậy', 'thuần ái', 'lần này', 'nhờ', 'nghĩ đến', 'tính người', 'thế đó', 'rõ là', 'chỉ có', 'nhờ có', 'ra', 'bên bị', 'chơi họ', 'ngoài xa', 'ráo nước', 'cật lực', 'tiếp đó', 'gây giống', 'đến nay', 'cứ', 'tênh tênh', 'nóc', 'thường sự', 'chọn bên', 'lời nói', 'lại người', 'dữ cách', 'chuyển', 'nhận thấy', 'thậm chí', 'nước quả', 'bỗng thấy', 'áng', 'ôi thôi', 'dễ thấy', 'lúc sáng', 'lần', 'ơ hay', 'đưa tin', 'lòng không', 'nghe chừng', 'nói', 'qua tay', 'tại đây', 'bằng người', 'lần lần', 'tay quay', 'khỏi', 'xin gặp', 'nói bông', 'ba bản', 'duy có', 'đủ điểm', 'đại để', 'thanh điều kiện', 'phía sau', 'chứ không', 'sẽ', 'ngay khi', 'cho', 'hết nói', 'cha', 'không điều kiện', 'gì đó', 'đưa cho', 'nhất luật', 'bước khỏi', 'không ai', 'người hỏi', 'ông từ', 'sao cho', 'biết mấy', 'nữa khi', 'thực sự', 'dần dà', 'quay lại', 'ngày nọ', 'vị tất', 'biết đâu đấy', 'sự', 'tự cao', 'nhất sinh', 'bội phần', 'hay đâu', 'thanh', 'câu hỏi', 'bài', 'hiện nay', 'đưa đến', 'nhận việc', 'thay đổi tình trạng', 'mợ', 'mỗi lần', 'luôn luôn', 'cùng tột', 'đã hay', 'quả thế', 'quá lời', 'tháng ngày', 'có cơ', 'nhằm', 'tốt bạn', 'cho tới', 'phần lớn', 'bao lâu', 'không gì', 'lúc nào', 'để mà', 'chị', 'thế nào', 'sau nữa', 'cũng', 'amen', 'như là', 'nghĩ ra', 'thực ra', 'nếu cần', 'cuối cùng', 'về không', 'bỏ riêng', 'tính cách', 'đối với', 'ăn chịu', 'thanh chuyển', 'giữ', 'ngay', 'dầu sao', 'năm', 'chung chung', 'bằng nấy', 'nghe được', 'chung', 'mang lại', 'trước đó', 'chẳng những', 'đáng lí', 'thực hiện', 'đến xem', 'bển', 'xăm xắm', 'chầm chập', 'một cách', 'giống người', 'xem lại', 'thay đổi', 'bất kỳ', 'thực hiện đúng', 'lần theo', 'dành', 'bỏ mẹ', 'nói thêm', 'rứa', 'theo tin', 'bấy lâu nay', 'còn về', 'ăn chung', 'hơn nữa', 'tôi', 'nức nở', 'mất', 'có điều', 'cần gì', 'tấm bản', 'nhân dịp', 'ông ổng', 'lại còn', 'ví phỏng', 'từ tại', 'nguồn', 'tự ăn', 'hết ráo', 'đến nơi', 'chùn chũn', 'hay làm', 'ăn chắc', 'xoẹt', 'ối dào', 'không phải', 'là thế nào', 'tìm cách', 'chuyển tự', 'thời gian', 'sang', 'cách bức', 'vài ba', 'nghĩ lại', 'nhìn xuống', 'lên xuống', 'mối', 'thì giờ', 'nhằm để', 'đang', 'nếu thế', 'phải như', 'không', 'ngọt', 'khó nghĩ', 'dở chừng', 'với nhau', 'sử dụng', 'tuy có', 'đưa vào', 'chịu lời', 'cái họ', 'này', 'lúc', 'lời chú', 'sau cùng', 'cái ấy', 'tới', 'chung qui', 'sau cuối', 'dào', 'thích cứ', 'không thể', 'nhìn lại', 'nhà tôi', 'nào đó', 'ầu ơ', 'cấp trực tiếp', 'khó khăn', 'tuổi', 'ồ', 'cùng ăn', 'đưa về', 'rồi thì', 'thà', 'trước hết', 'á', 'cảm ơn', 'thế lại', 'của ngọt', 'vậy nên', 'dài ra', 'quả thật', 'úi', 'nên làm', 'tức thì', 'vượt quá', 'đầu tiên', 'giống nhau', 'biết đâu', 'chưa chắc', 'càng', 'pho', 'mọi thứ', 'tìm', 'đến nỗi', 'cũng được', 'cấp số', 'làm gì', 'tắp tắp', 'lúc khác', 'dễ thường', 'ái dà', 'cũng nên', 'thế thì', 'như ai', 'do', 'ổng', 'nhanh lên', 'cần', 'nước ăn', 'cô quả', 'bấy chừ', 'chợt nghe', 'nghĩ', 'rén bước', 'nói toẹt', 'dùng hết', 'nghe tin', 'vâng dạ', 'chung cho', 'lý do', 'giờ đây', 'phải khi', 'thường thường', 'lần sau', 'kể như', 'mỗi ngày', 'dù dì', 'nói tốt', 'còn', 'đủ nơi', 'con tính', 'tắp', 'lại', 'xiết bao', 'giờ đến', 'cao thế', 'đã lâu', 'sở dĩ', 'trệu trạo', 'ai nấy', 'số phần', 'trên', 'biết chắc', 'dù gì', 'bản', 'thật', 'đến điều', 'số cho biết', 'ào', 'bằng như', 'chưa cần', 'từng cái', 'tuổi tôi', 'từng thời gian', 'đảm bảo', 'chúng ta', 'có nhiều', 'sáng thế', 'cha chả', 'ắt', 'bán', 'cùng tuổi', 'nhận nhau', 'bỗng chốc', 'khi trước', 'tạo', 'đặt làm', 'dễ dùng', 'hoặc là', 'cho đến nỗi', 'nếu', 'tính', 'bây bẩy', 'biết trước', 'nghe đâu như', 'tuy là', 'mọi việc', 'khá', 'như thế nào', 'bay biến', 'bất nhược', 'sau này', 'chứ sao', 'lấy số', 'nơi nơi', 'chứ không phải', 'sất', 'bao giờ', 'việc', 'nghĩ xa', 'ở đây', 'mỗi', 'bởi thế', 'ngày xưa', 'cả năm', 'tạo nên', 'tuyệt nhiên', 'mang', 'chốc chốc', 'dễ đâu', 'ba họ', 'có', 'sự việc', 'bệt', 'không cần', 'rồi ra', 'chành chạnh', 'không có', 'ba tăng', 'căn cắt', 'tới nơi', 'phóc', 'cu cậu', 'hơn hết', 'cho tới khi', 'con', 'biết đâu chừng', 'hết cả', 'tới mức', 'họ', 'tại', 'chớ chi', 'đến ngày', 'cho thấy', 'nhất định', 'ngày ngày', 'và', 'chí chết', 'sang tay', 'bỗng không', 'tăng giảm', 'trong ngoài', 'để lại', 'tự vì', 'phải cách', 'gần đến', 'giống như', 'nặng mình', 'trong', 'nói lại', 'chúng mình', 'thanh tính', 'sau sau', 'làm lại', 'xa', 'ba', 'đầy tuổi', 'trong lúc', 'ớ', 'bị chú', 'điều gì', 'ngay thật', 'tự tính', 'chẳng phải', 'nhớ lại', 'lâu ngày', 'nhớ bập bõm', 'một cơn', 'phè', 'ắt là', 'so', 'thật quả', 'ơi là', 'quả là', 'đầy năm', 'bán thế', 'cụ thể là', 'làm đúng', 'ra sao', 'ối giời', 'nào hay', 'nấy', 'bập bà bập bõm', 'giữa', 'dẫu sao', 'phải chăng', 'xa nhà', 'dùng cho', 'giữa lúc', 'bởi nhưng', 'gần như', 'ứ hự', 'rằng là', 'phần nào', 'số', 'tăm tắp', 'úi chà', 'thêm giờ', 'tránh xa', 'bất giác', 'quay', 'thế sự', 'nói nhỏ', 'điểm chính', 'đặt ra', 'bằng nhau', 'lên', 'chúng tôi', 'biết được', 'theo', 'veo veo', 'chứ', 'đến lúc', 'buổi mới', 'cây', 'có chứ', 'giảm thấp', 'không bao lâu', 'lời', 'ủa', 'chúng', 'trừ phi', 'nói đủ', 'mọi người', 'nhanh', 'vèo', 'chọn ra', 'về sau', 'bỏ việc', 'ngôi', 'bởi thế cho nên', 'vả lại', 'điều kiện', 'lên cao', 'cách', 'tớ', 'nọ', 'nước lên', 'tông tốc', 'nhược bằng', 'ba ngôi', 'nước nặng', 'ngày giờ', 'quan tâm', 'quan trọng', 'đáng kể', 'lượng từ', 'một', 'bị vì', 'bỏ cuộc', 'tanh', 'khác thường', 'tháng tháng', 'rén', 'nếu có', 'tin vào', 'rốt cục', 'chao ôi', 'ăn ngồi', 'nghe trực tiếp', 'từ nay', 'sau', 'rồi xem', 'thường tại', 'nói rõ', 'đành đạch', 'sang sáng', 'chung cuộc', 'thình lình', 'cũng vậy thôi', 'khác nào', 'nghe hiểu', 'nhớ lấy', 'tăng thêm', 'quá đáng', 'than ôi', 'sẽ biết', 'khác xa', 'từng', 'cả người', 'cuối điểm', 'bằng cứ', 'quá ư', 'như thế', 'ra bộ', 'từ điều', 'nước xuống', 'ra lại', 'bao nả', 'chưa kể', 'chịu chưa', 'giá trị', 'chùn chùn', 'tự khi', 'nghen', 'vùng lên', 'không đầy', 'ồ ồ', 'người nghe', 'ăn riêng', 'ăn tay', 'gồm', 'làm ra', 'đưa tới', 'như quả', 'cần số', 'đúng với', 'để đến nỗi', 'nói chung', 'trở thành', 'chuẩn bị', 'ăn sáng', 'đã thế', 'nói riêng', 'cảm thấy', 'tắp lự', 'đáo để', 'tối ư', 'nhỡ ra', 'lâu các', 'ý da', 'hoàn toàn', 'bởi tại', 'vì', 'tuần tự', 'dễ ngươi', 'cùng chung', 'lại đây', 'gặp khó khăn', 'ở như', 'bỏ mất', 'không chỉ', 'lấy', 'tăng', 'ví bằng', 'giá trị thực tế', 'nghe thấy', 'tên họ', 'tốt ngày', 'những lúc', 'nhà làm', 'biết việc', 'lên cơn', 'thích thuộc', 'tại vì', 'rồi sao', 'tựu trung', 'nhìn theo', 'tênh', 'thoạt nghe', 'cổ lai', 'tuốt tuồn tuột', 'chăng chắc', 'lượng cả', 'quay bước', 'quá trình', 'đặt mình', 'thì ra', 'chưa dùng', 'nhìn chung', 'ít nhất', 'a ha', 'có phải', 'cả thể', 'nghe', 'thoạt nhiên', 'chu cha', 'bức', 'vấn đề quan trọng', 'dễ ăn', 'đáng số', 'không dùng', 'để không', 'cả ngày', 'ba cùng', 'để cho', 'ngôi thứ', 'lần nào', 'khó', 'chứ còn', 'ít hơn', 'qua lại', 'ạ', 'người nhận', 'không bao giờ', 'thường xuất hiện', 'cũng thế', 'cả thảy', 'quay số', 'mang nặng', 'trả ngay', 'ai ai', 'thật vậy', 'đâu đó', 'theo bước', 'do vì', 'chớ kể', 'khoảng không', 'đây đó', 'thậm từ', 'cho được', 'tha hồ chơi', 'cụ thể', 'ờ ờ', 'vô luận', 'hỏi', 'nhón nhén', 'với lại', 'nhất tề', 'cấp', 'vậy', 'đâu đây', 'thật lực', 'nhất nhất', 'chịu tốt', 'phải', 'chưa có', 'nhớ', 'dễ nghe', 'ra điều', 'ở lại', 'phía trước', 'làm tin', 'cao sang', 'ra vào', 'thấp thỏm', 'ý', 'vạn nhất', 'alô', 'nhé', 'biết chừng nào', 'chăn chắn', 'bỗng', 'sáng', 'ở', 'thốt', 'làm dần dần', 'xin vâng', 'không ngoài', 'qua khỏi', 'phía', 'từ từ', 'vì chưng', 'ở đó', 'coi mòi', 'dù cho', 'sáng ngày', 'nhằm khi', 'lòng', 'đại nhân', 'mất còn', 'nói đến', 'bập bõm', 'là', 'lên số', 'ý chừng', 'quá tuổi', 'chính', 'khiến', 'nhiều', 'nên người', 'với', 'vài người', 'vừa khi', 'điều', 'khó làm', 'loại từ', 'tên tự', 'chia sẻ', 'nền', 'hơn cả', 'lấy giống', 'trả trước', 'xon xón', 'chủn', 'cái đã', 'ào ào', 'nay', 'kể', 'càng hay', 'đã là', 'chắc ăn', 'quả vậy', 'xem số', 'ừ ào', 'lấy ra', 'là vì', 'thời điểm', 'chậc', 'vụt', 'ờ', 'ra đây', 'ra chơi', 'nhà khó', 'từng nhà', 'chắc người', 'dạ', 'đến bao giờ', 'a lô', 'sao đang', 'nên tránh', 'tới thì', 'ít quá', 'vâng ý', 'nói khó', 'lấy cả', 'ái', 'hết rồi', 'tất thảy', 'vì sao', 'tò te', 'ngày ấy', 'thì', 'tại đó', 'cơ hội', 'lúc trước', 'như nhau', 'lấy ráo', 'dẫu', 'nghe ra', 'phía bên', 'đây này', 'cá nhân', 'có chăng', 'hoặc', 'thường số', 'lúc đi', 'rích', 'rồi', 'đâu như', 'người mình', 'vâng chịu', 'cả ăn', 'trước nay', 'bằng nào', 'tình trạng', 'xuất kì bất ý', 'nên chi', 'thích', 'không biết', 'chung ái', 'để lòng', 'riệt', 'chắc vào', 'phốc', 'nếu như', 'phải cái', 'choa', 'biết', 'vậy thì', 'nhưng mà', 'tuy vậy', 'thậm', 'bài bỏ', 'thậm cấp', 'rồi tay', 'trả lại', 'đủ số', 'thiếu', 'kể từ', 'dễ như chơi', 'thốt nói', 'phắt', 'thế mà', 'tức tốc', 'toà', 'bây giờ', 'à ơi', 'lại làm', 'chính thị', 'được cái', 'anh ấy', 'ơ kìa', 'thích tự', 'phỏng nước', 'tuổi cả', 'đều nhau', 'cùng', 'lúc lâu', 'bỏ bà', 'ít khi', 'gây thêm', 'gây cho', 'nếu mà', 'lại quả', 'nên chăng', 'bất quá', 'lần sang', 'đến tuổi', 'sớm', 'nữa', 'người khách', 'đáng lý', 'bản bộ', 'ráo cả', 'thà là', 'đến gần', 'khó mở', 'bấy nhiêu', 'lớn nhỏ', 'bộ', 'khi nào', 'là nhiều', 'thứ', 'nhất là', 'bất đồ', 'nào phải', 'nào', 'đúng tuổi', 'chính là', 'những ai', 'nhà chung', 'chết tiệt', 'bây chừ', 'suýt', 'đặt trước', 'cả đến', 'luôn tay', 'bấy chầy', 'phần', 'đều bước', 'dùng làm', 'lại bộ', 'tháng', 'thiếu gì', 'thật chắc', 'lâu nay', 'ô kìa', 'từ', 'ắt hẳn', 'mới đây', 'cho tin', 'cuốn', 'riêng', 'bỏ không', 'chắc', 'rất lâu', 'đến hay', 'vung thiên địa', 'ào vào', 'lượng', 'trước ngày', 'đáng', 'bởi vì', 'khi không', 'tít mù', 'giờ đi', 'ấy là', 'nhất tâm', 'đã đủ', 'để được', 'lại nữa', 'hết', 'có chuyện', 'nhận làm', 'tột cùng', 'nó', 'á à', 'phỏng theo', 'ngôi nhà', 'như chơi', 'à', 'ăn quá', 'phía bạn', 'tột', 'tốt mối', 'nhung nhăng', 'vùng nước', 'có được', 'cũng vậy', 'lấy xuống', 'tôi con', 'khó nói', 'âu là', 'bắt đầu từ', 'xa xa', 'không nhận', 'bất cứ', 'dù', 'ngăn ngắt', 'lấy lý do', 'đến lời', 'thêm vào', 'rày', 'ấy', 'phè phè', 'khó tránh', 'mới hay', 'thật tốt', 'vâng vâng', 'vừa', 'làm thế nào', 'phải giờ', 'lúc này', 'dưới nước', 'chú mình', 'tự', 'lại thôi', 'nhỏ người', 'nhằm lúc', 'tin', 'tỏ ra', 'trếu tráo', 'thanh thanh', 'nhỉ', 'làm mất', 'nhận ra', 'dễ sử dụng', 'sa sả', 'tránh ra', 'nhớ ra', 'dễ khiến', 'ra ngôi', 'rõ thật', 'đủ', 'ngày đến', 'vì thế', 'số loại', 'bông', 'đồng thời', 'vì rằng', 'đại loại', 'thứ bản', 'tại đâu', 'thoắt', 'nhau', 'vừa lúc', 'cần cấp', 'em', 'ráo trọi', 'chú khách', 'ít nhiều', 'sau đây', 'tất tần tật', 'có ăn', 'dưới', 'tốt hơn', 'vừa rồi', 'vượt khỏi', 'lại nói', 'nghe không', 'nhận họ', 'vài', 'xuất kỳ bất ý', 'cơ cùng', 'từ ấy', 'ngay tức khắc', 'họ xa', 'chỉ là', 'tất tật', 'thứ đến', 'tại nơi', 'tìm bạn', 'phót', 'nước đến', 'vậy là', 'trả', 'trước đây', 'tên', 'hiểu', 'được lời', 'những là', 'sao vậy', 'bước', 'rồi nữa', 'vào gặp', 'từ tính', 'thanh không', 'mạnh', 'ông nhỏ', 'ít', 'nhiên hậu', 'hay tin', 'tính căn', 'vào', 'ớ này', 'mang về', 'gây ra', 'xoét', 'làm cho', 'nhỏ', 'số người', 'tù tì', 'thúng thắng', 'chung nhau', 'hết chuyện', 'đại phàm', 'thế thường', 'quá', 'nghe nhìn', 'ừ thì', 'sao', 'nhất thì', 'lấy lại', 'một ít', 'vài tên', 'đưa chuyện', 'bấy lâu', 'cơn', 'số cụ thể', 'thế nên', 'cơ chừng', 'làm theo', 'ngay lập tức', 'ăn', 'thật ra', 'tránh tình trạng', 'hãy còn', 'con dạ', 'thêm', 'mọi nơi', 'dạ khách', 'làm sao', 'tuốt tuột', 'ở nhờ', 'bỏ cha', 'đến cả', 'dài lời', 'đó đây', 'trước sau', 'phỏng tính', 'mọi', 'ăn hết', 'làm lòng', 'nào cũng', 'chúng ông', 'đưa tay', 'chứ ai', 'vung tàn tán', 'căn', 'bên', 'luôn cả', 'đưa xuống', 'sáng rõ', 'trong này', 'nếu không', 'bao nhiêu', 'kể cả', 'không được', 'gần ngày', 'đã không', 'công nhiên', 'thuộc cách', 'của', 'đúng', 'thế thôi', 'chớ không', 'giữ lấy', 'nhưng', 'quá mức', 'do đó', 'xoẳn', 'dùng đến', 'vả chăng', 'nghe như', 'biết mình', 'dạ dài', 'tránh khỏi', 'từ ái', 'vừa qua', 'lên đến', 'ngay khi đến', 'bất kể', 'gần đây', 'như', 'lúc ấy', 'làm', 'văng tê', 'là ít', 'khoảng', 'khi', 'chớ', 'tìm ra', 'đã vậy', 'chắc lòng', 'nước', 'cao răng', 'giảm thế', 'nói lên', 'ăn cuộc', 'ừ', 'đưa ra', 'bất thình lình', 'ngọn', 'sì', 'cuối', 'đã', 'thật là', 'cơ', 'không hay', 'dài', 'như thường', 'nghe đâu', 'cô', 'cho rằng', 'vung tán tàn', 'đưa', 'có đáng', 'đưa em', 'ạ ơi', 'hay không', 'buổi làm', 'cao xa', 'ra lời', 'qua ngày', 'xin', 'mà lại', 'phỏng như', 'chưa bao giờ', 'khó thấy', 'tên chính', 'gần xa', 'hơn trước', 'sốt sột', 'nhất mực', 'có dễ', 'như trên', 'ít thấy', 'thì thôi', 'ngay lúc', 'đều', 'toẹt', 'nếu vậy', 'bác', 'mở mang', 'thanh ba', 'mọi khi', 'cơ chỉ', 'bán cấp', 'mọi giờ', 'ăn hỏi', 'đặt mức', 'răng', 'như trước', 'lớn lên', 'nhờ nhờ', 'thế ra', 'nhìn thấy', 'bởi', 'ít ra', 'cho chắc', 'tại tôi', 'không cùng', 'có tháng', 'xoành xoạch', 'chắc chắn', 'rút cục', 'đến cùng', 'bỏ lại', 'sắp đặt', 'của tin', 'mở nước', 'bằng vào', 'đạt', 'tránh', 'trực tiếp làm', 'lên nước', 'thường hay', 'thiếu điểm', 'cách nhau', 'ối giời ơi', 'ngày tháng', 'ô hay', 'riu ríu', 'sau đó', 'lấy làm', 'nói là', 'bấy', 'tìm việc', 'chưa tính', 'nhìn nhận', 'hiện tại', 'ừ nhé', 'thảo hèn', 'bất ngờ', 'cả tin', 'từ căn', 'dễ gì', 'bèn', 'có số', 'một số', 'tại lòng', 'từ đó', 'ứ ừ', 'nào đâu', 'vô kể', 'chịu ăn', 'điểm gặp', 'nước bài', 'các cậu', 'hỏi xin', 'hay hay', 'phải biết', 'vài nhà', 'ít có', 'là là', 'vài nơi', 'quá thì', 'yêu cầu', 'ở trên', 'như không', 'được nước', 'thà rằng', 'vùng', 'người khác', 'ít lâu', 'phỉ phui', 'bỏ nhỏ', 'nước cùng', 'chuyện', 'hết ý', 'lên mạnh', 'thành thử', 'không bán', 'bỗng đâu', 'sau hết', 'ngày càng', 'xăm xăm', 'phải tay', 'đặc biệt', 'dần dần', 'ai', 'răng răng', 'sao bằng', 'các', 'tin thêm', 'biết bao nhiêu', 'đây rồi', 'nhanh tay', 'nhất', 'chẳng nữa', 'tuy', 'nói xa', 'mới rồi', 'ái chà', 'vị trí', 'chăng', 'cả nghĩ', 'đâu', 'thời gian sử dụng', 'đâu nào', 'buổi', 'thôi việc', 'chiếc', 'tất cả bao nhiêu', 'chớ như', 'tấn', 'xem ra', 'để', 'phải rồi', 'làm được', 'cứ như', 'thêm chuyện', 'dạ bán', 'ngay bây giờ', 'dùng', 'phương chi', 'thấp', 'tăng chúng', 'hết của', 'ăn về', 'trong mình', 'giống', 'gặp phải', 'thuộc từ', 'nên', 'sự thế', 'ngộ nhỡ', 'con nhà', 'thuộc lại', 'có ý', 'ai đó', 'bởi ai', 'bên cạnh', 'hỗ trợ', 'chỉn', 'quá bộ', 'thường khi', 'bằng được', 'ngõ hầu', 'qua đi', 'tạo ra', 'ít nữa', 'nghỉm', 'ra tay', 'vào đến', 'xa xả', 'gần bên', 'chú dẫn', 'dạ con', 'rón rén', 'sắp', 'mỗi người', 'đúng ngày', 'tốt', 'duy chỉ', 'tuy rằng', 'cho đang', 'cho rồi', 'chơi', 'xảy ra', 'vừa mới', 'cái', 'từng đơn vị', 'cực lực', 'làm lấy', 'vì vậy', 'xuống', 'nhất quyết', 'tấm', 'họ gần', 'thộc', 'muốn', 'nhiều ít', 'số thiếu', 'tốt bộ', 'bằng ấy', 'bán dạ', 'chứ lại', 'tuy đã', 'chị ấy', 'ông ấy', 'vượt', 'nhờ chuyển', 'hay là', 'lượng số', 'thẩy', 'trên bộ', 'nữa rồi', 'cơ hồ', 'bất tử', 'trong số', 'thời gian tính', 'chung quy lại', 'nhận', 'chị bộ', 'có đâu', 'bỏ mình', 'ăn trên', 'thím', 'được', 'trong vùng', 'mọi sự', 'trên dưới', 'làm bằng', 'số là', 'khó chơi', 'từ giờ', 'ông', 'trời đất ơi', 'thuần', 'thế', 'bất luận', 'có chăng là', 'lấy để', 'nghe nói', 'cho nên', 'ở vào', 'từng ấy', 'mà vẫn', 'những khi', 'mình', 'cứ việc', 'đang tay', 'thấy tháng', 'toé khói', 'ở năm', 'đến giờ', 'ngồi bệt', 'như thể', 'có ngày', 'khác', 'làm vì', 'bỏ ra', 'có thể', 'tiếp theo', 'vậy ư', 'đều đều', 'cơ mà', 'sớm ngày', 'nơi', 'cho hay', 'nặng về', 'phía dưới', 'chợt', 'có ai', 'vậy mà', 'đó', 'thuộc', 'chưa', 'gì gì', 'chứ như', 'oai oái', 'thốt thôi', 'như tuồng', 'chung quy', 'thỉnh thoảng', 'vừa vừa', 'ngươi', 'rất', 'chỉ', 'chắc hẳn', 'từ khi', 'bộ điều', 'cơ dẫn', 'thế thế', 'vẫn', 'thích ý', 'tha hồ ăn', 'căn tính', 'úi dào', 'để phần', 'năm tháng', 'nghe rõ', 'nghiễm nhiên', 'chỉ chính', 'rồi sau', 'làm ngay', 'tính phỏng', 'ngồi sau', 'dù sao', 'ren rén', 'nhận được', 'nhận biết', 'mới', 'quan trọng vấn đề', 'nói ý', 'có thế', 'ra bài', 'thương ôi', 'em em', 'làm riêng', 'chịu', 'đâu cũng', 'người người', 'bớ', 'tự tạo', 'những muốn', 'cả', 'mà thôi', 'có vẻ', 'quả', 'bởi chưng', 'tuốt luốt', 'bài bác', 'điểm', 'chuyển đạt', 'bỏ quá', 'thôi', 'chết thật', 'chọn', 'giờ lâu', 'thành ra', 'nói phải', 'ắt phải', 'ử', 'thường tính', 'kể tới', 'cô tăng', 'cả nhà', 'khi khác', 'làm nên', 'so với', 'trả của', 'khác khác', 'lúc đó', 'bản riêng', 'chính điểm', 'tìm hiểu', 'thế à', 'tập trung', 'phải lời', 'quá tay', 'giảm', 'cao ráo', 'có họ', 'hay biết', 'về tay', 'tăng thế', 'phần nhiều', 'nhất đán', 'lấy thêm', 'giảm chính', 'chú mày', 'tấn tới', 'đây', 'không còn', 'nhất thiết', 'chứ lị', 'không kể', 'chú', 'còn nữa', 'được tin', 'thật sự', 'bạn', 'không để', 'ngày xửa', 'khó nghe', 'về', 'trước kia', 'qua', 'như sau', 'tăng cấp', 'bước tới', 'liên quan', 'cái đó', 'trước khi', 'quá nhiều', 'cóc khô', 'gì', 'khác nhau', 'phụt', 'quá bán', 'coi bộ', 'tuy nhiên', 'tha hồ', 'vào khoảng', 'ngồi không', 'con con', 'về nước', 'thực tế', 'gây', 'tay', 'cao số', 'đến thế', 'hay', 'còn thời gian', 'thì phải', 'chớ gì', 'để giống', 'dù rằng', 'ngoải', 'xăm xúi', 'xa tắp', 'làm tôi', 'phải lại', 'dẫu rằng', 'riêng từng', 'thoạt', 'tuy thế', 'xuất hiện', 'trong khi', 'nói ra', 'oái', 'phù hợp', 'ví dù', 'ý hoặc', 'quận', 'thường', 'nghe lại', 'đâu đâu', 'bất kì', 'đánh đùng', 'ngày cấp', 'trong ấy', 'vô hình trung', 'buổi sớm', 'một lúc', 'bà', 'đơn vị', 'có người', 'ngoài', 'tên cái', 'cụ thể như', 'tốc tả', 'thanh điểm', 'tháng năm', 'cách đều', 'qua thì', 'chui cha', 'mở', 'mà cả', 'nặng căn', 'tạo ý', 'đáng lẽ', 'đâu có', 'lần khác', 'chẳng lẽ', 'còn như', 'từng giờ', 'đủ điều', 'mức', 'nhiệt liệt', 'mở ra', 'vào lúc', 'thốc tháo', 'là phải', 'lên ngôi', 'xa gần', 'xềnh xệch', 'tự lượng', 'những', 'hỏi lại', 'lúc đến', 'ngồi', 'nhất loạt', 'việc gì', 'ví thử', 'ô kê', 'thốt nhiên', 'không phải không', 'có khi', 'hầu hết', 'đến cùng cực', 'hay nói', 'lại ăn', 'tấm các', 'nhờ đó', 'phải không', 'cả nghe', 'chắc dạ', 'một vài', 'đủ dùng', 'mà', 'dữ', 'tự ý', 'sang năm', 'khách', 'dẫu mà', 'nào là', 'mỗi một', 'bởi vậy', 'tạo điều kiện', 'xa tanh', 'quá giờ', 'dạ dạ', 'từ thế', 'nhà ngoài', 'qua lần', 'veo', 'ra gì', 'trển', 'làm tại', 'bà ấy', 'bất quá chỉ', 'dễ sợ', 'nghĩ tới', 'nhằm vào', 'ráo', 'xuể', 'dẫn', 'phần việc', 'sao bản', 'cho về', 'về phần', 'buổi ngày', 'phỏng', 'quay đi', 'chưa từng', 'cùng nhau', 'nhà ngươi', 'phải người', 'thấp xuống', 'ngay từ', 'sì sì', 'phăn phắt', 'hơn', 'theo như', 'khẳng định', 'khoảng cách', 'rằng', 'tiện thể', 'trước nhất', 'như vậy', 'càng càng', 'ắt thật', 'thốc', 'xem', 'vấn đề', 'bỏ xa', 'vẫn thế', 'thì là', 'làm tăng', 'ngày qua', 'lâu lâu', 'tà tà', 'đặt', 'như ý', 'bỗng nhiên', 'bài cái', 'biết bao', 'giữ ý', 'cuộc', 'ăn người', 'đến thì', 'nặng', 'cũng như', 'ngay tức thì', 'những như', 'luôn', 'vâng', 'cật sức', 'điểm đầu tiên', 'lần trước', 'qua chuyện', 'khó biết', 'chứ gì', 'cho nhau', 'hỏi xem', 'tiếp tục', 'phía trên', 'ngồi trệt'}\n", "{'than', 'those', 'how', 'what', 'did', 'doing', \"weren't\", \"don't\", \"we've\", 'be', 'weren', \"mightn't\", \"we're\", 'very', 'itself', 'our', 'any', \"it'd\", 'i', 'themselves', 'will', 'because', \"couldn't\", 'now', 'for', 'yourselves', 'on', 'haven', 'below', 'as', 'why', 'this', 'won', \"it'll\", 'again', 'don', 'nor', 'o', 'you', 'being', 'the', 'had', \"haven't\", 'wouldn', 'was', 'some', 'doesn', 'few', 'an', 'during', \"they'd\", \"didn't\", 'theirs', 'at', 'does', 'most', 'down', 'her', \"aren't\", \"they've\", 'ours', 'or', 'its', 're', 'yours', 'ain', 'from', 'own', 'both', \"doesn't\", 'when', 'before', 'having', 'not', 'have', 'ourselves', 'been', \"we'd\", 'after', 'm', 'into', \"they're\", 'while', 's', 've', \"wasn't\", 'too', \"hasn't\", \"needn't\", 'y', \"you've\", 'mightn', 'your', 'between', 'all', 'then', 'no', 'has', \"you'll\", 'each', 'other', 'hers', \"hadn't\", 'in', 'once', 'such', \"you're\", 'against', 'they', \"they'll\", 'he', 'a', 'by', 'same', 'didn', 'if', 'where', \"she'd\", \"won't\", \"you'd\", 'with', \"she'll\", \"i'll\", \"mustn't\", 'were', 'isn', \"we'll\", 'out', 'to', 'it', 'herself', 'off', 'their', 'aren', 'further', 'couldn', \"he's\", 'and', \"he'll\", 'hadn', 'are', \"i'd\", \"shouldn't\", 'them', 'll', \"should've\", 'we', \"shan't\", \"she's\", 'of', 'who', \"wouldn't\", 'that', 'about', 'wasn', 'myself', 'just', 'do', 'more', 'd', 'his', 'can', 'over', 'there', 'these', 'is', \"i'm\", 'she', 'only', \"it's\", 'above', 'here', 'through', 'him', 'hasn', \"isn't\", 'until', 't', 'my', \"i've\", 'so', 'mustn', 'shan', \"he'd\", 'yourself', 'am', 'but', 'ma', 'should', 'me', 'needn', 'under', 'which', \"that'll\", 'whom', 'shouldn', 'up', 'himself'}\n"]}], "source": ["with open(stop_words_vn_txt, 'r', encoding='utf-8') as f:\n", "    stop_words_vi = set(word.strip() for word in f if word.strip())\n", "stop_words_en = set(stopwords.words('english'))\n", "print(stop_words_vi)\n", "print(stop_words_en)"]}, {"cell_type": "code", "execution_count": 116, "id": "818115a9", "metadata": {}, "outputs": [], "source": ["df['description_cleaned'] = df.apply(lambda x: clean_text(x['description_en'] if x['language'] == 'en' else x['description'], x['language']), axis=1)\n", "df['requirements_cleaned'] = df.apply(lambda x: clean_text(x['requirements_en'] if x['language'] == 'en' else x['requirements'], x['language']), axis=1)\n", "df['skills_cleaned'] = df['skills_en'].apply(lambda x: clean_skills(x))"]}, {"cell_type": "code", "execution_count": 117, "id": "8f61ec69", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   id                                            title  \\\n", "0   1                                   MLops Engineer   \n", "1   2              Senior DevOps Engineer (Cloud, AWS)   \n", "2   3  VTS - <PERSON><PERSON><PERSON>n <PERSON> (Agile/ Azure)   \n", "3   4       VTS - <PERSON>ư <PERSON>ấn G<PERSON> - Presales Engineer   \n", "4   5    VHT - Embedded Software Engineer (Linux, C++)   \n", "5   6                  Quality Assurance Manager (QAM)   \n", "6   7                Platform Manager (CDN ecosystems)   \n", "7   8   Bridge Project Manager (BrSE/ IT Communicator)   \n", "8   9    Senior Process Quality Assurance (PQA, QA QC)   \n", "9  10     Hybrid - Ruby On Rails Developer (Ruby, SQL)   \n", "\n", "                                             company     location  \\\n", "0                                    Trusting Social  Ho <PERSON>   \n", "1                                              TymeX  Ho Chi Minh   \n", "2                                      Viettel Group       Ha Noi   \n", "3                                      Viettel Group       Ha Noi   \n", "4                                      Viettel Group       Ha Noi   \n", "5  Viettel Software Services (A Member of Viettel...       Ha Noi   \n", "6                                         DatVietVAC  Ho Chi Minh   \n", "7                                      Vitalify Asia  Ho <PERSON>   \n", "8                                           VNDIRECT       Ha Noi   \n", "9                                          MEALSUITE  Ho Chi Minh   \n", "\n", "               salary      work_type  \\\n", "0      You'll love it  Not specified   \n", "1      You'll love it  Not specified   \n", "2      You'll love it  Not specified   \n", "3      You'll love it  Not specified   \n", "4     650 - 2,200 USD  Not specified   \n", "5      You'll love it  Not specified   \n", "6      You'll love it  Not specified   \n", "7      You'll love it  Not specified   \n", "8  Very attractive!!!  Not specified   \n", "9      You'll love it  Not specified   \n", "\n", "                                         description  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3  <PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON>vũ tr<PERSON>” c<PERSON><PERSON> Viettel, n<PERSON><PERSON> bạn k...   \n", "4  <PERSON><PERSON><PERSON> hơn 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "5  X<PERSON>y dựng khung quy trình công ty và chủ trì cả...   \n", "6  X<PERSON><PERSON> dựng khung quy trình công ty và chủ trì cả...   \n", "7  <PERSON><PERSON> ch<PERSON> and こんにちは！, chúng tôi là Vitalify Asi...   \n", "8  1. <PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> trị khung kiểm soát dự án\\n...   \n", "9  Trung tâm Công nghệ thông tin là đơn vị xây dự...   \n", "\n", "                                        requirements  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3  Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4  Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "5  Tốt nghi<PERSON><PERSON> đạ<PERSON> học về các lĩnh vực <PERSON> nghệ t...   \n", "6  Tốt nghi<PERSON><PERSON> đạ<PERSON> h<PERSON> về các lĩnh vực <PERSON> nghệ t...   \n", "7  SKILL & EXPERIENCE REQUIREMENTS:\\n- Experience...   \n", "8  Tốt nghiệp đại học trở lên.\\nTối thiểu 3 năm k...   \n", "9  Tốt nghiệ<PERSON> họ<PERSON> loại khá trở lên chuyên ngà...   \n", "\n", "                                              skills language  \\\n", "0  MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2  Project Management, Business Analysis, Presale...       vi   \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4  Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "5  PQA, Team Management, QA QC, ISO 27001, IT Aud...       vi   \n", "6  PQA, Team Management, QA QC, ISO 27001, IT Aud...       vi   \n", "7  Bridge Project Management, Japanese, Agile, Pr...       vi   \n", "8  PQA, QA QC, Tester, ISO 27001, IT Audit, Gover...       vi   \n", "9  NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...       vi   \n", "\n", "                                           skills_en  \\\n", "0  MLOps, Python, Linux, Docker, Data Science, Te...   \n", "1  AWS, DevOps, Cloud, Cloud-native Architecture,...   \n", "2  Project Management, Business Analysis, Presale...   \n", "3  Presale, Business Analysis, Salesforce, Pre-sa...   \n", "4  Embedded, C++, Linux, C language, Embedded Eng...   \n", "5  PQA, Team Management, QA QC, ISO 27001, IT Aud...   \n", "6  PQA, Team Management, QA QC, ISO 27001, IT Aud...   \n", "7  Bridge Project Management, Japanese, Agile, Pr...   \n", "8  PQA, QA QC, Tester, ISO 27001, IT Audit, Gover...   \n", "9  NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...   \n", "\n", "                                      description_en  \\\n", "0  We are looking for qualified MLops Engineer fo...   \n", "1  We are seeking an experienced Senior DevOps En...   \n", "2  Ecotek is leading Ecopark to develop a model o...   \n", "3  Joining Viettel technology, where you are not ...   \n", "4  With more than 1200 high quality personnel, Vi...   \n", "5  Building a company process framework and presi...   \n", "6  Building a company process framework and presi...   \n", "7  Hello and こんにちは！, we are Vitalify Asia, an IT ...   \n", "8  1. Design & Project Control Frame\\nBuilding, o...   \n", "9  The Information Technology Center is a unit in...   \n", "\n", "                                     requirements_en  \\\n", "0  BS or MS in Computer Science or related fields...   \n", "1  Requirements:\\nBachelor's or Master's degree i...   \n", "2  Good logical thinking, solution -oriented thin...   \n", "3  Degree: Graduated from university (good or hig...   \n", "4  Graduated with regular university or higher sp...   \n", "5  Graduated from university in the fields of inf...   \n", "6  Graduated from university in the fields of inf...   \n", "7  Skill & Experience Requirements:\\n- Experience...   \n", "8  Graduated from university or higher.\\nAt least...   \n", "9  Graduated from university or higher specialize...   \n", "\n", "                                 description_cleaned  \\\n", "0  look qualified mlop engineer ekyc project help...   \n", "1  seek experienced senior devop engineer aw join...   \n", "2  ecotek dẫn dắt ecopark phát triển mô hình thàn...   \n", "3  gia nhập vũ trụ công nghệ viettel đắm chìm hàn...   \n", "4  1200 nhân sự chất lượng tổng công ty công nghi...   \n", "5  xây dựng khung quy trình công ty chủ trì cải t...   \n", "6  xây dựng khung quy trình công ty chủ trì cải t...   \n", "7  chào and こんにちは vitalify asia công ty it trụ sở...   \n", "8  1 thiết kế quản trị khung kiểm soát dự án xây ...   \n", "9  trung tâm công nghệ thông tin xây dựng triển k...   \n", "\n", "                                requirements_cleaned  \\\n", "0  bs ms computer science relate field 1 3 year e...   \n", "1  requirement bachelor master degree computer sc...   \n", "2  tư duy logic tư duy hướng gi<PERSON>i pháp tư duy phả...   \n", "3  bằng cấp tốt nghiệp đại học trở chuyên ngành c...   \n", "4  tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...   \n", "5  tốt nghiệp đại học lĩnh vực công nghệ thông ti...   \n", "6  tốt nghiệ<PERSON> đại học lĩnh vực công nghệ thông ti...   \n", "7  skill experience requirements experience proje...   \n", "8  tốt nghiệp đại học trở tối thiểu 3 kinh nghiệm...   \n", "9  tốt nghiệp đại học trở chuyên ngành cntt khmt ...   \n", "\n", "                                      skills_cleaned  \n", "0  [MLOps, Python, Linux, Docker, Data Science, T...  \n", "1  [AWS, DevOps, Cloud, Cloud-native Architecture...  \n", "2  [Project Management, Business Analysis, Presal...  \n", "3  [Presale, Business Analysis, Salesforce, Pre-s...  \n", "4  [Embedded, C++, Linux, C language, Embedded En...  \n", "5  [PQ<PERSON>, Team Management, QA QC, ISO 27001, IT Au...  \n", "6  [P<PERSON><PERSON>, Team Management, QA QC, ISO 27001, IT Au...  \n", "7  [Bridge Project Management, Japanese, Agile, P...  \n", "8  [<PERSON><PERSON><PERSON>, Q<PERSON> QC, <PERSON>er, ISO 27001, IT Audit, Gove...  \n", "9  [NodeJS, Java, PHP, OOP, CI/CD, Backend Develo...  \n"]}], "source": ["print(df.head(10))"]}, {"cell_type": "code", "execution_count": 118, "id": "d6568db8", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['<PERSON>', '<PERSON> <PERSON>', 'Others', '<PERSON> <PERSON> <PERSON>',\n", "       '<PERSON>', '<PERSON>',\n", "       '<PERSON>', '<PERSON>',\n", "       '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "       '<PERSON> Nan<PERSON>', '<PERSON>',\n", "       '<PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON> - Others',\n", "       '<PERSON>', 'Thai Nguyen', '<PERSON><PERSON>ng',\n", "       '<PERSON> - Ha <PERSON> - Others', '<PERSON> - Others',\n", "       '<PERSON> - <PERSON>'], dtype=object)"]}, "execution_count": 118, "metadata": {}, "output_type": "execute_result"}], "source": ["df['location'].unique()"]}, {"cell_type": "code", "execution_count": 119, "id": "1dcfa1ca", "metadata": {}, "outputs": [], "source": ["locations = np.array([\n", "    '<PERSON>', '<PERSON> <PERSON>', 'Others', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "    '<PERSON>', '<PERSON>',\n", "    '<PERSON>', '<PERSON>',\n", "    '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON>',\n", "    '<PERSON> Nan<PERSON>', '<PERSON>',\n", "    '<PERSON> <PERSON> <PERSON>', '<PERSON> <PERSON><PERSON> <PERSON> <PERSON> - Others',\n", "    '<PERSON>', 'Thai Nguyen', '<PERSON><PERSON>ng',\n", "    '<PERSON> - Ha <PERSON> - Others', '<PERSON> - Others',\n", "    '<PERSON> - <PERSON> <PERSON>'\n", "])\n", "\n", "main_cities = {'Ha Noi', 'Ho Chi Minh'}\n", "\n", "def classify_location_group(loc_str):\n", "    # Tách và làm sạch địa điểm\n", "    cities = [c.strip() for c in loc_str.split('-')]\n", "    cities_set = set(cities)\n", "\n", "    # Giao giữa cities_set và main_cities\n", "    common_cities = cities_set & main_cities\n", "\n", "    if common_cities == {'Ha Noi'}:\n", "        return 1\n", "    elif common_cities == {'Ho Chi Minh'}:\n", "        return 2\n", "    else:\n", "        return 3"]}, {"cell_type": "code", "execution_count": 120, "id": "a5b5b824", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["location_group\n", "2    551\n", "1    352\n", "3     92\n", "Name: count, dtype: int64\n", "0    2\n", "1    2\n", "2    1\n", "3    1\n", "4    1\n", "5    1\n", "6    2\n", "7    2\n", "8    1\n", "9    2\n", "Name: location_group, dtype: int64\n"]}], "source": ["df['location_group'] = df['location'].apply(lambda x: classify_location_group(x))\n", "print(df['location_group'].value_counts())\n", "print(df['location_group'].head(10))"]}, {"cell_type": "code", "execution_count": 121, "id": "62e17f1a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>company</th>\n", "      <th>location</th>\n", "      <th>salary</th>\n", "      <th>work_type</th>\n", "      <th>description</th>\n", "      <th>requirements</th>\n", "      <th>skills</th>\n", "      <th>language</th>\n", "      <th>skills_en</th>\n", "      <th>description_en</th>\n", "      <th>requirements_en</th>\n", "      <th>description_cleaned</th>\n", "      <th>requirements_cleaned</th>\n", "      <th>skills_cleaned</th>\n", "      <th>location_group</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>MLops Engineer</td>\n", "      <td>Trusting Social</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>We are looking for qualified MLops Engineer fo...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>en</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>We are looking for qualified MLops Engineer fo...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>look qualified mlop engineer ekyc project help...</td>\n", "      <td>bs ms computer science relate field 1 3 year e...</td>\n", "      <td>[MLOps, Python, Linux, Docker, Data Science, T...</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Senior Dev<PERSON>ps Engineer (Cloud, AWS)</td>\n", "      <td>TymeX</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>We are seeking an experienced Senior DevOps En...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>en</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>We are seeking an experienced Senior DevOps En...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>seek experienced senior devop engineer aw join...</td>\n", "      <td>requirement bachelor master degree computer sc...</td>\n", "      <td>[AWS, DevOps, Cloud, Cloud-native Architecture...</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>VTS - <PERSON><PERSON><PERSON><PERSON><PERSON> (Agile/ Azure)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td>Ecotek đang dẫn dắt Ecopark phát triển trở thà...</td>\n", "      <td>T<PERSON> duy logic tốt, tư duy hướ<PERSON> g<PERSON><PERSON>, tư d...</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>vi</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>Ecotek is leading Ecopark to develop a model o...</td>\n", "      <td>Good logical thinking, solution -oriented thin...</td>\n", "      <td>ecotek dẫn dắt ecopark phát triển mô hình thàn...</td>\n", "      <td>tư duy logic tư duy hướng giải pháp tư duy phả...</td>\n", "      <td>[Project Management, Business Analysis, Presal...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>VTS - <PERSON><PERSON> - Presales Engineer</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Not specified</td>\n", "      <td><PERSON><PERSON> <PERSON>vũ tr<PERSON> công ng<PERSON> Viettel, n<PERSON>i bạn k...</td>\n", "      <td>Bằng cấp: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (loại Khá trở lên...</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>vi</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>Joining Viettel technology, where you are not ...</td>\n", "      <td>Degree: Graduated from university (good or hig...</td>\n", "      <td>gia nh<PERSON><PERSON> vũ trụ công nghệ viettel đắm chìm hàn...</td>\n", "      <td>bằng cấp tốt nghiệp đại học trở chuyên ngành c...</td>\n", "      <td>[Presale, Business Analysis, Salesforce, Pre-s...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>VHT - Embedded Software Engineer (Linux, C++)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>650 - 2,200 USD</td>\n", "      <td>Not specified</td>\n", "      <td><PERSON><PERSON><PERSON> 1200 nhân sự chất lư<PERSON>o , Tổng Côn...</td>\n", "      <td><PERSON><PERSON><PERSON> nghi<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>vi</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>With more than 1200 high quality personnel, Vi...</td>\n", "      <td>Graduated with regular university or higher sp...</td>\n", "      <td>1200 nhân sự chất lượng tổng công ty công nghi...</td>\n", "      <td>tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...</td>\n", "      <td>[Embedded, C++, Linux, C language, Embedded En...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>990</th>\n", "      <td>11</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> viên (Hỗ trợ ứng dụng OPN)</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>991</th>\n", "      <td>12</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON> viên (Vận hành AM/DevOPs CI/CD)</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>992</th>\n", "      <td>13</td>\n", "      <td><PERSON><PERSON> li<PERSON> (DE) - <PERSON> Enginner</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>993</th>\n", "      <td>14</td>\n", "      <td>CV Phát triển DEV BE/FE/Fullstack</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specified</td>\n", "      <td>fr</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specified]</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>994</th>\n", "      <td>15</td>\n", "      <td>CV Phân tích nghi<PERSON> vụ và <PERSON>ử</td>\n", "      <td><PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)</td>\n", "      <td><PERSON></td>\n", "      <td>Not specified</td>\n", "      <td>Not specified</td>\n", "      <td>No description available</td>\n", "      <td>No requirements specified</td>\n", "      <td>Not specifi</td>\n", "      <td>fr</td>\n", "      <td>Not specifi</td>\n", "      <td>No description available</td>\n", "      <td>No requirements SPECIFied</td>\n", "      <td>no description available</td>\n", "      <td>no requirements specified</td>\n", "      <td>[Not specifi]</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>995 rows × 17 columns</p>\n", "</div>"], "text/plain": ["     id                                            title  \\\n", "0     1                                   MLops Engineer   \n", "1     2              Senior DevOps Engineer (Cloud, AWS)   \n", "2     3  VTS - <PERSON><PERSON><PERSON>n <PERSON> (Agile/ Azure)   \n", "3     4       VTS - <PERSON>ư <PERSON>ấn G<PERSON> - Presales Engineer   \n", "4     5    VHT - Embedded Software Engineer (Linux, C++)   \n", "..   ..                                              ...   \n", "990  11                <PERSON><PERSON><PERSON><PERSON> viên (Hỗ trợ ứng dụng OPN)   \n", "991  12           <PERSON><PERSON><PERSON><PERSON> viên (Vận hành AM/DevOPs CI/CD)   \n", "992  13               <PERSON><PERSON> s<PERSON> dữ liệu (DE) - <PERSON>ner   \n", "993  14                CV Phát triển DEV BE/FE/Fullstack   \n", "994  15               CV Phân tích nghi<PERSON> v<PERSON> và <PERSON> thử   \n", "\n", "                                        company     location           salary  \\\n", "0                               Trusting Social  Ho Chi Minh   You'll love it   \n", "1                                         TymeX  Ho Chi Minh   You'll love it   \n", "2                                 Viettel Group       Ha Noi   You'll love it   \n", "3                                 Viettel Group       Ha Noi   You'll love it   \n", "4                                 Viettel Group       Ha Noi  650 - 2,200 USD   \n", "..                                          ...          ...              ...   \n", "990  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "991  <PERSON>ân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "992  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "993  <PERSON><PERSON> hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "994  Ngân hàng TNHH MTV Việt Nam Hiện Đại (MBV)       Ha Noi    Not specified   \n", "\n", "         work_type                                        description  \\\n", "0    Not specified  We are looking for qualified MLops Engineer fo...   \n", "1    Not specified  We are seeking an experienced Senior DevOps En...   \n", "2    Not specified  Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3    Not specified  <PERSON><PERSON> “vũ tr<PERSON>” công ng<PERSON> Viettel, n<PERSON>i bạn k...   \n", "4    Not specified  <PERSON><PERSON><PERSON>n 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "..             ...                                                ...   \n", "990  Not specified                           No description available   \n", "991  Not specified                           No description available   \n", "992  Not specified                           No description available   \n", "993  Not specified                           No description available   \n", "994  Not specified                           No description available   \n", "\n", "                                          requirements  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3    Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4    Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "..                                                 ...   \n", "990                          No requirements specified   \n", "991                          No requirements specified   \n", "992                          No requirements specified   \n", "993                          No requirements specified   \n", "994                          No requirements specified   \n", "\n", "                                                skills language  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2    Project Management, Business Analysis, Presale...       vi   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "..                                                 ...      ...   \n", "990                                      Not specified       fr   \n", "991                                      Not specified       fr   \n", "992                                      Not specified       fr   \n", "993                                      Not specified       fr   \n", "994                                        Not specifi       fr   \n", "\n", "                                             skills_en  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...   \n", "2    Project Management, Business Analysis, Presale...   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...   \n", "..                                                 ...   \n", "990                                      Not specified   \n", "991                                      Not specified   \n", "992                                      Not specified   \n", "993                                      Not specified   \n", "994                                        Not specifi   \n", "\n", "                                        description_en  \\\n", "0    We are looking for qualified MLops Engineer fo...   \n", "1    We are seeking an experienced Senior DevOps En...   \n", "2    Ecotek is leading Ecopark to develop a model o...   \n", "3    Joining Viettel technology, where you are not ...   \n", "4    With more than 1200 high quality personnel, Vi...   \n", "..                                                 ...   \n", "990                           No description available   \n", "991                           No description available   \n", "992                           No description available   \n", "993                           No description available   \n", "994                           No description available   \n", "\n", "                                       requirements_en  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Good logical thinking, solution -oriented thin...   \n", "3    Degree: Graduated from university (good or hig...   \n", "4    Graduated with regular university or higher sp...   \n", "..                                                 ...   \n", "990                          No requirements SPECIFied   \n", "991                          No requirements SPECIFied   \n", "992                          No requirements SPECIFied   \n", "993                          No requirements SPECIFied   \n", "994                          No requirements SPECIFied   \n", "\n", "                                   description_cleaned  \\\n", "0    look qualified mlop engineer ekyc project help...   \n", "1    seek experienced senior devop engineer aw join...   \n", "2    ecotek dẫn dắt ecopark phát triển mô hình thàn...   \n", "3    gia nhập vũ trụ công nghệ viettel đắm chìm hàn...   \n", "4    1200 nhân sự chất lượng tổng công ty công nghi...   \n", "..                                                 ...   \n", "990                           no description available   \n", "991                           no description available   \n", "992                           no description available   \n", "993                           no description available   \n", "994                           no description available   \n", "\n", "                                  requirements_cleaned  \\\n", "0    bs ms computer science relate field 1 3 year e...   \n", "1    requirement bachelor master degree computer sc...   \n", "2    tư duy logic tư duy hướng gi<PERSON>i pháp tư duy phả...   \n", "3    bằng cấp tốt nghiệp đại học trở chuyên ngành c...   \n", "4    tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...   \n", "..                                                 ...   \n", "990                          no requirements specified   \n", "991                          no requirements specified   \n", "992                          no requirements specified   \n", "993                          no requirements specified   \n", "994                          no requirements specified   \n", "\n", "                                        skills_cleaned  location_group  \n", "0    [MLOps, Python, Linux, Docker, Data Science, T...               2  \n", "1    [A<PERSON>, DevOps, Cloud, Cloud-native Architecture...               2  \n", "2    [Project Management, Business Analysis, Presal...               1  \n", "3    [Presale, Business Analysis, Salesforce, Pre-s...               1  \n", "4    [Embedded, C++, Linux, C language, Embedded En...               1  \n", "..                                                 ...             ...  \n", "990                                    [Not specified]               1  \n", "991                                    [Not specified]               1  \n", "992                                    [Not specified]               1  \n", "993                                    [Not specified]               1  \n", "994                                      [Not specifi]               1  \n", "\n", "[995 rows x 17 columns]"]}, "execution_count": 121, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 122, "id": "a4f07def", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20884\\1309361321.py:2: FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value 'JOB_0' has dtype incompatible with int64, please explicitly cast to a compatible dtype first.\n", "  df.at[i, 'id'] = \"JOB_\" + str(i)\n"]}], "source": ["for i in range(len(df)):\n", "    df.at[i, 'id'] = \"JOB_\" + str(i)\n"]}, {"cell_type": "code", "execution_count": 123, "id": "f8369737", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([\"You'll love it\", '650 - 2,200 USD', 'Very attractive!!!',\n", "       'Up to 18,000,000 vnđ', '800 - 1,000 USD', '3,000 - 6,000 USD',\n", "       '2,000 - 5,000 USD', '900 - 1,600 USD', '700 - 1,800 USD',\n", "       '1,000 - 2,500 USD', '800 - 3,500 USD', '700 - 1,000 USD',\n", "       'Up to 30m', '2,500 - 3,000 USD', '2,000 - 4,000 USD',\n", "       '500 - 1,000 USD', 'Negotiation', '1,000 - 1,400 USD',\n", "       '1,000 - 3,500 USD', 'Not specified', '<PERSON><PERSON><PERSON><PERSON> cao thỏa thuận',\n", "       'Negotiable', '1,000 - 2,000 USD', '1,800 - 3,500 USD',\n", "       'Tới 35 triệu'], dtype=object)"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["df['salary'].unique()"]}, {"cell_type": "code", "execution_count": 124, "id": "83d17f33", "metadata": {}, "outputs": [], "source": ["df.drop(columns=['work_type'], inplace=True)"]}, {"cell_type": "code", "execution_count": 125, "id": "2a2c558f", "metadata": {}, "outputs": [], "source": ["def extract_min_salary(s):\n", "    if not isinstance(s, str):\n", "        return 'agreement'\n", "\n", "    s = s.lower()\n", "\n", "    # Nhóm 5: <PERSON><PERSON><PERSON><PERSON> thỏa thuận\n", "    if any(keyword in s for keyword in ['not specified', 'negotiation', 'negotiable', 'thỏa thuận', \"you'll love it\", 'attractive']):\n", "        return 'agreement'\n", "\n", "    # Lương VNĐ có đơn vị triệu hoặc 'm'\n", "    if 'm' in s or 'triệu' in s:\n", "        match = re.findall(r'\\d+', s)\n", "        if match:\n", "            usd = int(match[0]) * 1_000_000 / 24000  # <PERSON><PERSON><PERSON> sang USD\n", "            return usd\n", "\n", "    # Lương USD\n", "    match = re.findall(r'\\d+(?:,\\d+)?', s)\n", "    if match:\n", "        nums = [int(x.replace(',', '')) for x in match]\n", "        return min(nums)\n", "\n", "    return 'agreement'\n", "\n", "def classify_salary(s):\n", "    val = extract_min_salary(s)\n", "\n", "    if val == 'agreement':\n", "        return 5\n", "    elif val < 500:\n", "        return 1\n", "    elif val < 2000:\n", "        return 2\n", "    elif val < 5000:\n", "        return 3\n", "    else:\n", "        return 4"]}, {"cell_type": "code", "execution_count": 126, "id": "7f84b7ed", "metadata": {}, "outputs": [], "source": ["df['type_salary'] = df['salary'].apply(classify_salary)"]}, {"cell_type": "code", "execution_count": 127, "id": "87642498", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([5, 2, 1, 3])"]}, "execution_count": 127, "metadata": {}, "output_type": "execute_result"}], "source": ["df['type_salary'].unique()"]}, {"cell_type": "code", "execution_count": 128, "id": "1110fed7", "metadata": {}, "outputs": [], "source": ["# <PERSON>ẩn hóa chữ thường và lọc các dòng KHÔNG chứa 'no description available'\n", "df = df[~df['description'].str.lower().str.contains('no description available', na=False)]"]}, {"cell_type": "code", "execution_count": 129, "id": "c50bce93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "Index: 841 entries, 0 to 989\n", "Data columns (total 17 columns):\n", " #   Column                Non-Null Count  Dtype \n", "---  ------                --------------  ----- \n", " 0   id                    841 non-null    object\n", " 1   title                 841 non-null    object\n", " 2   company               841 non-null    object\n", " 3   location              841 non-null    object\n", " 4   salary                841 non-null    object\n", " 5   description           841 non-null    object\n", " 6   requirements          841 non-null    object\n", " 7   skills                841 non-null    object\n", " 8   language              841 non-null    object\n", " 9   skills_en             841 non-null    object\n", " 10  description_en        841 non-null    object\n", " 11  requirements_en       841 non-null    object\n", " 12  description_cleaned   841 non-null    object\n", " 13  requirements_cleaned  841 non-null    object\n", " 14  skills_cleaned        841 non-null    object\n", " 15  location_group        841 non-null    int64 \n", " 16  type_salary           841 non-null    int64 \n", "dtypes: int64(2), object(15)\n", "memory usage: 118.3+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 130, "id": "8795ce96", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[('IT Services and IT Consulting', 314), ('PHP', 237), ('Software Development Outsourcing', 210), ('Java', 209), ('OOP', 196), ('Software Products and Web Services', 166), ('Telecommunication', 164), ('CI/CD', 156), ('NodeJS', 150), ('Backend Developer', 149), ('HTML5', 142), ('CSS', 142), ('Tester', 115), ('DevOps', 113), ('Financial Services', 101), ('Cloud', 95), ('Fullstack', 88), ('JavaScript', 88), ('Lara<PERSON>', 88), ('Fullstack Developer', 88)]\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20884\\1394890719.py:13: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n"]}, {"data": {"image/png": "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******************************************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****************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["skills_series = df['skills'].str.split(',').explode().str.strip()\n", "\n", "# Count the most common skills\n", "skill_counts = Counter(skills_series)\n", "top_skills = skill_counts.most_common(20)\n", "print(top_skills)\n", "\n", "# Convert to DataFrame for plotting\n", "skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])\n", "\n", "# Plot\n", "plt.figure(figsize=(12, 8))\n", "sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n", "plt.title('Top 20 Kỹ năng đư<PERSON><PERSON> yêu cầu nhiều nhất')\n", "plt.xlabel('<PERSON><PERSON> lượ<PERSON> công việc yêu cầu')\n", "plt.ylabel('K<PERSON> năng')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 131, "id": "6bc797ae", "metadata": {}, "outputs": [], "source": ["skills_to_remove = [\n", "    'IT Services and IT Consulting', 'Software Development Outsourcing', 'Software Products and Web Services', 'Telecommunication','Financial Services', 'Banking','Bridge System Engineer (BrSE)','Japanese IT Communication'\n", "]\n", "\n", "# Đ<PERSON><PERSON> về dạng chữ thường để so sánh không phân biệt hoa thường\n", "skills_to_remove = [s.lower() for s in skills_to_remove]\n"]}, {"cell_type": "code", "execution_count": 132, "id": "ac361167", "metadata": {}, "outputs": [], "source": ["def remove_unwanted_skills(skill_string):\n", "    if pd.isna(skill_string):\n", "        return skill_string\n", "    skills = [s.strip() for s in skill_string.split(',')]\n", "    filtered = [s for s in skills if s.lower() not in skills_to_remove]\n", "    return ', '.join(filtered)\n", "def extract_primary_skills(text):\n", "    # <PERSON><PERSON> lý trư<PERSON><PERSON> hợp text không phải chuỗi\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in primary_skills if skill in text.lower()]\n", "\n", "def extract_secondary_skills(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    return [skill for skill in secondary_skills if skill in text.lower()]\n", "\n", "def extract_adjectives(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp_en(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADJ']))\n", "\n", "def extract_adverbs(text):\n", "    if not isinstance(text, str) or pd.isna(text):\n", "        return []\n", "    doc = nlp_en(text)\n", "    return list(set([token.text for token in doc if token.pos_ == 'ADV']))\n"]}, {"cell_type": "code", "execution_count": 133, "id": "9e5b5775", "metadata": {}, "outputs": [], "source": ["df['skills'] = df['skills'].apply(remove_unwanted_skills)"]}, {"cell_type": "code", "execution_count": 134, "id": "73094432", "metadata": {}, "outputs": [], "source": ["def read_skills(file_path):\n", "    skills = []\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            line = line.strip()\n", "            if line and not line.startswith('#'):\n", "                skills.append(line.lower())\n", "    return skills\n", "\n", "primary_skills = read_skills('../data/primary_skills.txt')\n", "secondary_skills = read_skills('../data/secondary_skills.txt')"]}, {"cell_type": "code", "execution_count": 135, "id": "bf660360", "metadata": {}, "outputs": [], "source": ["df['primary_skills'] = df['requirements_en'].apply(extract_primary_skills)\n", "df['secondary_skills'] = df['requirements_en'].apply(extract_secondary_skills)\n", "df['adjectives'] = df['requirements_en'].apply(extract_adjectives)\n", "df['adverbs'] = df['requirements_en'].apply(extract_adverbs)"]}, {"cell_type": "code", "execution_count": 136, "id": "cfddb74f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20884\\1403453381.py:12: FutureWarning: \n", "\n", "Passing `palette` without assigning `hue` is deprecated and will be removed in v0.14.0. Assign the `y` variable to `hue` and set `legend=False` for the same effect.\n", "\n", "  sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["skills_series = df['primary_skills'].explode().dropna().str.strip()\n", "\n", "# <PERSON><PERSON><PERSON> kỹ năng phổ biến nhất\n", "skill_counts = Counter(skills_series)\n", "top_skills = skill_counts.most_common(20)\n", "\n", "# <PERSON><PERSON><PERSON><PERSON> thành DataFrame để vẽ biểu đồ\n", "skills_df = pd.DataFrame(top_skills, columns=['Skill', 'Count'])\n", "\n", "# Vẽ biểu đồ\n", "plt.figure(figsize=(12, 8))\n", "sns.barplot(x='Count', y='Skill', data=skills_df, palette='viridis')\n", "plt.title('Top 20 Kỹ năng đư<PERSON><PERSON> yêu cầu nhiều nhất')\n", "plt.xlabel('<PERSON><PERSON> lượ<PERSON> công việc yêu cầu')\n", "plt.ylabel('K<PERSON> năng')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 137, "id": "0cbbffcb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>title</th>\n", "      <th>company</th>\n", "      <th>location</th>\n", "      <th>salary</th>\n", "      <th>description</th>\n", "      <th>requirements</th>\n", "      <th>skills</th>\n", "      <th>language</th>\n", "      <th>skills_en</th>\n", "      <th>...</th>\n", "      <th>requirements_en</th>\n", "      <th>description_cleaned</th>\n", "      <th>requirements_cleaned</th>\n", "      <th>skills_cleaned</th>\n", "      <th>location_group</th>\n", "      <th>type_salary</th>\n", "      <th>primary_skills</th>\n", "      <th>secondary_skills</th>\n", "      <th>adjectives</th>\n", "      <th>adverbs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>JOB_0</td>\n", "      <td>MLops Engineer</td>\n", "      <td>Trusting Social</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>We are looking for qualified MLops Engineer fo...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>en</td>\n", "      <td>MLOps, Python, Linux, Docker, Data Science, Te...</td>\n", "      <td>...</td>\n", "      <td>BS or MS in Computer Science or related fields...</td>\n", "      <td>look qualified mlop engineer ekyc project help...</td>\n", "      <td>bs ms computer science relate field 1 3 year e...</td>\n", "      <td>[MLOps, Python, Linux, Docker, Data Science, T...</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>[python, java, javascript, c++, tensorflow, py...</td>\n", "      <td>[git, docker, linux]</td>\n", "      <td>[Great, proficient, other, Familiar, Basic, pl...</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>JOB_1</td>\n", "      <td>Senior Dev<PERSON>ps Engineer (Cloud, AWS)</td>\n", "      <td>TymeX</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>We are seeking an experienced Senior DevOps En...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>en</td>\n", "      <td>AWS, DevOps, Cloud, Cloud-native Architecture,...</td>\n", "      <td>...</td>\n", "      <td>Requirements:\\nBachelor's or Master's degree i...</td>\n", "      <td>seek experienced senior devop engineer aw join...</td>\n", "      <td>requirement bachelor master degree computer sc...</td>\n", "      <td>[AWS, DevOps, Cloud, Cloud-native Architecture...</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>[python, bash, aws]</td>\n", "      <td>[git, gitlab, git flow, docker, jenkins, teamc...</td>\n", "      <td>[related, Good, more, technical, logical, high...</td>\n", "      <td>[least, effectively, at, independently]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>JOB_2</td>\n", "      <td>VTS - <PERSON><PERSON><PERSON><PERSON><PERSON> (Agile/ Azure)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Ecotek đang dẫn dắt Ecopark phát triển trở thà...</td>\n", "      <td>T<PERSON> duy logic tốt, tư duy hướ<PERSON> g<PERSON><PERSON>, tư d...</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>vi</td>\n", "      <td>Project Management, Business Analysis, Presale...</td>\n", "      <td>...</td>\n", "      <td>Good logical thinking, solution -oriented thin...</td>\n", "      <td>ecotek dẫn dắt ecopark phát triển mô hình thàn...</td>\n", "      <td>tư duy logic tư duy hướng giải pháp tư duy phả...</td>\n", "      <td>[Project Management, Business Analysis, Presal...</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[critical, General, Good, technical, least, li...</td>\n", "      <td>[directly, first, clearly, At]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>JOB_3</td>\n", "      <td>VTS - <PERSON><PERSON> - Presales Engineer</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td><PERSON><PERSON> <PERSON>vũ tr<PERSON> công ng<PERSON> Viettel, n<PERSON>i bạn k...</td>\n", "      <td>Bằng cấp: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (loại Khá trở lên...</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>vi</td>\n", "      <td>Presale, Business Analysis, Salesforce, Pre-sa...</td>\n", "      <td>...</td>\n", "      <td>Degree: Graduated from university (good or hig...</td>\n", "      <td>gia nh<PERSON><PERSON> vũ trụ công nghệ viettel đắm chìm hàn...</td>\n", "      <td>bằng cấp tốt nghiệp đại học trở chuyên ngành c...</td>\n", "      <td>[Presale, Business Analysis, Salesforce, Pre-s...</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>[lua, electron]</td>\n", "      <td>[]</td>\n", "      <td>[English, technical, technological, Dynamic, r...</td>\n", "      <td>[always]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>JOB_4</td>\n", "      <td>VHT - Embedded Software Engineer (Linux, C++)</td>\n", "      <td>Viettel Group</td>\n", "      <td><PERSON></td>\n", "      <td>650 - 2,200 USD</td>\n", "      <td><PERSON><PERSON><PERSON> 1200 nhân sự chất lư<PERSON>o , Tổng Côn...</td>\n", "      <td><PERSON><PERSON><PERSON> nghi<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>vi</td>\n", "      <td>Embedded, C++, Linux, C language, Embedded Eng...</td>\n", "      <td>...</td>\n", "      <td>Graduated with regular university or higher sp...</td>\n", "      <td>1200 nhân sự chất lượng tổng công ty công nghi...</td>\n", "      <td>tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...</td>\n", "      <td>[Embedded, C++, Linux, C language, Embedded En...</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>[electron]</td>\n", "      <td>[git, linux]</td>\n", "      <td>[electronic, English, Good, technical, similar...</td>\n", "      <td>[at]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>985</th>\n", "      <td>JOB_985</td>\n", "      <td>AI Engineer (Python,GenAI, Azure)- Up to 50M</td>\n", "      <td>NTT DATA VDS</td>\n", "      <td><PERSON></td>\n", "      <td>500 - 1,000 USD</td>\n", "      <td>About Us\\n At EATLAB, we're a team that is har...</td>\n", "      <td>Must-Haves\\nKnowledgable in your applied field...</td>\n", "      <td>Python, C++, DevOps, Machine Learning, AI, AI ...</td>\n", "      <td>en</td>\n", "      <td>Python, C++, DevOps, Machine Learning, AI, AI ...</td>\n", "      <td>...</td>\n", "      <td>Must-Haves\\nKnowledgable in your applied field...</td>\n", "      <td>we eatlab team hardworking smart fast take act...</td>\n", "      <td>must have knowledgable apply field always upda...</td>\n", "      <td>[Python, C++, DevOps, Machine Learning, AI, AI...</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>[flask, fastapi, computer vision]</td>\n", "      <td>[segment]</td>\n", "      <td>[more, picky, linear, capable, Knowledgable, d...</td>\n", "      <td>[easily, e.g., incredibly, always, so, back, t...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>986</th>\n", "      <td>JOB_986</td>\n", "      <td>.NET <PERSON>end <PERSON> (Intermediate/Senior)</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Trung tâm Công nghệ thông tin là đơn vị xây dự...</td>\n", "      <td><PERSON><PERSON><PERSON> nghi<PERSON><PERSON> học loại khá trở lên chuyên ngà...</td>\n", "      <td>NodeJS, Java, PHP, OOP, CI/CD, Backend Developer</td>\n", "      <td>vi</td>\n", "      <td>NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...</td>\n", "      <td>...</td>\n", "      <td>Graduated from university or higher specialize...</td>\n", "      <td>trung tâm công nghệ thông tin xây dựng triển k...</td>\n", "      <td>tốt nghiệp đại học trở chuyên ngành cntt khmt ...</td>\n", "      <td>[NodeJS, Java, PHP, OOP, CI/CD, Backend Develo...</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>[java, php, aws]</td>\n", "      <td>[agile, scrum]</td>\n", "      <td>[systematic, related, specialized, GCP, equiva...</td>\n", "      <td>[least, KHMT, At]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>987</th>\n", "      <td>JOB_987</td>\n", "      <td>Senior Java Developer (Spring, MVC, English)</td>\n", "      <td>Paved Digital Pty Ltd</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>Trung tâm Công nghệ thông tin là đơn vị xây dự...</td>\n", "      <td><PERSON><PERSON><PERSON> nghi<PERSON><PERSON> học loại khá trở lên chuyên ngà...</td>\n", "      <td>NodeJS, Java, PHP, OOP, CI/CD, Backend Developer</td>\n", "      <td>vi</td>\n", "      <td>NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...</td>\n", "      <td>...</td>\n", "      <td>Graduated from university or higher specialize...</td>\n", "      <td>trung tâm công nghệ thông tin xây dựng triển k...</td>\n", "      <td>tốt nghiệp đại học trở chuyên ngành cntt khmt ...</td>\n", "      <td>[NodeJS, Java, PHP, OOP, CI/CD, Backend Develo...</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>[java, php, aws]</td>\n", "      <td>[agile, scrum]</td>\n", "      <td>[systematic, related, specialized, GCP, equiva...</td>\n", "      <td>[least, KHMT, At]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>988</th>\n", "      <td>JOB_988</td>\n", "      <td>Middle DevOps Engineer (AWS/ Database)</td>\n", "      <td>Gearment Co.,Ltd</td>\n", "      <td><PERSON></td>\n", "      <td>You'll love it</td>\n", "      <td>JOB DESCRIPTION\\n  Design and manage Kubernete...</td>\n", "      <td>MUST HAVE:\\n  At least 2 years of experience i...</td>\n", "      <td>AWS, Linux, PostgreSql, DevOps, Cloud, Docker,...</td>\n", "      <td>en</td>\n", "      <td>AWS, Linux, PostgreSql, DevOps, Cloud, Docker,...</td>\n", "      <td>...</td>\n", "      <td>MUST HAVE:\\n  At least 2 years of experience i...</td>\n", "      <td>job description design manage kubernete helm c...</td>\n", "      <td>must least 2 year experience devop role especi...</td>\n", "      <td>[AWS, Linux, PostgreSql, DevOps, Cloud, Docker...</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>[shell script, bash, powershell, postgresql, aws]</td>\n", "      <td>[git, gith<PERSON>, docker, kube<PERSON><PERSON>, jenkins, ter...</td>\n", "      <td>[NICE, similar, Willing, least, premise, publi...</td>\n", "      <td>[especially, At]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>989</th>\n", "      <td>JOB_989</td>\n", "      <td>Senior Fullstack NodeJS &amp; ReactJS Developer (E...</td>\n", "      <td>Restaff – House Of Norway</td>\n", "      <td><PERSON></td>\n", "      <td>Up to 18,000,000 vnđ</td>\n", "      <td>- <PERSON><PERSON> gia <PERSON>, b<PERSON><PERSON> trì và tối ưu hóa c...</td>\n", "      <td>BẮT BUỘC:\\n- Có ít nhất 1–2 năm kinh nghiệm ph...</td>\n", "      <td>Fullstack, HTML5, PHP, JavaScript, CSS, Larave...</td>\n", "      <td>vi</td>\n", "      <td>Fullstack, HTML5, PHP, JavaScript, CSS, Larave...</td>\n", "      <td>...</td>\n", "      <td>OBLIGATORY:\\n- At least 1–2 years of fullstack...</td>\n", "      <td>tham gia phát triển bảo trì tối <PERSON>u hóa website...</td>\n", "      <td>b<PERSON><PERSON> b<PERSON> 1 2 kinh nghiệm ph<PERSON>t triển web fullst...</td>\n", "      <td>[Fullstack, HTML5, PHP, JavaScript, CSS, Larav...</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>[java, javascript, php, laravel, symfony]</td>\n", "      <td>[]</td>\n", "      <td>[independent, Japanese, detailed, least, equiv...</td>\n", "      <td>[directly]</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>841 rows × 21 columns</p>\n", "</div>"], "text/plain": ["          id                                              title  \\\n", "0      JOB_0                                     MLops Engineer   \n", "1      JOB_1                Senior DevOps Engineer (Cloud, AWS)   \n", "2      JOB_2    VTS - <PERSON><PERSON><PERSON><PERSON> (Agile/ Azure)   \n", "3      JOB_3         VTS - <PERSON><PERSON> - Presales Engineer   \n", "4      JOB_4      VHT - Embedded Software Engineer (Linux, C++)   \n", "..       ...                                                ...   \n", "985  JOB_985       AI Engineer (Python,GenAI, Azure)- Up to 50M   \n", "986  JOB_986        .NE<PERSON> Backend Engineer (Intermediate/Senior)   \n", "987  JOB_987       Senior Java Developer (Spring, MVC, English)   \n", "988  JOB_988             Middle DevOps Engineer (AWS/ Database)   \n", "989  JOB_989  Senior Fullstack NodeJS & ReactJS Developer (E...   \n", "\n", "                       company     location                salary  \\\n", "0              Trusting Social  Ho Chi Minh        You'll love it   \n", "1                        TymeX  Ho Chi Minh        You'll love it   \n", "2                Viettel Group       Ha Noi        You'll love it   \n", "3                Viettel Group       Ha Noi        You'll love it   \n", "4                Viettel Group       Ha Noi       650 - 2,200 USD   \n", "..                         ...          ...                   ...   \n", "985               NTT DATA VDS       Ha Noi       500 - 1,000 USD   \n", "986                   <PERSON><PERSON>        You'll love it   \n", "987      Paved Digital Pty Ltd  Ho Chi Minh        You'll love it   \n", "988           Gearment Co.,Ltd  Ho Chi Minh        You'll love it   \n", "989  Restaff – House Of Norway  Ho <PERSON>  Up to 18,000,000 vnđ   \n", "\n", "                                           description  \\\n", "0    We are looking for qualified MLops Engineer fo...   \n", "1    We are seeking an experienced Senior DevOps En...   \n", "2    Ecotek đang dẫn dắt Ecopark phát triển trở thà...   \n", "3    <PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON>vũ tr<PERSON>” c<PERSON><PERSON> Viettel, n<PERSON><PERSON> bạn k...   \n", "4    <PERSON><PERSON><PERSON> hơn 1200 nhân sự chất l<PERSON><PERSON>o , Tổng Côn...   \n", "..                                                 ...   \n", "985  About Us\\n At EATLAB, we're a team that is har...   \n", "986  Trung tâm Công nghệ thông tin là đơn vị xây dự...   \n", "987  Trung tâm Công nghệ thông tin là đơn vị xây dự...   \n", "988  JOB DESCRIPTION\\n  Design and manage Kubernete...   \n", "989  - <PERSON><PERSON> gia <PERSON><PERSON>, b<PERSON><PERSON> trì và tối <PERSON>u hóa c...   \n", "\n", "                                          requirements  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Tư duy logic tố<PERSON>, tư duy hư<PERSON><PERSON><PERSON>, tư d...   \n", "3    Bằng cấp: <PERSON><PERSON><PERSON> <PERSON>hi<PERSON><PERSON><PERSON> (loại Khá trở lên...   \n", "4    Tốt nghiệ<PERSON><PERSON> h<PERSON> ch<PERSON>h quy loại Khá trở lên ...   \n", "..                                                 ...   \n", "985  Must-Haves\\nKnowledgable in your applied field...   \n", "986  T<PERSON><PERSON> nghiệp <PERSON><PERSON> học loại khá trở lên chuyên ngà...   \n", "987  T<PERSON><PERSON> nghiệp <PERSON><PERSON> học loại khá trở lên chuyên ngà...   \n", "988  MUST HAVE:\\n  At least 2 years of experience i...   \n", "989  BẮT BUỘC:\\n- Có ít nhất 1–2 năm kinh nghiệm ph...   \n", "\n", "                                                skills language  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...       en   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...       en   \n", "2    Project Management, Business Analysis, Presale...       vi   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...       vi   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...       vi   \n", "..                                                 ...      ...   \n", "985  Python, C++, DevOps, Machine Learning, AI, AI ...       en   \n", "986   NodeJS, Java, PHP, OOP, CI/CD, Backend Developer       vi   \n", "987   NodeJS, Java, PHP, OOP, CI/CD, Backend Developer       vi   \n", "988  AWS, Linux, PostgreSql, DevOps, Cloud, Docker,...       en   \n", "989  Fullstack, HTML5, PHP, JavaScript, CSS, Larave...       vi   \n", "\n", "                                             skills_en  ...  \\\n", "0    MLOps, Python, Linux, Docker, Data Science, Te...  ...   \n", "1    AWS, DevOps, Cloud, Cloud-native Architecture,...  ...   \n", "2    Project Management, Business Analysis, Presale...  ...   \n", "3    Presale, Business Analysis, Salesforce, Pre-sa...  ...   \n", "4    Embedded, C++, Linux, C language, Embedded Eng...  ...   \n", "..                                                 ...  ...   \n", "985  Python, C++, DevOps, Machine Learning, AI, AI ...  ...   \n", "986  NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...  ...   \n", "987  NodeJS, Java, PHP, OOP, CI/CD, Backend Develop...  ...   \n", "988  AWS, Linux, PostgreSql, DevOps, Cloud, Docker,...  ...   \n", "989  Fullstack, HTML5, PHP, JavaScript, CSS, Larave...  ...   \n", "\n", "                                       requirements_en  \\\n", "0    BS or MS in Computer Science or related fields...   \n", "1    Requirements:\\nBachelor's or Master's degree i...   \n", "2    Good logical thinking, solution -oriented thin...   \n", "3    Degree: Graduated from university (good or hig...   \n", "4    Graduated with regular university or higher sp...   \n", "..                                                 ...   \n", "985  Must-Haves\\nKnowledgable in your applied field...   \n", "986  Graduated from university or higher specialize...   \n", "987  Graduated from university or higher specialize...   \n", "988  MUST HAVE:\\n  At least 2 years of experience i...   \n", "989  OBLIGATORY:\\n- At least 1–2 years of fullstack...   \n", "\n", "                                   description_cleaned  \\\n", "0    look qualified mlop engineer ekyc project help...   \n", "1    seek experienced senior devop engineer aw join...   \n", "2    ecotek dẫn dắt ecopark phát triển mô hình thàn...   \n", "3    gia nhập vũ trụ công nghệ viettel đắm chìm hàn...   \n", "4    1200 nhân sự chất lượng tổng công ty công nghi...   \n", "..                                                 ...   \n", "985  we eatlab team hardworking smart fast take act...   \n", "986  trung tâm công nghệ thông tin xây dựng triển k...   \n", "987  trung tâm công nghệ thông tin xây dựng triển k...   \n", "988  job description design manage kubernete helm c...   \n", "989  tham gia phát triển bảo trì tối ưu hóa website...   \n", "\n", "                                  requirements_cleaned  \\\n", "0    bs ms computer science relate field 1 3 year e...   \n", "1    requirement bachelor master degree computer sc...   \n", "2    tư duy logic tư duy hướng gi<PERSON>i pháp tư duy phả...   \n", "3    bằng cấp tốt nghiệp đại học trở chuyên ngành c...   \n", "4    tốt nghiệp đại học ch<PERSON>h quy loại trở chuyên n...   \n", "..                                                 ...   \n", "985  must have knowledgable apply field always upda...   \n", "986  tốt nghiệp đại học trở chuyên ngành cntt khmt ...   \n", "987  tốt nghiệp đại học trở chuyên ngành cntt khmt ...   \n", "988  must least 2 year experience devop role especi...   \n", "989  b<PERSON><PERSON> bu<PERSON> 1 2 kinh nghiệm phát triển web fullst...   \n", "\n", "                                        skills_cleaned location_group  \\\n", "0    [MLOps, Python, Linux, Docker, Data Science, T...              2   \n", "1    [A<PERSON>, DevOps, Cloud, Cloud-native Architecture...              2   \n", "2    [Project Management, Business Analysis, Presal...              1   \n", "3    [Presale, Business Analysis, Salesforce, Pre-s...              1   \n", "4    [Embedded, C++, Linux, C language, Embedded En...              1   \n", "..                                                 ...            ...   \n", "985  [Python, C++, DevOps, Machine Learning, AI, AI...              1   \n", "986  [NodeJS, Java, PHP, OOP, CI/CD, Backend Develo...              1   \n", "987  [NodeJS, Java, PHP, OOP, CI/CD, Backend Develo...              2   \n", "988  [AWS, Linux, PostgreSql, DevOps, Cloud, Docker...              2   \n", "989  [Fullstack, HTML5, PHP, JavaScript, CSS, Larav...              2   \n", "\n", "     type_salary                                     primary_skills  \\\n", "0              5  [python, java, javascript, c++, tensorflow, py...   \n", "1              5                                [python, bash, aws]   \n", "2              5                                                 []   \n", "3              5                                    [lua, electron]   \n", "4              2                                         [electron]   \n", "..           ...                                                ...   \n", "985            2                  [flask, fastapi, computer vision]   \n", "986            5                                   [java, php, aws]   \n", "987            5                                   [java, php, aws]   \n", "988            5  [shell script, bash, powershell, postgresql, aws]   \n", "989            1          [java, javascript, php, laravel, symfony]   \n", "\n", "                                      secondary_skills  \\\n", "0                                 [git, docker, linux]   \n", "1    [git, gitlab, git flow, docker, jenkins, teamc...   \n", "2                                                   []   \n", "3                                                   []   \n", "4                                         [git, linux]   \n", "..                                                 ...   \n", "985                                          [segment]   \n", "986                                     [agile, scrum]   \n", "987                                     [agile, scrum]   \n", "988  [git, gith<PERSON>, docker, kube<PERSON><PERSON>, jenkins, ter...   \n", "989                                                 []   \n", "\n", "                                            adjectives  \\\n", "0    [Great, proficient, other, Familiar, Basic, pl...   \n", "1    [related, Good, more, technical, logical, high...   \n", "2    [critical, General, Good, technical, least, li...   \n", "3    [English, technical, technological, Dynamic, r...   \n", "4    [electronic, English, Good, technical, similar...   \n", "..                                                 ...   \n", "985  [more, picky, linear, capable, Knowledgable, d...   \n", "986  [systematic, related, specialized, GCP, equiva...   \n", "987  [systematic, related, specialized, GCP, equiva...   \n", "988  [NICE, similar, Willing, least, premise, publi...   \n", "989  [independent, Japanese, detailed, least, equiv...   \n", "\n", "                                               adverbs  \n", "0                                                   []  \n", "1              [least, effectively, at, independently]  \n", "2                       [directly, first, clearly, At]  \n", "3                                             [always]  \n", "4                                                 [at]  \n", "..                                                 ...  \n", "985  [easily, e.g., incredibly, always, so, back, t...  \n", "986                                  [least, KHMT, At]  \n", "987                                  [least, KHMT, At]  \n", "988                                   [especially, At]  \n", "989                                         [directly]  \n", "\n", "[841 rows x 21 columns]"]}, "execution_count": 137, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 138, "id": "c36f9956", "metadata": {}, "outputs": [], "source": ["df.to_csv('../data/clean/clean_jobs_v2.csv', index=False, encoding='utf-8')"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}