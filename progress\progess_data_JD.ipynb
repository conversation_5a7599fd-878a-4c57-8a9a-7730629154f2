

import pandas as pd
from langdetect import detect
from googletrans import Translator

csv_jd = "../data/raw/itviec_jobs_undetected.csv"

df = pd.read_csv(csv_jd)

print(df.head(10))


def detect_language(text):
    try:
        return detect(text)
    except:
        return 'unknown'

# Áp dụng cho cột 'description'
df['language'] = df['description'].apply(detect_language)

print(df[['id', 'title', 'language']].head())