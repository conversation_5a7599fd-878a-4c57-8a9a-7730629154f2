import pandas as pd
from langdetect import detect
from deep_translator import GoogleTranslator

translator = GoogleTranslator()

csv_jd = "../data/raw/itviec_jobs_undetected.csv"

df = pd.read_csv(csv_jd)

print(df.head(10))


def detect_language(text):
    try:
        return detect(text)
    except:
        return 'unknown'
def translate_to_english(text, lang):
    if lang == 'vi':
        try:
            return translator.translate(text, src='vi', dest='en').text
        except Exception as e:
            print(f"Error translating: {e}")
            return text
    return text


# Áp dụng cho cột 'description'
df['language'] = df['description'].apply(detect_language)
df['description_en'] = df.apply(lambda x: translate_to_english(x['description'], x['language']), axis=1)
df['requirements_en'] = df.apply(lambda x: translate_to_english(x['requirements'], x['language']), axis=1)

print(df[['id', 'title', 'language']].head())