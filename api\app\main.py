#!/usr/bin/env python3
"""
FastAPI Application for Job-Resume Matching
Using XGBoost model for predictions
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Optional
import joblib
import json
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Job-Resume Matching API",
    description="API for predicting job-resume suitability using XGBoost model",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model and metadata
model = None
metadata = None

# Pydantic models for request/response
class SimilarityFeatures(BaseModel):
    """Input features for prediction"""
    primary_skills_sim: float = Field(..., ge=0.0, le=1.0, description="Primary skills similarity (0-1)")
    secondary_skills_sim: float = Field(..., ge=0.0, le=1.0, description="Secondary skills similarity (0-1)")
    adjectives_sim: float = Field(..., ge=0.0, le=1.0, description="Adjectives similarity (0-1)")

    class Config:
        schema_extra = {
            "example": {
                "primary_skills_sim": 0.75,
                "secondary_skills_sim": 0.60,
                "adjectives_sim": 0.45
            }
        }

class PredictionResponse(BaseModel):
    """Response model for predictions"""
    suitability_class: int = Field(..., description="Predicted suitability class (0, 1, 2)")
    suitability_label: str = Field(..., description="Human-readable suitability label")
    confidence_scores: Dict[str, float] = Field(..., description="Confidence scores for each class")
    features_used: Dict[str, float] = Field(..., description="Input features used for prediction")

class BatchPredictionRequest(BaseModel):
    """Request model for batch predictions"""
    features_list: List[SimilarityFeatures] = Field(..., description="List of feature sets for batch prediction")

class BatchPredictionResponse(BaseModel):
    """Response model for batch predictions"""
    predictions: List[PredictionResponse] = Field(..., description="List of predictions")
    total_predictions: int = Field(..., description="Total number of predictions made")

class ModelInfo(BaseModel):
    """Model information response"""
    model_type: str = Field(..., description="Type of model")
    feature_names: List[str] = Field(..., description="Names of features used by the model")
    feature_count: int = Field(..., description="Number of features")
    suitability_mapping: Dict[int, str] = Field(..., description="Mapping of class numbers to labels")
    model_performance: Dict[str, float] = Field(..., description="Model performance metrics")

def load_model_and_metadata():
    """Load the trained XGBoost model and metadata"""
    global model, metadata

    try:
        models_dir = Path("models")

        # Load model
        model_path = models_dir / "xgboost_model.joblib"
        if model_path.exists():
            model = joblib.load(model_path)
            logger.info(f"✅ Model loaded from {model_path}")
        else:
            logger.error(f"❌ Model file not found: {model_path}")
            return False

        # Load metadata
        metadata_path = models_dir / "model_metadata.json"
        if metadata_path.exists():
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            logger.info(f"✅ Metadata loaded from {metadata_path}")
        else:
            logger.error(f"❌ Metadata file not found: {metadata_path}")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ Error loading model: {str(e)}")
        return False

def get_model():
    """Dependency to ensure model is loaded"""
    if model is None:
        raise HTTPException(status_code=503, detail="Model not loaded. Please check server logs.")
    return model

def prepare_features(features: SimilarityFeatures) -> np.ndarray:
    """Prepare features for prediction"""
    # Create base feature vector
    feature_dict = {
        'primary_skills_sim': features.primary_skills_sim,
        'secondary_skills_sim': features.secondary_skills_sim,
        'adjectives_sim': features.adjectives_sim
    }

    # Add engineered features (same as in training)
    # Weighted similarity
    weighted_sim = (
        features.primary_skills_sim * 0.4 +
        features.secondary_skills_sim * 0.3 +
        features.adjectives_sim * 0.2
    )
    feature_dict['weighted_similarity'] = weighted_sim

    # Similarity statistics
    sim_values = [features.primary_skills_sim, features.secondary_skills_sim, features.adjectives_sim]
    feature_dict['similarity_variance'] = np.var(sim_values)
    feature_dict['similarity_mean'] = np.mean(sim_values)
    feature_dict['similarity_max'] = np.max(sim_values)
    feature_dict['similarity_min'] = np.min(sim_values)

    # Create feature vector in the same order as training
    feature_names = metadata['feature_names']
    feature_vector = []

    for feature_name in feature_names:
        if feature_name in feature_dict:
            feature_vector.append(feature_dict[feature_name])
        else:
            # Default value for missing features
            feature_vector.append(0.0)

    return np.array(feature_vector).reshape(1, -1)

# Startup event
@app.on_event("startup")
async def startup_event():
    """Load model on startup"""
    logger.info("🚀 Starting Job-Resume Matching API...")
    success = load_model_and_metadata()
    if not success:
        logger.error("❌ Failed to load model. API will not function properly.")
    else:
        logger.info("✅ API ready to serve predictions!")

# Health check endpoint
@app.get("/", tags=["Health"])
async def root():
    """Root endpoint - health check"""
    return {
        "message": "Job-Resume Matching API",
        "status": "healthy",
        "model_loaded": model is not None,
        "version": "1.0.0"
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "model_loaded": model is not None,
        "metadata_loaded": metadata is not None,
        "ready_for_predictions": all([model is not None, metadata is not None])
    }

# Model information endpoint
@app.get("/model/info", response_model=ModelInfo, tags=["Model"])
async def get_model_info():
    """Get information about the loaded model"""
    if metadata is None:
        raise HTTPException(status_code=503, detail="Model metadata not loaded")

    return ModelInfo(
        model_type="XGBoost Classifier",
        feature_names=metadata['feature_names'],
        feature_count=metadata['feature_count'],
        suitability_mapping=metadata['suitability_mapping'],
        model_performance=metadata['model_performance']
    )

# Single prediction endpoint
@app.post("/predict", response_model=PredictionResponse, tags=["Prediction"])
async def predict_suitability(features: SimilarityFeatures, model_dep=Depends(get_model)):
    """Predict job-resume suitability for a single input"""
    try:
        # Prepare features
        feature_vector = prepare_features(features)

        # Make prediction
        prediction = model_dep.predict(feature_vector)[0]

        # Get prediction probabilities
        probabilities = model_dep.predict_proba(feature_vector)[0]

        # Create confidence scores
        confidence_scores = {}
        for class_id, prob in enumerate(probabilities):
            label = metadata['suitability_mapping'][str(class_id)]
            confidence_scores[label] = float(prob)

        # Get suitability label
        suitability_label = metadata['suitability_mapping'][str(prediction)]

        # Prepare features used
        features_used = {
            "primary_skills_sim": features.primary_skills_sim,
            "secondary_skills_sim": features.secondary_skills_sim,
            "adjectives_sim": features.adjectives_sim
        }

        return PredictionResponse(
            suitability_class=int(prediction),
            suitability_label=suitability_label,
            confidence_scores=confidence_scores,
            features_used=features_used
        )

    except Exception as e:
        logger.error(f"Prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

# Batch prediction endpoint
@app.post("/predict/batch", response_model=BatchPredictionResponse, tags=["Prediction"])
async def predict_batch_suitability(request: BatchPredictionRequest, model_dep=Depends(get_model)):
    """Predict job-resume suitability for multiple inputs"""
    try:
        predictions = []

        for features in request.features_list:
            # Prepare features
            feature_vector = prepare_features(features)

            # Make prediction
            prediction = model_dep.predict(feature_vector)[0]

            # Get prediction probabilities
            probabilities = model_dep.predict_proba(feature_vector)[0]

            # Create confidence scores
            confidence_scores = {}
            for class_id, prob in enumerate(probabilities):
                label = metadata['suitability_mapping'][str(class_id)]
                confidence_scores[label] = float(prob)

            # Get suitability label
            suitability_label = metadata['suitability_mapping'][str(prediction)]

            # Prepare features used
            features_used = {
                "primary_skills_sim": features.primary_skills_sim,
                "secondary_skills_sim": features.secondary_skills_sim,
                "adjectives_sim": features.adjectives_sim
            }

            predictions.append(PredictionResponse(
                suitability_class=int(prediction),
                suitability_label=suitability_label,
                confidence_scores=confidence_scores,
                features_used=features_used
            ))

        return BatchPredictionResponse(
            predictions=predictions,
            total_predictions=len(predictions)
        )

    except Exception as e:
        logger.error(f"Batch prediction error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch prediction failed: {str(e)}")

# Feature engineering endpoint (for debugging)
@app.post("/features/engineer", tags=["Debug"])
async def engineer_features(features: SimilarityFeatures):
    """Show how features are engineered (for debugging purposes)"""
    try:
        feature_vector = prepare_features(features)

        if metadata is None:
            raise HTTPException(status_code=503, detail="Model metadata not loaded")

        # Create feature mapping
        feature_mapping = {}
        feature_names = metadata['feature_names']

        for i, feature_name in enumerate(feature_names):
            if i < len(feature_vector[0]):
                feature_mapping[feature_name] = float(feature_vector[0][i])

        return {
            "input_features": {
                "primary_skills_sim": features.primary_skills_sim,
                "secondary_skills_sim": features.secondary_skills_sim,
                "adjectives_sim": features.adjectives_sim
            },
            "engineered_features": feature_mapping,
            "feature_vector_shape": feature_vector.shape,
            "total_features": len(feature_names)
        }

    except Exception as e:
        logger.error(f"Feature engineering error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Feature engineering failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)