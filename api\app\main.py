from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict
import joblib
import json
import numpy as np
import pandas as pd
from pathlib import Path
import PyPDF2
import io
import re
import nltk
import spacy
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords
from nltk.tag import pos_tag
from utils.read_file import read_skills
from utils.extract import extract_primary_skills, extract_secondary_skills, extract_adjectives, extract_adverbs
import nltk
nltk.download('punkt')
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('corpora/stopwords')
    nltk.data.find('taggers/averaged_perceptron_tagger')
except LookupError:
    nltk.download('punkt')
    nltk.download('stopwords')
    nltk.download('averaged_perceptron_tagger')

nlp_en = spacy.load('en_core_web_md')
app = FastAPI(title="Job-Resume Matching API", version="1.0.0")
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"])
global primary_skills 
global secondary_skills 
primary_skills = read_skills('app/primary_skills.txt')
secondary_skills = read_skills('app/secondary_skills.txt')


model = None
preprocessing = None
metadata = None

class PrimarySkillsResponse(BaseModel):
    primary_skills: List[str]

class AllFeaturesResponse(BaseModel):
    primary_skills: List[str]
    secondary_skills: List[str]
    adverbs: List[str]
    adjectives: List[str]

class PredictionResponse(BaseModel):
    suitability_class: int
    suitability_label: str
    confidence_scores: Dict[str, float]

def extract_text_from_pdf(file_content: bytes) -> str:
    try:
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(file_content))
        text = ""
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    except:
        return ""

def extract_all_features(text: str) -> Dict[str, List[str]]:
    

    primarys = extract_primary_skills(text,primary_skills)

    secondarys = extract_secondary_skills(text, secondary_skills)
    adverbs = extract_adverbs(text, nlp_en)
    adjectives = extract_adjectives(text, nlp_en)

    return {
        'primary_skills': primarys,
        'secondary_skills': secondarys,
        'adverbs': adverbs,
        'adjectives': adjectives
    }

def load_model():
    global model, preprocessing, metadata
    try:
        models_dir = Path("models")
        print(f"Loading model from: {models_dir.absolute()}")

        # Check if files exist
        model_file = models_dir / "best_model.joblib"
        preprocessing_file = models_dir / "preprocessing.joblib"
        metadata_file = models_dir / "model_metadata.json"

        if not model_file.exists():
            print(f"❌ Model file not found: {model_file}")
            return False
        if not preprocessing_file.exists():
            print(f"❌ Preprocessing file not found: {preprocessing_file}")
            return False
        if not metadata_file.exists():
            print(f"❌ Metadata file not found: {metadata_file}")
            return False

        model = joblib.load(model_file)
        preprocessing = joblib.load(preprocessing_file)
        with open(metadata_file) as f:
            metadata = json.load(f)

        print("✅ Model loaded successfully!")
        return True
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def calculate_similarity(features1: Dict, features2: Dict) -> Dict[str, float]:
    def jaccard_similarity(set1: set, set2: set) -> float:
        if not set1 and not set2:
            return 1.0
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        return intersection / union if union > 0 else 0.0

    return {
        'primary_skills_sim': jaccard_similarity(set(features1['primary_skills']), set(features2['primary_skills'])),
        'secondary_skills_sim': jaccard_similarity(set(features1['secondary_skills']), set(features2['secondary_skills'])),
        'adjectives_sim': jaccard_similarity(set(features1['adjectives']), set(features2['adjectives'])),
        'adverbs_sim': jaccard_similarity(set(features1['adverbs']), set(features2['adverbs']))
    }

def predict_suitability(similarities: Dict[str, float]) -> Dict:
    # Check if model is loaded
    if model is None or preprocessing is None or metadata is None:
        raise HTTPException(status_code=500, detail="Model not loaded. Please check model files.")

    feature_vector = []
    for feature_name in metadata['feature_names']:
        feature_vector.append(similarities.get(feature_name, 0.0))

    X = np.array(feature_vector).reshape(1, -1)
    X_scaled = preprocessing['scaler'].transform(X)
    X_selected = preprocessing['feature_selector'].transform(X_scaled)

    prediction = model.predict(X_selected)[0]
    probabilities = model.predict_proba(X_selected)[0]

    confidence_scores = {}
    for i, prob in enumerate(probabilities):
        confidence_scores[metadata['suitability_mapping'][str(i)]] = float(prob)

    return {
        'suitability_class': int(prediction),
        'suitability_label': metadata['suitability_mapping'][str(prediction)],
        'confidence_scores': confidence_scores
    }

@app.on_event("startup")
async def startup_event():
    load_model()

@app.get("/")
async def root():
    return {"message": "Job-Resume Matching API", "status": "healthy"}

@app.post("/extract/primary-skills", response_model=PrimarySkillsResponse)
async def extract_primary_skills_api(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(status_code=400, detail="Only PDF files are supported")

    content = await file.read()
    text = extract_text_from_pdf(content)

    if not text:
        raise HTTPException(status_code=400, detail="Could not extract text from PDF")

    skills = extract_primary_skills(text)
    return PrimarySkillsResponse(primary_skills=skills)

@app.post("/extract/all-features", response_model=AllFeaturesResponse)
async def extract_all_features_api(file: UploadFile = File(...)):
    if file.content_type != "application/pdf":
        raise HTTPException(status_code=400, detail="Only PDF files are supported")

    content = await file.read()
    text = extract_text_from_pdf(content)

    if not text:
        raise HTTPException(status_code=400, detail="Could not extract text from PDF")

    features = extract_all_features(text)
    return AllFeaturesResponse(**features)

@app.post("/predict", response_model=PredictionResponse)
async def predict_match(cv_file: UploadFile = File(...), jd_file: UploadFile = File(...)):
    if cv_file.content_type not in ["application/pdf", "text/plain"]:
        raise HTTPException(status_code=400, detail="CV file must be PDF or TXT")
    if jd_file.content_type not in ["application/pdf", "text/plain"]:
        raise HTTPException(status_code=400, detail="JD file must be PDF or TXT")

    cv_content = await cv_file.read()
    jd_content = await jd_file.read()

    if cv_file.content_type == "application/pdf":
        cv_text = extract_text_from_pdf(cv_content)
    else:
        cv_text = cv_content.decode('utf-8')

    if jd_file.content_type == "application/pdf":
        jd_text = extract_text_from_pdf(jd_content)
    else:
        jd_text = jd_content.decode('utf-8')

    if not cv_text or not jd_text:
        raise HTTPException(status_code=400, detail="Could not extract text from files")

    cv_features = extract_all_features(cv_text)
    jd_features = extract_all_features(jd_text)

    similarities = calculate_similarity(cv_features, jd_features)
    prediction = predict_suitability(similarities)

    return PredictionResponse(**prediction)

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(app, host="0.0.0.0", port=8000)