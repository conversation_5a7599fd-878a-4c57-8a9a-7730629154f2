import pandas as pd
import matplotlib.pyplot as plt
from ast import literal_eval

# Đọc file CSV
df_jd = pd.read_csv('../data/clean/clean_jobs_v2.csv')
df_cr = pd.read_csv('../data/clean/clean_resumes_v2.csv')


# Hàm chuyển đổi an toàn: xử lý cả chuỗi và danh sách
def safe_literal_eval(val):
    if isinstance(val, list):  # Nếu đã là danh sách, giữ nguyên
        return val
    if pd.isna(val):  # Nếu là NaN, trả về danh sách rỗng
        return []
    try:
        return literal_eval(val)  # Thử chuyển chuỗi thành danh sách
    except (ValueError, SyntaxError):
        return []  # Nếu lỗi, trả về danh sách rỗng

# Đọc file CS
# Kiểm tra kiểu dữ liệu ban đầu
print("JD DataFrame Info:")
print(df_jd[['primary_skills', 'secondary_skills', 'adjectives', 'adverbs']].head())
print("\nCR DataFrame Info:")
print(df_cr[['primary_skills', 'secondary_skills', 'adjectives', 'adverbs']].head())

# Chuyển đổi các cột đặc trưng thành danh sách
for col in ['primary_skills', 'secondary_skills', 'adjectives', 'adverbs']:
    df_jd[col] = df_jd[col].apply(safe_literal_eval)
    df_cr[col] = df_cr[col].apply(safe_literal_eval)

# Kiểm tra sau khi chuyển đổi
print("\nJD sau khi chuyển đổi:")
print(df_jd[['primary_skills', 'secondary_skills', 'adjectives', 'adverbs']].head())
print("\nCR sau khi chuyển đổi:")
print(df_cr[['primary_skills', 'secondary_skills', 'adjectives', 'adverbs']].head())

# Hàm Jaccard Similarity
def jaccard_similarity(list1, list2):
    set1, set2 = set(list1), set(list2)
    return len(set1.intersection(set2)) / len(set1.union(set2)) if len(set1.union(set2)) > 0 else 0

# Tính Jaccard Similarity
similarity_scores = []
for _, jd in df_jd.iterrows():
    for _, cr in df_cr.iterrows():
        scores = {
            'jd_id': jd['id'],
            'cr_category': cr['Category'],
            'primary_skills_sim': jaccard_similarity(jd['primary_skills'], cr['primary_skills']),
            'secondary_skills_sim': jaccard_similarity(jd['secondary_skills'], cr['secondary_skills']),
            'adjectives_sim': jaccard_similarity(jd['adjectives'], cr['adjectives']),
            'adverbs_sim': jaccard_similarity(jd['adverbs'], cr['adverbs'])
        }
        similarity_scores.append(scores)

df_similarity = pd.DataFrame(similarity_scores)

# Gán nhãn Suitability
def assign_suitability(row):
    total_sim = (0.4 * row['primary_skills_sim'] + 
                 0.3 * row['secondary_skills_sim'] + 
                 0.2 * row['adjectives_sim'] + 
                 0.1 * row['adverbs_sim'])
    if total_sim >= 0.6:
        return 'Match'
    elif total_sim >= 0.3:
        return 'Neutral'
    else:
        return 'Mismatch'

df_similarity['suitability'] = df_similarity.apply(assign_suitability, axis=1)

# Lưu kết quả
df_similarity.to_csv('jd_cr_similarity.csv', index=False)
print("\nKết quả Similarity Sample:")
print(df_similarity[['jd_id', 'cr_category', 'suitability']].head())

# Vẽ biểu đồ tròn
class_counts = df_similarity['suitability'].value_counts()
labels = class_counts.index
sizes = class_counts.values
colors = ['#66b3ff', '#ff9999', '#99ff99']
explode = (0.1, 0, 0)

plt.figure(figsize=(8, 8))
plt.pie(sizes, explode=explode, labels=labels, colors=colors, autopct='%1.1f%%', shadow=True, startangle=90)
plt.title('Class Distribution for Suitability')
plt.axis('equal')
plt.savefig('class_distribution_suitability.png')
plt.show()