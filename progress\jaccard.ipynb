{"cells": [{"cell_type": "code", "execution_count": 42, "id": "e0514e1c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Số lượng Job Descriptions: 841\n", "Số lượng Candidate Resumes: 826\n", "\n", "Cột trong JD: ['id', 'title', 'company', 'location', 'salary', 'description', 'requirements', 'skills', 'language', 'skills_en', 'description_en', 'requirements_en', 'description_cleaned', 'requirements_cleaned', 'skills_cleaned', 'location_group', 'type_salary', 'primary_skills', 'secondary_skills', 'adjectives', 'adverbs']\n", "Cột trong CR: ['Category', 'Resume', 'resume_cleaned', 'primary_skills', 'secondary_skills', 'adjectives', 'adverbs', 'id']\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from ast import literal_eval\n", "import seaborn as sns\n", "\n", "# Đọc file CSV\n", "df_jd = pd.read_csv('../data/clean/clean_jobs_v2.csv')\n", "df_cr = pd.read_csv('../data/clean/clean_resumes_v2.csv')\n", "\n", "print(f\"Số lượng Job Descriptions: {len(df_jd)}\")\n", "print(f\"Số lượng Candidate Resumes: {len(df_cr)}\")\n", "print(f\"\\nCột trong JD: {df_jd.columns.tolist()}\")\n", "print(f\"Cột trong CR: {df_cr.columns.tolist()}\")"]}, {"cell_type": "code", "execution_count": 56, "id": "2d6252df", "metadata": {}, "outputs": [], "source": ["\n", "# Hàm chuyển đổi an toàn: xử lý cả chuỗi và danh sách\n", "def safe_literal_eval(val):\n", "    if isinstance(val, list):\n", "        return val\n", "    if pd.isna(val):\n", "        return []\n", "    try:\n", "        return literal_eval(val)\n", "    except (<PERSON><PERSON><PERSON><PERSON>, SyntaxError):\n", "        return []\n", "def jaccard_similarity(list1, list2):\n", "    set1, set2 = set(list1), set(list2)\n", "    return len(set1 & set2) / len(set1 | set2) if set1 | set2 else 0\n", "\n", "def assign_suitability(score):\n", "    if score > 0.6:\n", "        return 'Most Suitable'\n", "    elif score > 0.1:\n", "        return 'Moderately Suitable'\n", "    else:\n", "        return 'Not Suitable'\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "58fc5c9a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   jd_id   cr_category  total_similarity          suitability\n", "0  JOB_0  Data Science          1.341837        Most Suitable\n", "1  JOB_0  Data Science          0.416667  Moderately Suitable\n", "2  JOB_0  Data Science          0.444444  Moderately Suitable\n", "3  JOB_0  Data Science          0.277778  Moderately Suitable\n", "4  JOB_0  Data Science          0.181818  Moderately Suitable\n", "5  JOB_0  Data Science          0.153846  Moderately Suitable\n", "6  JOB_0  Data Science          0.166667  Moderately Suitable\n", "7  JOB_0  Data Science          0.358974  Moderately Suitable\n", "8  JOB_0  Data Science          0.133333  Moderately Suitable\n", "9  JOB_0  Data Science          0.166667  Moderately Suitable\n"]}], "source": ["\n", "# <PERSON><PERSON><PERSON><PERSON> các cột từ chuỗi sang list\n", "for col in ['primary_skills', 'secondary_skills', 'adjectives', 'adverbs']:\n", "    df_jd[col] = df_jd[col].apply(safe_literal_eval)\n", "    df_cr[col] = df_cr[col].apply(safe_literal_eval)\n", "\n", "# Tính similarity\n", "similarity_scores = []\n", "for _, jd in df_jd.iterrows():\n", "    for _, cr in df_cr.iterrows():\n", "        primary_sim = jaccard_similarity(jd['primary_skills'], cr['primary_skills'])\n", "        secondary_sim = jaccard_similarity(jd['secondary_skills'], cr['secondary_skills'])\n", "        adj_sim = jac<PERSON>_similarity(jd['adjectives'], cr['adjectives'])\n", "        adj_weight = len(cr['adjectives']) if cr['adjectives'] else 1  # tránh nhân 0\n", "\n", "        total_sim = primary_sim + secondary_sim + (adj_sim * adj_weight)\n", "\n", "        scores = {\n", "            'jd_id': jd['id'],\n", "            'cr_category': cr['Category'],\n", "            'primary_skills_sim': primary_sim,\n", "            'secondary_skills_sim': secondary_sim,\n", "            'adjectives_sim': adj_sim,\n", "            'adj_weight': adj_weight,\n", "            'total_similarity': total_sim,\n", "            'suitability': assign_suitability(total_sim)\n", "        }\n", "        similarity_scores.append(scores)\n", "\n", "df_similarity = pd.DataFrame(similarity_scores)\n", "\n", "# <PERSON><PERSON><PERSON> n<PERSON>u cần: lo<PERSON><PERSON> các dòng không có primary_sim\n", "df_similarity = df_similarity[df_similarity['primary_skills_sim'] > 0]\n", "\n", "# <PERSON><PERSON><PERSON> kết quả\n", "df_similarity.to_csv('jd_cr_similarity.csv', index=False)\n", "\n", "# <PERSON><PERSON><PERSON> thị 10 dòng đầu\n", "print(df_similarity[['jd_id', 'cr_category', 'total_similarity', 'suitability']].head(10))"]}, {"cell_type": "code", "execution_count": 58, "id": "7fa8a183", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<PERSON><PERSON><PERSON> quả Similarity Sample:\n", "   jd_id   cr_category  primary_skills_sim  secondary_skills_sim  \\\n", "0  JOB_0  Data Science            0.178571              0.285714   \n", "1  JOB_0  Data Science            0.166667              0.250000   \n", "2  JOB_0  Data Science            0.222222              0.222222   \n", "3  JOB_0  Data Science            0.111111              0.166667   \n", "4  JOB_0  Data Science            0.181818              0.000000   \n", "5  JOB_0  Data Science            0.153846              0.000000   \n", "6  JOB_0  Data Science            0.166667              0.000000   \n", "7  JOB_0  Data Science            0.192308              0.166667   \n", "8  JOB_0  Data Science            0.133333              0.000000   \n", "9  JOB_0  Data Science            0.166667              0.000000   \n", "\n", "   total_similarity          suitability  \n", "0          1.341837        Most Suitable  \n", "1          0.416667  Moderately Suitable  \n", "2          0.444444  Moderately Suitable  \n", "3          0.277778  Moderately Suitable  \n", "4          0.181818  Moderately Suitable  \n", "5          0.153846  Moderately Suitable  \n", "6          0.166667  Moderately Suitable  \n", "7          0.358974  Moderately Suitable  \n", "8          0.133333  Moderately Suitable  \n", "9          0.166667  Moderately Suitable  \n", "\n", "<PERSON><PERSON> bố nhãn Suitability:\n", "suitability\n", "Moderately Suitable    196855\n", "Most Suitable          109771\n", "Not Suitable            31807\n", "Name: count, dtype: int64\n", "\n", "Tổng số cặp JD-CR: 338433\n", "\n", "Thống kê Primary Skills Similarity:\n", "Mean: 0.1944\n", "Median: 0.1667\n", "Min: 0.0357\n", "Max: 1.0000\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA24AAAKTCAYAAABhKlsUAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAAvoFJREFUeJzs3Qd8XFeZ/vFHvUvuvffeSxzHJb2RkAAhEHrvLPxpC4SapS0svSxLSYBAAum9J3bixE4cx733Jlm9d83c/+e98hhZHkkjj6Q75fcNwtJo5s6Z0cyd+9zznnMSHMdxBAAAAACIWIleNwAAAAAA0DmCGwAAAABEOIIbAAAAAEQ4ghsAAAAARDiCGwAAAABEOIIbAAAAAEQ4ghsAAAAARDiCGwAAAABEOIIbAAAAAEQ4ghsi2re//W0lJCRozZo1inWrV692H6sX7rjjDve+7d+27DJrl1fe//73u204cuSIIsXrr7+uyy+/XIMHD3bbNm/ePMWavnjeu/t6t32AXd/2CV1tp6Pr9qX8/HxlZWXp+9//vuJBPO2rI2kfGYv7lGh7r0fK5/+KFSu0dOnSHmsTIhPBDX1u06ZN+tCHPqTJkye7BzYZGRmaOHGi3vOe9+iZZ55RLHw4Bb6Sk5PVv39/zZgxQ+9617t07733qqmpqcfvN1o/vDoKjJGqqqpK1157rV577TXdfPPN+ta3vqWPf/zjfd6O0tJS/ed//qdmzpypzMxM92vs2LG69NJL9Z3vfEeFhYU9fp/R+hpra9y4ce5XX/j617/u/l0++9nPntOGtvsI+0pLS9P48eP10Y9+NKJOUsSiwD6n7Zd9Bk2bNk3/7//9P5WUlHjdxKjgOI7uvPNOXXLJJRo4cKBSU1M1dOhQzZ8/X5/85Ce1du1axQKfz6fbb7/9zMk6e5zDhg3Tddddp/vuu0+RxPbN9tl09913e90U9KLk3tw40Jbf79cXv/hF/exnP3MDje3wr7/+eqWkpOjQoUN67LHH3A+C7373u/rGN76haGbBdNSoUe6Hmx3s79+/X4888oj+8Y9/aPr06e6Odc6cOWfd5q9//avq6uo8ae+NN96oCy64QMOHD1ck+cEPfuAGlJEjRyoS2IdiUVGRvve97+lrX/uaJ204ceKELrzwQh0/ftzt7fvABz6gfv36qaCgQK+88or74b18+XL3ICpWnnezZMkS7d69W4MGDerR6/YGe7/b+9nCW3Z29jm/T0pK0q233nrm54qKCr366qv6wx/+oPvvv19vvPGGxowZ08etji92kuOiiy5yvy8uLtZTTz3lfjbZ828nFy2MhMJeZxbQ480HP/hBNwTbick3velN7r6ivr5eW7du1Z/+9Cf3c2/VqlXntW2v378Btq9/85vfrA0bNrifjfb9kCFD3H2wHa88+uijboC766673JPQkfCaXrBggXtC0U4selXBg95FcEOfsQMV+2C0g03rebJetrZsp//rX//a7U2Idh/+8IfdINRWdXW1u0O15+CKK65wD85GjBhx5vdeHqjl5eW5X5HGPiwjKUxa+Ztp+3fra/YastDW0QmO7du3u0Eulp53YwfH1ivS09ftDf/3f//nnqiyKoJg7MRVsJ7LT33qU/rtb3+rP/7xj+7fF73nsssuc09OBDQ3N+vKK6/UCy+8oF/96lch9yx7+TrzyksvveSGNvsst5613Nzcs35vJyJ27dp13tv3+v0beD3ccMMNbmizE7H2mrCe2baP8d3vfrd7QtZOnv3rX/9SJLA2Wc/x888/7wY5xB5KJdEnDhw4oP/+7/92z2I++eST54Q2YzvFL33pS26pV1f+/Oc/u2e/rOwoPT1dAwYMOPOhG4yVNNjZPztbZte3A2/74G5f6mC3v/rqq93fW/mS9VpY3bgdiIUrJydHP/3pT91ySitl+6//+q8ua9zt4M8O4uwMpD1Ge46sJ8/O8gXGktgBxsUXX+x+b89d2xKgQNlVoITTejb/53/+xy3dtMdnl4dSsmhnGN/5zne6Z0DtQ9V6dJ599tlu1em3H+NgP9sHnrF/27a7o9u0ZeUrVs9vPRr2Zd8Ha3/bEr/A+DT7W1hQtZ7GUEvTbBvve9/7zmlv2/s8evSo+yFvZ5+tpMb+VvbzsWPHOnyuGhoa3JMa9p6w3ueuDhjXr1/v/vuZz3wm6O9nz56t0aNHn/nZHp/dT+BvHcoYnfbPeyivsX379unLX/6ye8bX3uf2PpsyZYp7cFxTU9Ph47HHb9exExd2G+uRtoMk660+31LN9tcNPAf297Gvtu2369hr2b63Eq9gDh48qMTERHcf0xV7z/7lL39xD2qtHLw7rrrqKvff9uV63X1urff1P/7jP9z7t32GBXl7Xq2st7Ky8qzrWum27Zds29ZrYO8N2+c9/PDDQdtoJw1sX2D7I3vf2X71xRdf1Pl4+eWX3dJj25Y9JjtYtxMTwSoPAq9T23fa+9D2RfbY7ARZT4yrs/fexz72Mff7jRs3nvM6st5sO+Fmz2XbfVRn7x/b3/7kJz9x/1bWVtvvBsrY7Hm3HtnAZ5hVYDzxxBPntMt6/z796U9r1qxZ7j7LtmPv8R/+8IduuOioHNiChd3O9gV2osD2U9bDaN/b6yOY9773vW67A/uYjgR+b3+H9qHN2HNkVQGhsOfYrm/v/z179vRYWbb1ett+2kqQ7bPOXmNz587V5z73uXP2LcHYe9gep70XrCe8bWgz1uZ77rlHkyZNcv+1oBRsn2vHPvY5Yz2T9v6y4w7rleyKfe7bNuy4KRi7P/t94DUbcNNNN7n/RsvwA3QfPW7oE7YTsVpx28l0VcJlO9mu2Jlp2wnbTtDqzk+ePKkHH3zQ/dlKXSzUBfzud79zD8isB8F2oHbgc+rUKbfs7YEHHtBb3/pW93pW+mCByHbIdnu7vpXQ2E72b3/7mzv+pCdYL4k9H3aG7je/+U2n5Qxf/epX3R23HdTfcsst7kGVPdZ169a5B5t2wGBf9kFhHzR2ENX2IKJ9z4sd7NsZRDtYssdqQbYr5eXlblCz59l6Eu05+ec//+keZFrPqZ2VPB92Ozu4eOihh9znuzuTfNi4ITu4t4BkwchYCLcP6s2bN+sXv/jFObexgzF7Li2A2OvQrmevGeuh2rFjh3vw1Bk7oNyyZcs57Q38awfXdmBkz489tzb+zLZrJxnsrKz9zewArj17/dlrzJ5P+3vZgUZnAiVcdn8W6PtCKK8xe99ZiZQ9v/Z7CzD2WvvRj37knpW3g3s7OG7v7W9/u/u3CLwP7e9of1+7PzvJ0BOsjfb3+/nPf+7+bAdvbR+bPSZ7j1kpsx1oty99s4MoO9j7yEc+0uV92evJXgOBx9MdTz/9tPuvhai2uvPcWuix96s9fxY0bJ9nIeHw4cPufszK1QO9642Nje7rzg6U7XVs7yULA7YvtNe4vcfs4D/ADviXLVvm7oMsxFo7raTNToYEgn2o7GDXAqDt762sy/ZF9vitp9HKFq1N7d+Ttr+w95i133ozrZTN9kXWFgs4Fm56Qvt9soU2m2TGHqN9DgQ7EROM9XxYCaztD6w81kKb7cftIN6eW+uVsn2xnbyw15495/Z8tj2xaaHB9h8rV67UNddc4/597bmxzwbbpwUbZ2V/VxuKYKHehiNYWLPPXdvvWVi2k17tS73tubX9ue237G8c6j4oHNZ2G/ttj9f+5naiq6cqI2zfWFtb6z6/9vqy7y3MWY+2vcftOemMPUfGwnVHn9EW5r7whS/oE5/4hLuft+e8LXsP2okFe06ttNROANnnh72O7O/c2bGQvTds2/a+t5M27dnrwrTfJ9lzaGH9ueee6/TxIYo5QB9YvXq1neJynn322W7d7lvf+pZ7uxdeeOGsyw8dOnTOdfPz850RI0Y4kydPPuvyBQsWOKmpqU5hYeE5tykpKTnz/Vve8hb3vrZs2dLp9Trzvve9z93G+vXrO73e6NGj3esdPHjwzGWrVq1yL2trwIAB7mOqra09ZxulpaVnvrfnx25rz1dn7Ro1apRz9OjRc35/++23u7+3f9uyy+zrlltucfx+/5nLt27d6j6ngwcPdurq6jp9DO3bcPjw4S7vt7PbrF271r1s+vTpTkVFxZnLy8rKnClTpri/e/HFF895buzr7rvvPmv773nPe9zL77rrrqD3H+rzZC6++GL3d7///e/Puvw3v/mNe/kll1xy1uWB52revHln/S278stf/tK93ZAhQ5xvfvOb7uOrrKzs8Pr23Nn17bkMxn5nbenqee/qNXbixAmnsbHxnMu/853vuLe78847gz7+qVOnnvV3tO/tsoSEBGfjxo1d3n+w11xH1x07dqz7FcyPfvQj9zZ33HHHWZc3Nzc7w4cPd5/vpqYmpyuBv/cf/vCHoL+3+09KSnLbFvj6/Oc/7yxfvtxJTEx0br755nOex+48tw8//LB72ec+97lzrl9dXe00NDSc+flrX/uae91vfOMbZ72/q6qqnEWLFrnv8ZMnT57zuviv//qvs7Zrr/nAe6z9vjoYe73m5eU5aWlp7r4kwOfzuY/ftvPd7373rNsEtv/JT37SvV7AH//4R/fyj33sY0533sM/+MEPzvk723vUfmfPa/t9x5///Oduv39sf1RUVHTm8ldffdW9vF+/fs5FF13k1NTUnPndP//5T/d3n/nMZ87alu2vW1pazrrM/lYf/OAH3euvW7funNeXXX7llVeetW829fX17mfKhAkTzvp7m1//+tfu7X7+8587XTl+/LiTm5vrvkfts+Gee+5xjhw50ult2u9Tfve737mv9wsvvNDdd7cV7ns9sI8M9lhC2dfaayElJcVJTk52n7PO7Nu3z70ve07b73Pt64c//OFZ17/11luDvv6CPbZPfOIT7mVr1qw55zHYe8c+O4K58cYb3dsFO05C9KNUEn3CerhMT51RC9YrYT1kdpbbzqpZOVRbdjY62Nn+YAPQ25dEdHS9cATGSIUyg5mV3NnZ2vas9KO7rBS1u2Pp7L7tbHPbs45W1mNnvK1n4fHHH1dfsl4fY2U0bcfl2Vls61XpqEzEzljbmde27Cxo29Ko82Vn4K3M1kqh2p8BtfI0KwGz0hYrM2vPSg+787e0HhD7O9oZcuudsLO31qNkZ3WtdK6jMqjeFigPDdZeE6y0NtAD3fbvaN9b6agdEwf+1n3Bemut/da71pb1PtlzamVhwfYhwcqKTWdn0636wP7ugS8b92o9IfY3tNdo++fxfJ7bYPsxK20MVDRYr51VI1hvR6D8NcB69r/5zW+6PXXW22fse+vdsp4x6wloy3riu1MWar0OVrJp77+2kzRZOar1igdK+9qzUjPrZbTrBdjfxa7f3fewPWe2D7Evq0Sw9669R+2zpW0vo7GexUBZd3dYb41VKgRYL9CECRPc965NcNR2Qgv77LLXV/syOttft9//29/Kqk4CjyMYex7bvwasB9OeLyvhbFvaZ6xnx14bHY3LbMs+x623zHp2rKfQyvOsPNNeG/b6bb/t9uz1Zr1U1oNo7bd9d28I9h4IZV9r4+yt59nKcbuqxAiUpQfb79pryfbVbQUqREJ5vQZmLG6/T7Kec+tV7agCILDvCeyLEFsIbohK9sFjOy076LAda2C8ipWftJ1EwrzjHe9wyySsjMZ2ohY0bMar9ux6xkob7IPbyii9nhra2mTlFtZ2O8C1D0SbxOV8nU9pnR042FTz7Vntv7Eyt74UuL9gaycFyrWspLG9hQsXnnNZ4ESCHUiFI3B/VnLXvqzGDjItNHbUru7+TQLjHqxczYKNHQAtWrTIHR9iB7V2AGrlWX3NgpaVC9ljtYMjO9i0tgZOerR9TwZ7HXn92rID7Le85S1uSWtgrE3bgyYLJ6EITK7U2QQxdoBsz1fgyyYustJHO1C0NgT2Y+fz3Np17CSWjYGyMjELZ1aS135cz969e90yaNt/2oF0IMQEvmwssgk8F3Z9K+mz11r7g1l7jVt5Zk+8h21/Y+HG9vH2vLRlpcbtZ+kMlAF29z1spWSB4GxlZ/YYrLTRSujbH9wvXrxY5yNY+Xdg0p/2v7O/qQWf9u+TwBhE20/YeDJrp/3tA/uzYO8r+/vYOLhgAiX/gVI7Y2WmgXLlUE8i2bAEK/2zJXzss8lCmIUJGwJgk2J0NOuulSnb68vGf9lnbLBwFS4rTbVQbOHWgqSVPdrrqa/Z37jtSYbufubYSQ07HrES1rbXt5Bt5dxWZhpM4G/o9fELegdj3NAnbN0TOwCwg82pU6eGtS0b7GsfYha+7EDddtKBDzSr/bcxH/YBEmBjOuwAxw5gbMxMoL7dDmrsTHeg987OGtqYJ/uQ/N///d8z48/sPux2PbnQcuDDtu3Z2GBsrJa1zz54bDIT+7IPZRsXZG3q7nTJ5zNFfEe3CVzefrKD3mZ/d/tbB3vurE32NwsWzIMNog+Mc7AekHDbFLj/YAIHa8Hadb7T9tvf3iYTsK9Ar7adcLAz4XZwFsoA+J5k49JsVlg7A23jauwxB3p37OC47Xuyq8fv1WvLxgDZOCQLa7afsPepTRhhgTzY+MRgAgeiFnJCFZhcx3q37MDOehztzHxgrF13nlvrsbQQaD1mNjYq0CNut7Ue2cAELGVlZe6/O3fudL86Yie92v4tOhoX253XcSjvFxs/Zdez3r/O3sOB93F338OBJS9Ccb7v0c72OR39rv2EI29729vcv6O9/gJjAa1nzg7k7fMh2PvKrtPRuCzr/bfXs33W2UkG+2wMnJwIZQxn+/ZagLMv09LS4vaU2skke36t7e3HawYmsrHP7a7GmZ0v6/2z94AFRHv9B2Z8tMduVQqBCTw6Ys+JPccWfOx93FmvW6CKItgsvD3xmWP7JOvttaWSbP9uJ+VsHK31nHY0E3Tg5G48LlMRD+hxQ58InI3tiQGzFrbsTLF9QNjZPptwwHbGtpMONoWwfYBZSY6VJlhpn53ls7PaVq5j68+03YHa4HALfrZ9O2Czs+wWBm0Af7i9MgF25s929hY8uloM2HbyFjztwMpCr5WlWG+ErRHV0dm2zpzPui4dLeYcuLzth0fg7KJ9gLfXUwfh9mFoZV72t2zPJiuwnoWODvB6S+D+OnquAqXCwdrVU2vt2MkRK6GxA/pt27ad6fnpi7+JPe92osPOENsJGntv2oGbvSe7WqA82HMW7LXVF6wHyPYh9v6yng47YWL7h+4c0AZOKASCUXdYL52d2LLAEpj44XyeW+u1suvZe8R6Uqwn1t4z1gNha061fS1aL0vb3r/2X4FJGgJ/C2tPMN1Z9D2c94sXvFoPyz6zLLTZ5CvWa2q9ZFZiaX/7QIXI+bTXXjcW+AJrh9prwkpdg/WAdod9Xtlnpk3AYoLN8myfv3Yy0tofKMPtDValYj1V9j602SHtRIa9riz8WllyV4/Delltn9nVQuKBY5quJnQ5X9Ze2y8EwnUoITuw7+nqxDCiE8ENfcLKIqwUxKbVD3bA3VZHZ+YDrDzDtJ050thBRlc7ZDuTZrMZ2lgNmwHKPgytB689O8trYc3aG5i+v6fKz2677Tb33+4ukGnj4mymKSthsimIbWxA4MxaYAxEuD1HHY3faj9mMLCWj5k/f/6ZywJjFSxktmUHjcF6gM6n3YH7CzYFeOCynuwdDUXg/uxscvuSNPs5cJa5t9tloa39OKxAyV77v0l3SxE7+1vZyQh7nHbmvf1Z3sDrpCPBfh/stdUT7DF09Vqz3krbR1mPhJUn2mu6OzNEBkrUrLTwfNhJo8B7Jtzn1kK7veZsVrpAYAtM82/LA1gwsiUygk0r3571+FjPg12/fW+itdVmXuyJ97Cd1LJ9vJVLtu1ti0eBzzqrDmk/zq2rv31n7MSlHdRbCLDZPe0ETqilwKEItuh8gJXd29/deoDtMzDYrJg9yfaHVm5oPdO//OUv3feSLZzdlcDyKXaSpKPlA+x9YBU6bcdL9zTrwbeqCvv8tCBsxy723u2sNNn2Pfa4vV4LD72D4IY+YUHDDh6s9MDWSbOpqTvaCXa1dktgvJWNRWnLxnTY9Ovt2YdE+x2vHagEzkoFyiDs4DrYQV3gDHNXg5S7YlMz26B+OxNuZRUdjQFoG2CDHQxZ6ZJty3bMgd6UQE17sMkvwmXPibW17XNoPTrWu2Mf/ja2of1YkPYTC9jfNdjf/HzaHVhLzT6I25Ye2sFHYA3AwHX6ivVwWEmt9YzawX5bFv5t6mc7UdB2fbXzZSWybcdgtWXldPbasA/swPgnOzi3Xhx7v7Q9SWHjh2xK8VB19rcKvCft9RoIHIHB8V3dh53IaNvzZ99bSXDbdfN6ij2GQPlTR+w+7b3++c9/3g1NNllDd9771iNu78vzOdFjvRH2PrGwGJjavrvPrb0GO+vFDDwW61WwkjY7KWO9+sHCm+1PA/s/OylgJdr2c/tlGiwAdGdqeDvpZj141pvXtkzT9jFf+cpX3J6OjtYdjCcdfdbZc2aB4nzZRDf2/NqJS9u322dJd55vO3loFSvBevFtH2Nh0NjSDR3tL+1z2R6f9bxZz1hPsjF7wcrS278HOmPPh5UvW4+b9VC232fYfsqCp02GZqWX7ZcC6EmBtdpscW3bb3fW22aVAnZCzsaiUioZmxjjhj5jB2O287NSRzuQtB2dHZzYh4YdrFgPkpV3tV+Yuj3bidoHvp0FtwMJO0C1evY33njDPTNps8C1ZT1sdvBqZ93sg8IOUKzE0j60rAY/8OFo40hsTIt92FgJox042gemDVa323b0IRSMHcjYh1tg4gHbudsHgH1vM8fZOJpgNfFtWW+anVWzM902EN0+7Oyg3M4WWsmHHWwFxrnYgbr1yNl27TIbJ2Ptt9nSwi03sxItex4slNlZ/8A6bvahbaGk7eByq8W3iTMsfNtEHDZ5jJ2htwNAG1fRvuzEykvs9lbuaj0NgdIOG+PTEZt8wR6XTeBgr59AqZedubWDWfs7BiYD6Us2htJeI/ahauVNNkmIHWBZD4c9Lvt9TwisxWU9O3ZgYeNZrIw38B6w57P9fdkJA+tJsufbDjIsAFgpcHcmXejsNRaY0dX+BnbAYJMT2EGSvVbt+0DPQTD2+g78HU3g72gTRdi2epLtc+z1aCePLGDZAay9Vtq+Xizc2XNkz/P5jPux0GWvdXvPdDQ+xt47bU9Q2ckYe63YPiMwyVJgFsnuPre2b7NJmAL7Dts/WgC116G1JTAbobETHfaasZ4I22/a82CvJ+udtXE0dpbfyswC49rs5JiVhtn70x6f9ZzZSQkbR2RrxgXWoeuK7Y+t7M8qCOw1bAfA9h6xzwA76LYxzO1n44tH9jzYl43RslkL7XPIKiDsb2mfdeEEHgsDgXGc9voKZU3PADtxZCc2bJytvWZsP2/7YAtt9lqw8GAnBexv2xE7iWXhzU542evAbt/V2LNQ2Xv397///Zm22evNPu+tbfb+DmWGUDsusXBqY0rtc87eb3aSMvD+sJ/teMWGWwTKiXuLfZbY/sp6WW3fGxjXHIxdx076nu/6qogCXq9HgPhjazPZGjSTJk1yMjIy3PVIxo0b564H88wzz4S0jpv9bOse5eTkuGviXHPNNc6mTZuCXv+3v/2tc/3117vr26SnpzsDBw50lixZ4q4j03ZdJlvj6+1vf7szceJEJzMz011naO7cue76Trb+USgCa9UEvmy9JmvfjBkznHe9613uejcdrQXVfh0Xu57d9xVXXOGuv2ZrKg0dOtRZuXKl849//OOcdXg2bNjgbsOek8D9B9bMCbYuV6jruNk2bd0eW1/J1gCy53DZsmXO008/HXRbtg7epZde6j6HttbPm9/8Zmf//v0dtuGxxx5zFi9e7L4WAu1u/3wGa7etq2S3s/uxL/s+2FpLna0/1tUaZ6E+TwG2ltEHPvABd90vWwPI/rWfg61x1Nmad51544033HWm7Pa2HqC9Luy5mzZtmrvuj60r1NH6YrbGoa1PNGbMGHcNOHuNhbqOW1evMXuPfOELX3Dfy/aetvu67bbbOryPwOO3dZK+/OUvn3kstoabrcPU/vXdE+u4WRs/8pGPuH8Xe2929Lqw9SbtdxdccIFzPgJrctm/7QXW2Wr7FXitvPWtb3Vefvnlc27Tned2165dzn/8x3848+fPd/d1dn1bY8r+pjt37jxn27ZGmK3DZvtTe7/a9e31cdVVV7n7yLZrjQXWFbN9ge3X7H23YsUKd23FjvbVnbH1Fq+++mp3W/a3t3XPbE259vdpgr2GQlmfL9R13ILpau3CjtrV2X6rs/d9sMdh68DZ56Wt52n73tmzZ7vvZVujK9i+qzvPha0lZ9t48sknQ7p+2zbZOoVve9vb3Per7Q9sv2Kv4Te96U3Ovffee85tOnpObI1Cez3beyDwfgn3vW77KVvXb9asWe5ry/aPdh+f/vSng65j2tWabrZWoK3xZ+8ne5y2puO1117rfp731NqZXX0eBNYrfMc73tFpe9///ve776W26wcitiTY/3kdHgEAiBTWE2E9Pjbt9vmMXbFefasqsLP91gMGRBrrDbZecxuPZj2y7aetR2SxGSVtkiLr8e6oLNOqVqyCyCqJ2pfsI3bwTgUAoM0BrY0VtJLHzmbu66rMysYgWelfdybtAPqKlfdZqZ+VTBLaIpsNT7A1O+1kUGCt0mBsLLmNSQ9MgIbYxBg3AEDcszFbNgbzqaeecifssOAVzuB+G7dl45ECyzIAkcDGKVoQsDFgNl4rsK4fIo+NO7UxqDaW0ca327jYzmaitvF7tsTDyJEj+7Sd6FuUSgIA4p4dFNlkHTbhgs0kaZPs9NYCwYBX7MDfeoTnzp3rToJjE54gMtnMltbTZpNCWalkd2YBRuwiuAEAAABAhKOwGQAAAAAiHMENAAAAACIcwQ0AAAAAIhzBDQAAAAAiHMENAAAAACIcwQ0AAAAAIhzBDQAAAAAiHMENAAAAACIcwQ0AAAAAIhzBDQAAAAAiHMENAACgj3z729/WvHnz+mQ773//+3XDDTeEfV8AIgPBDQAARB0LJQkJCfrhD3941uUPPvige3l3jBs3Tj//+c+7vN7WrVt1/fXXa8iQIUpPT3dvd/PNN6uoqCjk+/riF7+o55577qzHQbgCEAqCGwAAiEoWnn70ox+pvLy81++ruLhYl156qQYMGKCnnnpKu3fv1u23364RI0aotrY25O1kZ2dr4MCBvdpWALGJ4AYAAKLSZZddpmHDhukHP/hBp9e77777NHPmTKWlpbm9ZP/zP/9z5nerV6/W0aNH9fnPf97tqeuot+7ll19WZWWl/vjHP2r+/PkaP368Lr74Yv3sZz9zvzd33HGH+vXr12kPYNsSR/v+L3/5ix566KEz971mzRr3d1/5ylc0ZcoUZWZmasKECfrGN76h5ubmc9r1+9//XqNHj3av9/a3v91tY0f8fr/7XFl7MzIyNHfuXN17772dPncAIgfBDQAARKWkpCR9//vf169+9SudOHEi6HU2bdrkBpp3vOMd2r59uxuWLARZyDL333+/Ro0ape9+97sqKChwv4KxgNjS0qIHHnhAjuP0SPutbNLadtVVV5257wsvvND9XU5OjtvGXbt26Re/+IX+8Ic/uCGxrQMHDuhf//qXHnnkET355JPavHmzPvnJT3Z4fxba/vrXv+p///d/tXPnTjesvvvd79batWt75PEA6F3Jvbx9AACAXnPjjTe6PVjf+ta39Kc//emc3//0pz91SxwtrBnrxbIw9OMf/9gdX2aljxYALShZOOvIBRdcoK997Wu65ZZb9PGPf1xLlizRJZdcove+970aOnToebXdyiat56uxsfGc+7711lvPfG+9hBby7r77bn35y18+c3lDQ4MbxEaOHOn+bAH22muvdXsU22/P7sNC7rPPPqtly5a5l1lP3rp169xeu1WrVp3XYwDQd+hxAwAAUc3GuVnJoY07a88uW758+VmX2c/79++Xz+fr1v1873vf06lTp9weKyu9tH+nTZvm9uT1tH/+859uOy2AWcCzIHfs2LGzrjNmzJgzoc1YILNyyL17956zPeudq6ur0+WXX+5uL/Blwe/gwYM93n4APY/gBgAAotrKlSt15ZVX6qtf/Wqv35dNLHLTTTfpJz/5iRsKbXIS+94kJiaeU0YZbFxaV9avX693vetduuaaa/Too4+6JZBf//rX1dTUdN7trqmpcf997LHHtGXLljNf1vvIODcgOlAqCQAAop4tC2Alk1OnTj3r8unTp7sTi7RlP1vJpJVImtTU1G73vgVuN3HixDOzSg4ePFjV1dXuz1lZWe5lFo662kb7+37llVc0duxYN6wF2AQq7VkPXH5+vhsezYYNG9zw2P45MDNmzHAnZ7HbUBYJRCeCGwAAiHqzZ892e6l++ctfnnX5F77wBS1evFi33Xabu+aa9Wb9+te/1m9/+9uzxpC9+OKL7gQmFm4GDRp0zvat58vGmNl1LPRZz5pNCvL444+7ywKYpUuXurM72li4z372s3r11VfPTILSEbtvW17AyhutNy8vL0+TJ092A5bdn7XdeslsUpRgyyG8733vc3v8qqqq3Pu0yU6CjdWzMXw2Ts4mJLFyyosuusidgdJCbG5urrsdAJGNUkkAABATbGZICyVtLViwwJ150ULQrFmz9M1vftO9nk1M0vZ2R44ccXvPrNcsGOuxslBmQdB69myyEtuuLQ/wnve8x72OTXRy5513umHOguRdd93lzmLZmY985CNuD9miRYvc+7YgZYt8W8D69Kc/7d6X9cAFJldpa9KkSXrLW97illReccUVmjNnzlmBtD0Lr7Ydm13SeiJtNksLhYHlDABEtgSnp+a0BQAAAAD0CnrcAAAAACDCEdwAAAAAIMIR3AAAAAAgwhHcAAAAACDCEdwAAAAAIMIR3AAAAAAgwhHcAAAAACDCEdwAAAAAIMIR3AAAAAAgwiV73QAAAGLJy3pZO7VTCUpwf044/V+SkpR8+r+237f/Oe30f+mn/wt8H/g3kXOuABCXCG4AAPQCR85Z//rkU5Oawt5uilLcAJehDGUrW1mn/2v7faYyCXgAEGMIbgAARJHm0/9Vq1pFKgp6Hevhs/BmYS5HOcpr91+qUvu83QCA8BDcAACIMdbLV3v6v0IVnvN7663rp35nhbn+6q9c5Z4p8QQARBaCGwAAcab+9H8FKjinDHOABmhgm//sZxt7BwDwFntiAADgshJM66Fr20tnPXDWIxcIcoNP/0e5JQD0LYIbAADd4DiO+5WQcHrWyNP/xnLZZcXp/w7q4JkwZz1xQzREQ0//Z+EOANB7Ehz79AEAwGNNPqmhRapvlurt3zbfN7S5rKHd7+029tXil/yOBY3T/zr//nnOEOlji3qmnfc/c7/2HNzjfu+OB7P/JSQoOSlZKckpSrsgTYlT429GRxs31zbIWa8cJZYA0HPYowIAel2zTyqrl8oapHL7t/70v21+bvT13v37evAU5ZETR1RWWaa87Dy35y3QK9XgNMjv9yuvLs+dyTHe2Ji5o6f/M7Y23TAN0wiN0EiNdIMcE58AwPkjuAEAwlbbJJ2qlcrq2oWzhtZ/a8JfviyiZGVkaVD/QUF/l5BJOAmsW3fy9H8btdFdQHy4hrshzv6zWS0BAKEjuAEAulXOWFAt5VdLJ09/5VdJFY1etwyRrlGNOnL6P2MLhVuAG336PyY7AYDOEdwAAOewsWGFNe0CWrVUXNs6ZgwIl60xt+/0f1ZCaWWVYzVWYzSG3jgACILgBgBxzueXjlZKB8ulY5WtAe1UTetkH0BfsDGCBaf/26AN7gyV407/ZxOeMDYOAAhuABB36pqlg2WtQe1AmXSkQmompCGCVKpSW0//Z7NVWk/cBE1wJzpJVPzN2AkAhuAGADGupK41oAWCmo1Ro9wR0TRb5Z7T/1mIswA3URPdJQfoiQMQTwhuABBjY9NOVLUGNDeslTFxCGIrxO08/V+2st0QN0mTNEjBZ/gEgFhCcAOAKFfZIO0okrYXSbtLWhejBmJdjWq07fR/NibOeuEsxDGxCYBYRXADgChjaz7bZCLbC6VtRdLxSkofEd9sTNwbp/+zEsqpmuoGuRSleN00AOgxBDcAiAL1zdKu4tZetZ3FUhXlj0BQhaf/W6/1binlNE1zwxwARDuCGwBEKJuS33rVLKzZeDUf3WpAyJrVrL2n/+uv/m4v3GRNdic4AYBoRHADgAhiMz++nt8a2IrrvG4NEBvKVe6uD/eaXnMX+J6hGRqlUV43CwC6heAGABHQs/bqSem1k61T9wPoHX75deT0f9YLN1MzNUVTlMzhEIAowJ4KADyaCXJjfmtgO1bpdWuA+OyFW6d12qiNbhmlhbgc5XjdLADoEMENAPqITdO/uaA1rO0tbV1zDYC3GtXoLimwXdvdMsrZmq0RGuF1swDgHAQ3AOhFPr+0o7i1DHLrKanZ73WLAATjyNHR0/8N0ADN0iy3jDJRiV43DQBcBDcA6KVJRjackDblS7XNXrcGQHeUqUwv6kVt0ibN0RxN13TGwQHwHHshAOghTT7p1RPSmqPSiSqvWwMgXLWqddeD26zNbg+cjYNLU5rXzQIQpwhuABCm4trWsPbKcamO3jUg5jSoQa/rdW3VVrf3zXrhMpXpdbMAxBmCGwCcB8eRdhZLLxyRdhbZ+BgA8bCot01kslM73fFvczVXucr1ulkA4gTBDQC6ob5Zevm4tPaoVFTrdWsAeMEnn3Zrt/Zqr7uUwAItUJayvG4WgBhHcAOAEJysktYcaZ3Kv9HndWsARMqC3hbg9mmfZmiG5mmeMpThdbMAxCiCGwB0wNZZs3XXLLDtK/O6NQAiuQfO1oHboz3uJCZWQpmqVK+bBSDGENwAoJ1mn7T+hPTUQamkzuvWAIimMXA2A+Uu7XInMLEQl6IUr5sFIEYQ3ACgzXT+Lx6VnjkoVTR63RoA0apRjdqojdqhHZqv+W4ZJQt5AwgXwQ1A3LMJR54/Ij1/WKpp8ro1AGJFver1il5xZ6G8QBdorMZ63SQAUYzgBiBu1TZJzxxqndK/ocXr1gCIVZWq1FN6SiM1Usu0TAM0wOsmAYhCBDcAcRnYnj3c2sNGYAPQV07qpO7TfZqmaVqkRcxACaBbCG4A4kZds/TsodbAVk9gA+ABR467hMABHXDHv83WbCUpyetmAYgCBDcAcTGGzXrYnjtEYAMQOTNQvqbX3BBn5ZPjNM7rJgGIcAQ3ADHL52+dJfKRfVJts9etAYBzVataT+tpjdEYLddy5SjH6yYBiFAENwAxaXuhdN9uqaDG65YAQNeO6Zjyla8FWuCuAcfyAQDaI7gBiCknq6R7dkm7S7xuCQB0T4ta3PLJ/dqvFVqhYRrmdZMARBCCG4CYUNUoPbxXevm45He8bg0AnL9ylethPaypmqqlWqp0pXvdJAARgOAGIKo1+6TnDktPHGBqfwCxZa/26qiOuuHNQhyA+EZwAxC1NuZLD+yWSuu9bgkA9I4GNWit1rrLB6zUSiYvAeIYwQ1A1Dlc3jqO7WC51y0BgL5bvPte3aslWqIZmqEEJXjdJAB9jOAGIGqU10v3727taWMYG4B4XPvtZb2sQzqkS1pWKSs51+smAehDBDcAEc9xpBePtYY2xrEBiHc1/iql3vOgNHeRNGOG180B0EcIbgAi2qka6W/bpANlXrcEACKAI618NV0p1aXSunXS0aPSypVSVpbXLQPQywhuACKSzy89dVB6bL/U4ve6NQAQGWaUD9PI7af+fcHx49K990rLl0uTJnnZNAC9jOAGIOIcqZD+tlU6Ue11SwAgcuT4s7T00eJzf9HYKD3/vHTkiHTRRVI6674BsYjgBiBiNPmkh/ZKzx9mEW0AOIsjrdqQppSG2o6vc+iQVFAgrVoljRnTl60D0AcIbgAiwu5i6c7tUkmd1y0BgMgzs2yYRuxoUyLZkfp66cknpTlzpCVLpMTEvmgegD5AcAPgqdqm1jXZ1p/wuiUAEJly/dnBSyQ7s22bVFwsXXqplJnZW00D0Ic4DQPAM6/nS99eS2gDgE5LJF9JVXKjr/u3tbLJ++6T8vN7o2UA+hg9bgA86WWzKf43h1D1AwDxbNCWFg3fFcZ6KFY6+dhj0uLF0ty5UkJCTzYPQB8iuAHoU/tKpT9vlsobvG4JAES2pApHV7/eA8VRjiO99pp06pR08cVSWlpPNA9AH6NUEkCfsFkiH94r/XQ9oQ0AuuR3tHyNlOH04KHasWPS/fe3jn0DEHUIbgB6XVm99JNXWhfTZpZ/AOja0O3StKJeKGusrpYefljavbvntw2gV1EqCaBXbcpvnea/rtnrlgBAdEgqd3Tlxl68A59Peuml1tLJFSukZA4HgWjAOxVAry2m/c8d0rrjXrcEAKKI39GKNVK6vw8mEdm/XyopkS6/XOrXr/fvD0BYKJUE0ONOVEnff4nQBgDdNWybNKW4D2d+LC+XHnhAOnSo7+4TwHmhxw1Aj3r+sHTfbkctfXG2GABiSHKZo6t6s0SyI83N0rPPSrNmSRdcICVyXh+IRAQ3AD2ipkn661Zpa6H9RGgDgG7xO7pojZTqeLj/3LFDqqqSLruMcW9ABOKUCoCwHSiTvrvWOR3aAADdNXyLNKUkAk562ZIBjzwi1dV53RIA7RDcAITlpaO2NpujysYIOOAAgCgtkbxykyKHrfP20ENSRYXXLQHQBsENwHnx+aW7trdO9e/zsrQHAKKZz9HKFzwukexovTcLbwUFXrcEwGkENwDnNZ7t5xscrTnqdUsAILqN2CJNKo2w0BbQ2Cg9/rh04IDXLQHA5CQAuutklfTr1/wqa+C8DwCEI7nU0RVvKLLZYt3PPy/V1Ejz5nndGiCuEdwAhGxzgaM/veGo2SG0AUBYfI5WeT2LZHe89lpr+eTy5SwXAHiE4AagS44jPbTHpycO2oc1H9gAEK6Rm6WJkVoi2ZHdu6XaWunSS6WUFK9bA8QdjsAAdKqxRfrV+kY9cTCJ9dkAoAeklDi6YrOiE8sFAJ4huAHoUGmddNsLTdpZluZ1UwAgNvgcrX5BSomWEslgSkpaZ5wsL/e6JUBcIbgBCGpviV/feaFZxY2pXjcFAGLGqDccjS+P4tAWYOPdHn5Yys/3uiVA3CC4ATjHmkMt+tkGqdFhDAMA9JSUYiuRjIHQ1n65gKOsDQP0BYIbgLPcv6NBd+1KlsPuAQB6tkRyjc0KF0PBzfj90jPPSEeOeN0SIOZxZAbgzMyRf9xQo6eOpHvdFACIOaM3xUiJZEfh7dlnCW9ALyO4AVCLz9H/rKnUxpJsr5sCADEnpcjR5VtiNLS1D2+HD3vdEiBmEdyAOFfX6NN3nyrX/to8r5sCALHH5+iSWCyR7Cy8HTrkdUuAmMQC3EAcK66o03+/2KCqxAFeNwUAYtLY1x2NrUiMr7r7555r/X7CBK9bA8QUghsQp/afKNOvNiaqMYXQBgC9IbXQ0aVb46CnraPwZv9OnOh1a4CYQXAD4tCGXSf017258qXket0UAIhNLY4ujZcSyWAstD3/fOu/kyZ53RogJhDcgDjz1KbDeuDYEDkpWV43BQBi1rjXHY2ujKMSyWAstL3wQuv3hDcgbAQ3IE44jqP7Xzmop0tGSylpXjcHAGJW6ilHl2yL0562jsKb/Tt5stetAaIawQ2IA36/X3994YDW146XklK8bg4AxK54L5EMxkLbmjWt/06Z4nVrgKhFcANiXEuLT398Zq82t0yTEuO8bAcAetn4jY5GV7GvPYeFtrVrW78nvAHnheAGxLDGpmb935M7tUOzCW0A0MvSCk6XSNLZ1nl4S0igbBI4DxzJATGqrr5Bv314s3ZolpSY5HVzACC2NTu6bI2UZKEEXYe3Eye8bgkQdQhuQAyqrK7Rrx/YqN2pC6REOtYBoLdN2OhoZDWhLSR+v/TMM1JJidctAaIKwQ2IMSVlFfrNA6/pQNYFSiC0AUCflEhevJ3Q1i3NzdITT0jV1V63BIgaBDcghuQXFus392/Q0byLlMDskQDQ+5odXU6J5Pmpr28Nbw0NXrcEiAoENyBGHD6Wr989sF75gy6WklK9bg4AxIWJrzoaQYnk+auokJ56yqZA9rolQMQjuAExYP+hY/q/h9araNgVUjKLawNAX0g/6ejinYS2sBUWSs8/3zpxCYAOEdyAKHfgyAn9+dFXVTbqGik53evmAED8lEiulRIpkewZR45Ir7zidSuAiEZwA6K8PPL2R9arbPQ1UkqG180BgLgxeYOj4TWEth61c6e0ZYvXrQAiFsENiFJHTxTozw+vU8nIq6WULK+bAwBxVSK5ahehrVe89pp04IDXrQAiEsENiELH8wt1+0NrVTT8Kik12+vmAED8aHJ0xRpKJHvVmjXSyZNetwKIOAQ3IMqcPFWsv9z3jAoGXSKl5XrdHACIK1M2OBpWS2jr9QW6n35aKi31uiVARCG4AVHkVFGp/nrv4zref7mUNcTr5gBAXMk47mjlbkJbny7QXVPjdUuAiEFwA6JEUUmZ/nrPYzqSPk/qN97r5gBAfGlydOWLlEj2qbq61vDW1OR1S4CIQHADokBxabn+cs9jOuiMk4bN9bo5ABB3pq53NIQSyb5XXi698AJrvAEENyDylZZX6m/3Pa4DtbnSuJVeNwcA4k7GMUer9nLI5JmjR1kmACC4AZGtvLJKd973hPYWO9LUN3ndHACIP42tJZLw2OuvSydOeN0KwFMENyBCVVbX6M57n9DO4xVKnHWTlJDkdZMAIO5MtxLJOkokPWelks8/z2QliGsENyACNTQ06l8PP6vth/KVMu9d8ieled0kAIg7mUcdrdjHoVLEaGiQnnlG8vm8bgngCfZGQITx+Xx68Kk1en3HPqXNf7daUnK8bhIAxJ2ERkdXUSIZeYqLpVde8boVgCcIbkAEcRxHT699VWs3bFb6nJvUlD7Y6yYBQFya/oqjQfWUSEak3bulffu8bgXQ5whuQATZ8MYOPfnCK0qddrUaclirDQC8kHXE0UX7OUSKaC+9JJWWet0KoE+xVwIixM59h/TAEy/IGT5fDQNZqw0AvJDQ4OjKl7xuBbpk49yeflpqbPS6JUCfIbgBEeDYyVP618PPqDZloBpHr/a6OQAQt2a8TIlk1KiuZnFuxBWCG+CxkrIK3fXgUyqsapQz7Uam/QcAj2QfdrT8IIdGUeXYMWnzZq9bAfQJ9k6Ah2rr6nX3Q0/r0NF8pc29Wb6kTK+bBABxWyJ5FSWS0WnTJun4ca9bAfQ6ghvgkabmZt376HPatvuAcue9WQ1pQ71uEoAY0VDdoH9+7p/66tiv6tMZn9aPLvyRjmw8EtJtD7x8QJ9I/oRum3fbWZe/+vdX9Z+j/1Of7/95/ev//eus35UcKdE3pnxD9VX1ilaz1jka0ECJZFQvzm2lk0AMI7gBHvD7/Xrs2Ze1ftN2DZy6TFW5M7xuEoAY8tcP/1W7n9mtD/ztA/rm9m9qxhUz9LPLfqbyk+Wd3q6uok63v/d2Tbt02lmX15TU6G8f/pve+pO36j+e/g+9dudr2vbotjO/v+uTd+nGH96ojNwMRaPsQ46WHeKQKKrZJCXPPmsfsF63BOg17KUAD9ZqW7N+k55b95oGjJyo8sErvG4SgBjSVN+kzfdt1lv/+62asnKKhkwaouu+fZ3779rfre30tn//+N+15JYlmrBswlmXFx8qVkZehhbfvFjjFo/TlIunqGB3gfu71+56TUkpSVrwlgWKRgn1jq5a53Ur0GOLc7/xhtetAHoNwQ3oY5t37NWjz65TVk6eqsdcKScx2esmAYgh/ha//D6/ktPP3rekZKTo4LqDHd7u5dtfVsmhEr3pW28653dDJg9RU12Tjm0+ptqyWh3deFSj5oxSbXmtHv7Gw3rHr9+haDWbEsnYYhOVFBV53QqgV3DECPSh4/mF7lptVirpn3ilmpLzvG4SgBiTnpPu9pg9ftvjGj59uHKH5rq9YofWH3J73YIp3F+oB/7zAX3ppS8pKfncmW2z+mfp/X95v1tG2VzfrAvee4FmXjlTf/3QX7X606tVcrhEv73+t/I1+/Smb79JC9+2UNEg56CjCw5zDjvmxrutWSO95S1SMoe5iC28ooE+Ul1Tp3seeVbFZRUaPOtSFWWM87pJAGLUB//2Qf3lg3/RV0Z+RYlJiRqzYIwWv3Oxjm06ds51rXfuT7f8Sdd95zoNndLxJEnzb5zvfgXsW7tPJ7ad0Dt+9Q7dOulWffiuDyt3WK5+sOQHmrxysnKH5CrSSySvpkQyNlVUSBs3SsuWed0SoEcR3IA+0NLi0wNPvqA9B49q9OQ5OpkXHWejAUSnwRMH64trv6jG2kY1VDUob3ie/u/m/9OgCYOCzkB59PWjOr75uO7+9N3uZY7fccfj2uySNhnJtEvOnqykubFZ//jkP9yAWHSgyC3PnLJqivs7C3+HXz2sudfNVSSb+5LUr5ESyZi1fbs0dqw0YoTXLQF6DMEN6AM2GcmGTds1ctRIFQ5eJYdFtgH0gbSsNPfLxqLtemqX3vLfbznnOum56e7Mk22t/e1a7Xl+jz5278c0aPy5Ye/x/3pcM6+a6fbk2bg3X4vvzO+sXNLxOYpkuQccLTlCaIt5a9dKb3ublJLidUuAHkFwA3rZjr0H9cTzrygvN1tVwy9Wc3KO100CEON2PrXT7TEbNnWY2yN235fu07Bpw7T8A8vd3z/w1QdUcbJCH/jrB5SYmKiRs0aedfucITlKSU8553KTvytfr//zdd26+Vb3Z9tuQmKC1v1pnfKG5enUnlMau3isIlVCnaOrXva6FegTtq7b+vXSypVetwToEQQ3oBcVFpfqvsefV3NLi7InLlNZxhivmwQgDtRX1reGsxMVyhyQqQVvXaAbvneDO22/qSyoVNmxsm5v18LgnR+9Uzf99Ca3J8+kZqTq/Xe8X3d96i61NLbonb9+p/qP7K9INXcdJZJxZc8eadw4aQyfv4h+CY7thQH0uIaGRv3p7oe1bfd+jZ88XUeHvEn+RMo1AC/MHSp9cnHPbOunt/9UldWVGjZoWNDfJyxMUOJUZiqMRHn7Hd38AqEt7mRmSjfdJKW1nmwAohWfLEAvsPMhT7yw3g1t40aP0Kn+FxLaAMBDCbWOrqZEMj7V1Ukv88dH9CO4Ab3gje17tOaV1zV00ADVDZij+rSOp9gGAPS++S9JuU30tsWtAwekQ4e8bgUQFoIb0MPyC4v10FMvKiExUZmDRqk4Z47XTQKAuJa3z9GiY4S2uLduXWvvGxClCG5AD6pvaNS9jz6nopIyjRoxVAV5y+QkMAcQAHglkRJJBDQ0SC+95HUrgPNGcAN6cFzbY8+t0859hzRuzAiV585WQ+pAr5sFAHFtwYtSbjO9bTjt6FFp716vWwGcF4Ib0IPj2l7csFnDBg+UkzlEJdmzvG4SAMS1fnsdLThOaEM7trZbfb3XrQC6jeAG9ICSsgo9+uw6JSYmKDcvT/n9lkkJvL0AwCuJNY6ufsXrViAiNTVJr73mdSuAbuPIEgiTz+dzQ9vJU8UaPWKoSnLmqimln9fNAoC4tvBFKYcSSXTEyiWLirxuBdAtBDcgTK9u3qmNW3a6oa0hfajKsqZ63SQAiGv99ziaf4LQhi7Y2m6O43UrgJAR3IAwnCoq1RMvvKL0tFRlZue4s0hSIgkAHpdIrve6FYgKxcVMVIKowhEmcJ6aW1r08NNrVVRSrhHDBqsoZ76ak7O9bhYAxLVFa6VsSiQRKhvr1tjodSuAkBDcgPP08sat2rJzn8aMtBLJYarImux1kwAgrg3Y7WjeSUIburm22+uve90KICQEN+A8HM8v1FMvrFd2VqYyMjJ0KneR100CgLiWWO3oKkokcT527ZLKyrxuBdAlghvQTY1NzXr46RdVXlWtYUMGupORMIskAHhriZVIttDbhvNgE5TYRCVAhCO4Ad20dv0mbd99QGNHDVdLUqZKs2d73SQAiGsDdzqak09oQxgKCqQDB7xuBdApghvQDYeOndSzL72m/v1y3Jkki3IXyJ+Y4nWzACBuJVY5unqD161ATHj1Vam52etWAB0iuAEhqm9o1ENPrVVNbZ0GD+yv2tShqs4Y63WzACB+OY6WrpUyffS2oQfU1kpvvOF1K4AOEdyAED330mvac+CIWyKphCQV5jEhCQB4adAuaXYBoQ09aPt2qaLC61YAQRHcgBDsP3RML6zfpEED+ik1NUVlWdPUlJzndbMAIG4lVTm6ihJJ9DS/X1rP9KSITAQ3oAtNzc164oVX3FJJC27NiZkqyZ7pdbMAIH45ji5YQ4kkesnx49KRI163AjgHwQ3owqtv7NDu/Uc0esRQ92ebkMRhQhIA8MzgHdLMU4Q29KING1p734AIQnADOlFaXqlnXnxNWZnp7iySNanDVZ0xxutmAUDcSqp0dPVrXrcCMa+qStqzx+tWAGchuAEdcBzHnfr/VHGphg8dJL8SVZi30OtmAUD8chwtWyOlUyKJvrB5s9TS4nUrgDMIbkAH9h06pg1vbNfwIQOVmJiosuzpak7O9bpZABC3hmyXZhQS2tCHywPs2uV1K4AzCG5AEI1NzXryhfVqampWv7wcd0KSUiYkAQDPJFU4uooSSfS1LVukpiavWwG4CG5AENbTZmu2jR7ZOiFJSc5sOQnJXjcLAOKT39GFViLpp7cNfayhoXVtNyACENyAdkrKKtyxbVlZ6UpLTVVjUq4qM8Z73SwAiFtDt0vTiwht8Mi2ba0BDvAYwQ1oNyHJMy++qqKSMg0fMsi9rCRnjpTAWwUAvJBU7ujKjV63AnGtubm1ZBLwGEejQBtWHvnq5p1uaLMJSRqS+6s6fbTXzQKA+OR3tIISSUSCnTulujqvW4E4R3ADTmtobHInJGlublFebrZ7WXHuPCmBAwYA8MKwbdKUYvbBiAA+X2vJJOAhghtw2vpN27X30NEzE5LUpg5Rbdpwr5sFAHEpuczRFa973QqgDVsaoL7e61YgjhHcAElFpeV67qVXlZOVqbTUFPey4px5XjcLAOKT39FFlEgi0thi3PS6wUMEN0DS2vWb3PA2bMhA9+fqtJFqSG2dnAQA0LeGb5WmlBDaEKFj3ZhhEh4huCHuHc8v1Gubd2rIoAHuhCSO29s21+tmAUDclkheSYkkIhW9bvAQwQ2K9+n/16zfpKqaOg3ol+teVpUxTk0p/bxuGgDEH5+jlS9IqQ69bYhg9LrBIwQ3xLWDR09q8469GjZkgBISEuQoQSXZc7xuFgDEpRFbpUmlhDZEwbpu27d73QrEIYIb4pbf79eaV15XfX2D8nJap/+vyJyk5uTW7wEAfSe51NEVm7xuBdCNXjcLcEAfIrghbu05cFTb9xzU8GGD3d42v5JUkj3L62YBQPzxOVq1hhJJRJGmJmn/fq9bgThDcENc8vl8WrP+dTU3N7tLAJjyrCnyJWV43TQAiDsjN0sTKZFEtNmxwwbLe90KxBGCG+KS9bTt2ndYI4cPcX92lKjyrKleNwsA4k5KiaMrNnvdCuA8VFRIJ0963QrEEYIb4k5Tc7NeeLl1runMjHT338qMcWpJau15AwD0bYlkCiWSiOZeN6CPENwQd7bs2Kf9h49r1JneNqksa7rXzQKAuDPqDUcTyghtiGLHjkmVlV63AnGC4Ia40tDQqBdeeV3JKUlKS0t1L6tNG6GmlDyvmwYAcSWl2NHlWwhtiJEZJoE+QHBDXHl9224dOZ6vkcMGn7mslN42AOhbPkerKZFErNi7t3WWSaCXEdwQN2pq67Rm/SZlZKQrNSXFvaw+ZaDq04Z63TQAiCujNzkaX05oQ4yw9dz27fO6FYgDBDfEjdc279SJ/CINHzLozGWMbQOAvpVSRIkkYrRckqUB0MsIbogLtXX1eum1LcrOzlBycpJ7WVNStqrTR3ndNACIHz5Hl6yRkkVwQ4yxCUqOH/e6FYhxBDfEhS0796mgqERDBw08c1lZ1jQpgbcAAPSVMa87GltBaEOMYmkA9DKOWhEX67a9vHGr0tNSz/S2tSSkqTJzgtdNA4C4kVro6LKthDbEsBMnWhflBnoJwQ0xb8eegzp6okDDhvy7t60ia7KchGRP2wUAcaPF0aWUSCIe0OuGXkRwQ0zz+/165fVtSkhIVFpq67ptfiWpPHOK100DgLgx7nVHoysJbYgD+/ezNAB6DcENMW3foWPu17AhA85cVpk5Xr6kdE/bBQDxVCJ5yTZCG+JoaYA9e7xuBWIUwQ0xy3EcbXhjh5pbWpSVmdF6GUsAAEDfoUQS8Wj3bq9bgBhFcEPMOp5fqO27D2jIoP5nLqtNHa7m5BxP2wUA8WL8RkokEadLAxQVed0KxCCCG2LWa1t2qqauTnk52Wcuq8yc6GmbACBepBVQIok4duCA1y1ADCK4ISYVl5br9a27NbB/PyUktB44tCSmqSZ9pNdNA4DY1+zo0rVS0un9LxB3Dh60GdK8bgViDMENMWnTtt0qq6jSwP65Zy6ryhgvJ6F1HTcAQO+ZsNHRqCpCG+JYfb108qTXrUCMIbgh5lTV1OqV17erX262EhP//RKvyGDBbQDoixLJi7cT2gB3aQCgBxHcEHO27NinopIyDW4zKUldyiA1pfTztF0AEPOaHV2+hhJJwHXkSOvyAEAPIbghpjQ0NunljVuVkZGm5KR/l0UyKQkA9L6JrzoaUU1oA1wtLa3hDeghBDfElF37DrnLAAwbPPDMZb6EZFWlj/G0XQAQ69LzHV28k9AGnIXZJdGDCG6IqQW339i+x51FMjU15czl1elj5ST++2cAQO+USCZSIgmc7cSJ1olKgB5AcEPMOFVUqj0HjmrQwLyzLq+gTBIAetWkDY6G1xDagHM4TuvSAEAPILghZmzfc0DVtbVnLbjdkJynhtRBnrYLAGJZ+klHq3cR2oAOMbskegjBDTGhsalZG7fuUnZmxpkFtw2TkgBAL2pydAUlkkDnioulykqvW4EYQHBDTNh/6JjyT5WctQSAX4mqyhjnabsAIJZN2eBoWC2hDegSvW7oAQQ3xITNO/fK7/crLTX1zGU16aPlS0z3tF0AEKsyTjhauZvQBoSE2SXRAwhuiHolZRXaseegBg44e1KSyswJnrUJAGK+RHItJZJAyKqqpKIir1uBKEdwQ9TbsfegKqqq1T8v58xlvoRU1aYO9bRdABCrpq53NJQSSaB7KJdEmAhuiGotLT5t3LJTGelpSkz898u5On2UlMDLGwB6WsYxR6v2sn8Fuu3oUa9bgCjHnhdR7dCxkzqeX6TBA/89KYmpTh/tWZsAIGY1OrryRa8bAUSpmhqpvNzrViCKEdwQ1bbu2qempmZlZvx7EhJfQorq0oZ52i4AiEXT1jsaUkeJJHDejh3zugWIYgQ3RK3Kqhpt3rFXA/rnnnV5TfpIOQlJnrULAGJR5jFHK/dx2ACE5fhxr1uAKMYeGFFr575DKiuv0oB+Zwc3yiQBoGclNDq6aq3XrQBiwKlTUlOT161AlCK4ISrZmm2vb92llJRkJSX9u3fNn5Ck2rThnrYNAGLNtFccDaqnRBIIm98v5ed73QpEKYIbotLJU8U6cqJAgwf2O+vymjQrk0z2rF0AEGuyjjhasZ/DBaDHMM4N54k9MaLSgcPHVVtXr+yszLMup0wSAHq2RPLKl7xuBRBjGOeG80RwQ9RxHEdbd+1XelqaEhL+Xbrjl5VJjvC0bQAQS2a8TIkk0ONqa6WyMq9bgShEcEPUKSgq0fGCwnMmJbGxbf7EFM/aBQCxJPuwo+UHOEwAegXlkjgP7JERdfYfPq6a2jrlZFMmCQC9IaHB0VWUSAK9h3JJnAeCG6KuTHL77gNKS009q0zSUaK7fhsAIHyzXnY0oIESSaDXFBayLAC6jeCGqFJUUqajJwqClEkOkz8x1bN2AUCsyD7kaNlBDg+AXl8W4ORJr1uBKMOeGVFXJlldU6fcnKyzLq9OH+VZmwAgViTUO7pqndetAOIE49zQTQQ3RFWZ5LY9B9xFt9uWSZra1GGetQsAYsXsdZRIAn3mxAmvW4AoQ3BD1Cgpq9CRY/nq365MsikpWy3J2Z61CwBiQc5BRxcc5rAA6NNlAUpLvW4Fogh7aERVmWRVTa3y2pVJ2vg2AEB4JZJXUyIJ9D1ml0Q3ENwQNXbsOajkpCQlJp79sq2jTBIAwjLnJUf9GimRBPrcqVNetwBRhOCGqFBWUaWDR0+cUybpWHBLG+JZuwAg2uUedLT0CIcDgCeKirxuAaIIe2pEhf2Hj6myukZ5uWePZWtM7i9fYrpn7QKAaJZQxyySgKcaGqTKSq9bgShBcENU2Ln3kJISk5TUrkyS8W0AcP7mrhMlkoDX6HVDiAhuiHg1tXU6cOS4+uWdO3NkXepQT9oEANEud7+jJUcIbYDnCgu9bgGiBMENEe/YyUJVVtUoL+fs4OYoUXWpjG8DgPMqkXzZ61YAcNHjhhAR3BDxjhcUyufzuwtvt1WfOkhO4tmXAQC6Nv8lqV8TvW1ARLC13FpavG4FogDBDRHNcRzt3n9Y6emp5/yulmUAAKDb8vY5WnSU0AZEDMeRiou9bgWiAMENEa28sloFhSXKbVcmaerSGN8GAN2RWOvoakokgchDuSRCQHBDRDueX6iq6lrlZmeedbkvIVn1KQM9axcARGuJZG4zvW1AxGGCEoSA4IaIdvREgVsumZSUdO5skgm8fAEgVP32Olp4jNAGRCR63BACjnwRsfx+vzu+LSvz3AW2KZMEgNAl1ji6+hWvWwGgQ3V1Uk2N161AhCO4IWIVl5a7X7m5545vq08Z7EmbACAaLXxRyqFEEohslEuiCwQ3RPT6bTV19crOzDjrcr8S1ZjSz7N2AUA06b/H0fwThDYg4lEuiS4Q3BCxDh8/qcSEBCUmnv0ybUzpLyfh7DFvAIAOSiTXe90KACEhuKELBDdEpJYWn/YePKrsrLNnkzTMJgkAoVn0opRNiSQQHUpKJJ/P61YgghHcEJEKiopVVl4VdP22hlSCGwB0ZcBuR/MokQSih4W20lKvW4EIRnBDxI5vq29oVGZG2jm/o8cNADqXWO3oKkokgehTVuZ1CxDBCG6ISAePnFBSUqISEs4+W+xLSFFzcq5n7QKAaLBkrZTdQm8bEHUqKrxuASIYwQ0Rp6m5WQeOHFdOTtY5v2ugtw0AOjVwl6M5+YQ2ICoR3NAJghsiTmFxmapr6pQTZGKShpQBnrQJAKJBYpWjqzd43QoA562y0usWIIIR3BBxCotLVd/YqIz0c8e3NaT096RNABDxHEdLX5QyKZEEoldVleT3e90KRCiCGyJOfmGJ7LCj/fg2Q48bAAQ3aJc0mxJJILo5Tmt4A4IguCHiHDp6Umlp5/a2+RKS1Zx07vIAABDvkqocXUWJJBAbGOeGDhDcEFFsbFthSZlysjLO+V2jlUkG6YUDgLjmOLpgjZTpY/8IxASCGzpAcEPEjW+rqa1TVrCJSZIpkwSA9gbvkGaeIrQBMYPghg4Q3BBRThWXqrm5RWmpKef8jolJAOBsSZWOrnzN61YA6FHMLIkOENwQUfILi4NOSmIaU/r1eXsAIGI5jpZRIgnEHnrc0AGCGyKG4zg6crxAmZnpQX/flJTT520CgEg1ZLs0o5DQBsScxkapocHrViACEdwQMapr61RWUaWsjHODW3NihpzEc8snASAeJVU4umqj160A0GvodUMQBDdEjKKSctXW1Ssz89wZJZuT6W0DAJff0YVrpHRKJIHYRXBDEAQ3RIyikjI1t7QoNSX5nN81sX4bALiGbpemFxHagJhGcEMQBDdE1FIACfZfkMlJmuhxAwAllTu6khJJIPYxsySCILghYhw9cUrp6alBf8fEJADint/R8rVSup/eNiDm0eOGIAhuiAh19Q0qKi1TVpDxbYYxbgDi3bBt0jRKJIH4UF3tdQsQgQhuiAil5ZWqr29URnraOb9zKJUEEOeSyx1d8brXrQDQZ/z+1mUBgDYIbogIlVU1amhsUnrauaWSLbYUQMK5E5YAQFzwO7rIZpGkRBKIL/X1XrcAEYbghohQXlnl/puYeO5Lkt42APFs+FZpSjGhDYg7dXVetwARhuCGiFBR2XEtdzMTkwCIU8lljq6kRBKIT/S4oR2CGyLCqZIypQRZv83Q4wYgLvkcrXxBSnXobQPiEsEN7RDc4DnHcVRUXBp0fJshuAGI1xLJSaWENiBuEdzQDsENnquprVd1bT1ruAHAacmljq7c5HUrAHiK4IZ2CG7wXEVV1ekZJYMvBdCcnO1JuwDAEz5Hq9ZQIgnEPYIb2iG4wXMVlbYUQKPSUlPO+Z0vMY2lAADElZGbpYmUSAIguKEdghs8V15ZrQQlBF0KwJeY7kmbAMALKSWOrtjsdSsARASCG9ohuCEiSiVbiyLP1UJwAxBnJZIplEgCMKzjhnYIbvDcqaIypQQpkzT0uAGIF6M2SxPKCG0ATvP5pKYmr1uBCEJwg+dLARSWlHW4FEBL4rkTlgBArEkpdnQ5JZIA2qNcEm0Q3OCp6to61dbVdxjc6HEDEPN8jlZTIgkgGIIb2iC4wVMVldXujJLBlgIwLUkENwCxbfQmR+PLCW0AgiC4oQ2CGzxVWWVLATQFXQogsBwAAMSqlCJHl28htAHoAMENbRDc4Kna+gbZIUuwpQAMpZIAYpbP0SVrpGR3LwgAQRDc0AbBDZ6qr2+QOjloYTkAALFqzOuOxlYQ2gB0oqXF6xYgghDc4Km6BgtuHaPHDUAsSi10dNlWQhuAEJYEAE4juMFTVdW1SkgMfvDiV6L8icHHvgFA1GpxdDElkgBC4fd73QJEEIIbPFVZXaOU5OSgv6O3DUAsGmslkpWENgAhoMcNbRDc4HmPW0pKUtDfsRQAgFgskbx0G6ENQIgIbmiD4AbP+P1+dwFuetwAxIUWR5dSIgmgOyiVRBsEN3imvqFRLc0tnQQ31nADEDvGbXQ0mhJJAN1BjxvaILjBM3X1DWpuaVFySvDg5k8IXkIJANEm7RQlkgDOAz1uaIPgBk973JpbfB32uDkENwCxoLm1RDIpgeAGoJvocUMbBDd42+PmlkoGD2iOCG4Aoo/fd/YZ8gkbHY2qIrQBOA8EN7RBcIOnwc3v+JWUFDygUSoJIBqVlhWf+T6twNHF2wltAM4TpZJoI3iNGtBHpZIJncyu5iRwXgFAdMkvOCH/oNNnyJsdXU6JJIBwENzQBkfG8LTHrTOUSgKIJk1Njdq7Z5fSk1Lcnye+5mhENaENQBgolUQbBDd4GtwcOR3+nh43ANFk246tUkOLhvQbrPR8RxfvILQBCBPBDW1wZAzPVNfUKrmD8W2GWSUBRIv8/BMqLy7S9BGTlJ6QqsvWSomUSAIIF6WSaIMxbvBMY1OLEhI7G+NGcAMQ+RqbGrRn704NSsnR5DGTNHGbNKKJ0AagB9DjhjYIbvCMz+dTYiflkA4dwgCiwPYdW5XY6NPCOQuUmJiok/WOGhMdpdHjhvP0u7Vr3a8jpaXuzzOHD9c33/QmXT1rlspqa/Wthx/W07t361hZmQZnZ+uGefN025vfrLyMjJC2//G//12/f/FF/eymm/S5yy5zL2tsbtaH//Y3PbR1q4bl5uq3t9yiy6ZPP3ObHz/1lHt/v3rnO3vpUSMoghvaILjBM03NzUro5MCG5QAARLoTJ4+rvLhYc4ZPUU5WjntZk6QdjqOFBDecp1H9+umHN96oyUOGuCPB/7J+vd78299q8623ynEc5VdW6idvfatmjBiho6WlbhCzy+792Me63PYDmzdrw6FDGtGv31mX/99LL2nTsWNa/5Wv6IkdO3TLn/6kwh//2P2cPlxSoj+sW6fXv/a1XnzUCMrpeC4AxB+6NOCZ5pYWJXZaKsnLE0Dkamhs0N59OzUoNVeTxkw663f7HEdVHHDhPF03d66umT1bk4cO1ZShQ/W9G25QdlqaG7hmjRyp+z7+cfc6EwcP1iXTprm/f2TbNrV00Ttzsrxcn7n7bv39Qx9SSrsx5rtPndL1c+Zo5ogR+tTq1SqurlZJTY37u0/8/e/60VveotwQe/TQgzqZCwDxhyNjeKalxddFcGNnBSBybd+xRcmNjhZNay2RbMsi2xYmFUAP8Pn9unvjRtU2NWnZhAlBr1NZX6/c9PROJ/zy+/16z+2360tXXOGGs/bmjhqldQcOqL6pSU/t2qXheXkalJ2tv7/6qtJTUnTj/Pk9+rgQonb7FsQ3SiXhaY9bQqdj3AhuACLTqcICVZSUaO6IqcrOzA56nROSCh1HQymZxHnYfvKklv3oR2pobnZ72x74+Mfd0sj2rFfstsce00dXrOh0ez966iklJybqs5dcEvT3H1y+XNtOnNCMb3/bDWz/+uhHVV5Xp28+/LDWfOELuvXBB3X366+7vXx/fu97NbJ//x57rOhEMofq+DdiPDzT0tzS6XTZlEoCiES+Fp+KCvLdEsmJoyd2et03/H75KZnEeZg6dKi23HqrXv3P/9QnVq3S++64Q7vy88+6TlV9va791a80Y/hwffu66zrc1qajR/WL55/XHe9/f4djy6108je33KLD3/++Nn7ta7po0iR94Z573KC3+fhxPbh1q7Z+4xu6YPx4ffaf/+zxx4sO0OPWo1avXq3Pfe5zfbKdcePG6ec//7l6Eq8GeMIGV1stfqelkrw8AfSQuuae21bhyRNKbUkMWiLZXrmkIwQ3nIfU5GRNGjJEC8eO1Q9uvNEtZbTwFVDd0KCrfvlL5aSn64FPfOKcMWttvbR/v4qqqzXmq19V8ic+4X7ZpCZfuPdejetgwpEX9u7VzoICffrii7Vm715dM2uWstLS9PZFi7Rm375eecyIjDFu7z8d8D/+8Y+f87tPfepT7u/sOj3hyJEj7va2bNnS5XUfeOABXXDBBcrLy1NOTo5mzpzZ7RB2//3367bbbuvVcNWb6H+FJ3y+1rPQnZVKJojxIQB6xv4yaV+pNGVg+Cedpgwcq9SSBmVlZIV0m62Oo9GOoxRKJhEG+8xsbGk509N25S9+obSUFD38qU+5Y9A6854LLjhran9z5S9/qfcsXaoPXHjhOde38sxP3XWX/v7BDyopMVE+x5FzeuKTZp/PHXeH2J6cZPTo0br77rv1s5/9TBmnJ6VpaGjQP/7xD40ZM6bP2/Pcc8/p5ptv1ve+9z1df/31btjbtWuXnnnmmW5tZ8CAAYpmdGnAE9bb5vc7nfa4JTisXQKg59yzK/yZte1g4Zrr3qLB/QarurIypNvU24x99LqhG776wAN6cd8+HSkpcce62c/Wy/WuJUvc0HbFL37hTlbyp/e+1/35VGWl+9U2UE375jfdqf/NwOxsdzbKtl/WQ2frtU0dNuyc+7cxc9bDNv/0AfryiRN1/+bN7hi4X7/wgvszYju4LViwwA1v1kMVYN9baJvfbqKaxsZGffazn9WQIUOUnp6uiy66SBs3bjzz+/Lycr3rXe/S4MGD3RA4efJk3X777e7vxo8f7/5r27T9q5UgBvPII49o+fLl+tKXvqSpU6dqypQpuuGGG/Sb3/zmzHWsF9Aua8t65Npus22Jo31/9OhRff7zn3fvO1BGXFpaqne+850aOXKkMjMzNXv2bN11113ntKmlpUWf/vSn3R7AQYMG6Rvf+IZ7cq8jFRUV+vCHP+w+D7m5ubrkkku0detWdQfBDZ4tvm0v7s7GuCU4nNED0HOOVUobbMaQMI0cO1YzFy5UaWGhO1NfKCy41RHeECIra3zvHXdo6re+pUt/9jNtPHJET332s7p8xgy9ceyYXj182A10k269VcO//OUzX8fLys5sY29hoTvbZHftOHlS/9q0Sd9pM2bubQsW6NrZs7Xixz/WtpMn9Yubb+6xx4rIXQ7ggx/84JmAZf785z/rAx/4wDnX+/KXv6z77rtPf/nLX/TGG29o0qRJuvLKK1V2+vVogcZ6x5544gnt3r1bv/vd79ygY1577TX332effVYFBQVnBcW2hg0bpp07d2rHjh099vjsvkaNGqXvfve77n3bV6BnceHChXrsscfc+/voRz+q97znPWfaGmCPNzk52b38F7/4hX7605/qj3/8Y4f3d9NNN6moqMh9HjZt2uSG40svvfTM8xQKSiXhCTsraAc8CYkdvwQTRI8bgJ714F5p4QgpNYxjITsru3jFCh3YscNdfHvg0KFd3sZ3umRyGeWSCIH1pHVk9dSpcn7/+y630dV1jnz/+0Evt964/W3GABkby/nbW25xvxA/we3d7363vvrVr7q9Uubll192yyfXrFlz5jq1tbVuELvjjjt09dVXu5f94Q9/cEsY//SnP7k9ZMeOHXN71BYtWnRmXFmA9T6ZgQMHuuGsI5/5zGf00ksvub1fY8eOdce6XXHFFW5PXlpa2nk9PiubTEpKcsfLtb1v62n74he/eNZ9P/XUU/rXv/6lJUuWnLnceiStlNQ+E6wXcPv27e7PH/nIR865r3Xr1rkBz4JboL0/+clP9OCDD+ree+91w2Eo6HGDJ+hxA+CFigbpqYPhb6f/oEFasHy5aior3XKZUBx2HJXS6wagO7oYv9ibLFRde+21biiznjf7PtBTFnDw4EE1Nze7ZYwBKSkpbsCx3jXziU98wg188+bNc3vnXnnllW63JSsry+0BO3DggG699VZlZ2frC1/4gns/dXV16uljVJvAxEKihTu7LwtuFkDbsvDYdpbWZcuWaf/+/e7t27OSyJqaGjeg2vYCX4cPH3afw1DR4wbPFt+2MW4JnczIlsgYNwC94OmD0ooxUr/08LYzZ+lS7dqyRcUFBRo+enRIt7HlAS738Aw6gCjjYXALlEvaOC7TdjxZd1hPnPXaPf74425PnJUH2uyU1uPUXRMnTnS/bKzY17/+dXes2z//+U+3hNN6htuPMbNQ2V0//vGP3dJHm23SwpuFRhsX19TUpPNloW348OFn9VYG9OvXL+Tt0OMGT1iZpN/xd97jxqySAHpBk096cE/428nMytKSVavU0tSkxoaGkG5TLOk4vW4AQpWa6undX3XVVW5gsQBk49basxCVmprqllEG2HVtcpIZM2ac1Xv3vve9T3feeacbiP7v//7Pvdxua4L1UnVl3Lhx7uQhVq4ZuI/AOLWArpYZsPtvf9/2WN785je7paJz587VhAkTtC/IEhivvvrqWT9v2LDBnXjFyi/bs/Fsp06dcsfE2RjAtl/tezE7Q3CDJ0I5bmFWSQC9xSYpsclKwjVtzhyNnTxZRSdPhnybzX6/O706AER6j5uFECt5tMlFggUS642yUkgby/bkk0+617MxXla++KEPfci9zje/+U099NBDbpmjTTDy6KOPavrp5SlsJkqbadJuW2gT6nQwW++3v/1tt8zSeqysvHDz5s1ub6CFxMsvv9y9js3S+Prrr+uvf/2rW7L4rW99q8vJTCz8vfjiizp58qRKSkrcyyx8Wc+glXTaY//Yxz7mtq09K538f//v/2nv3r3urJO/+tWv9B//8R9B7+eyyy5zSylt1sunn37aXb/Otm+9htbmUBHc4InEpES3t62zaVMJbgB6i+157tkZ/naSU1K09OKL3X9rq6tDuk2NpH0ENwBRENyMTV1vXx354Q9/qLe+9a3uzIvWs2QBzcaE9e/f/0yvlk1yMmfOHK1cudINgDbmzVgP1C9/+Uv9/ve/14gRI9yermBWrVqlQ4cO6b3vfa+mTZvmll9aD5aFIJsYxFiPoM1gaQFv8eLFqrbZWTuZ6MfYjJIWoqznMDBRio2hs8dh27MlA2zikvbLDBjbdn19vTvOzko/LbR1NMmIjYWzUlF7/FbWaSWe73jHO9wS0qEhTHB1ZjtOZ0fOQC8pq6jSD351u7KzMpWbE3wR28KcBSrPntbnbQMQPz6+UJo/PLxt2MfoY3ffre2vvaYxkyefNVi9I3Yodl1iotKZZRJAZ1askNotno74RY8bPJFkPW5BBpG2xXIAAHrb/btteZLwtmFBbenq1crKy1NFaWlIt7Hh8js4bwogCnrcEDkIbvBEUmJrqaRNUNIRlgMA0NuK6qQXjoS/ncHDh2ve0qWqLC0NeZD9fsdRJeENQGcIbmiD4AZPJCclKSExwV0SoCMsBwCgLzy2X6o9/1mez5h/4YUaNHy4SoMMYg/GOT1RCQB06DwXl0ZsIrjBw8lJuiqV5IAGQO+ra5YeOXem527LycvT4hUrVF9bq+YQ1/vJl1RArxuAjmQFnwcA8YnghojtcWNWSQB9Ze1R6ZRN9ximmQsXavSECSrKt0gWGut18xPeAASTmel1CxBBCG7whE1MkpSY1GmPW6LT0qdtAhC/7BzSfbvD305qWpo7UYmxnrdQVEg6RHAD0F56us3m5nUrEEEIbvBMSnJip5OTJPkb+rQ9AOLbtkJpT+v6q2GZOGOGJs2cqaKCgk5PTp11346jZsIbgLYok0Q7BDd4xhZd7KxUMtlHcAPQt+7Z1dr7Fm5FwdJVq5SRmanqCutP65rt7XYR3AC0RZkk2iG4wTPJyZ2XSib5G/u0PQBwokp65Xj42xk+ZoxmLVyo0qIi+UOcOXK346iW8AYggOCGdghu8EyK2+NGqSSAyPLwXqmhJfxFuReuWKH+gwerrLg4pNvY3nArwQ1AAKWSaIfgBs+kpCR3PjmJ/Er0N/dpmwCgslF66mD42+k/cKAWLl+u2qoqtbSElgSPOI5KCG8ADD1uaIfgBm/HuHVxgEKvGwAvPHNQKqsPfztzlizR8NGju7U8wBssyg3A0OOGdghu8Eyq9bh1MQtAMsENgAea/dIDe8Lfjk1QsmTVKvmam9VYH1oStIktjxLeABDc0A7BDZ7JyshQi6/zRbbpcQPglY0npSOhTQrZqalz5mjCtGnd6nXb4jjyUTIJxDdKJdEOwQ2eyc7KkLo4LmFmSQBecU4vDxCupORkt9ctJTVVNVVVId3Glu7eS3AD4ldCgpSR4XUrEGEIbvBMRka6lND5dSiVBOClA2XSptA7yjo0ZtIkTZs7VyWnToW8KPcOx1ED4Q2I3942C29AGwQ3eCYzPb3rHjcfPW4AvHX/Hqm586rukJYHWLJ6tXLy8lRRWhrSbWweym0ENyA+USaJIAhu8ExGRpocOZ2u5UaPGwCvldRJzx8JfzuDhg7V/GXLVFlWJl8X43sDDjqOKghvQPxhYhIEQXCDZzIz0t1FuFtaOj6AYXISAJHgif1SdQ8UAMxbtkxDhg93SyZDYZFtMzNMAvGHHjcEQXCDp6WStgh3cycL0xLcAESC+hbpkX3hbyc7N1eLVq50lwZoagwtCRZIyqfXDYgv9LghCIIbPC2VtB63zoIbpZIAIsVLx6T86vC3M3PBAo2eMEHF3VgewHrd/IQ3IH707+91CxCBCG7wTEb66eDWyah/Ww4gwek42AFAX/E70n09sDyALQuw9OKL3Rnj6mpqQrpN5enxbgDiBMENQRDc4JnUlBS3163F13Ews4lwU1tCO7ABgN62o1jaVRz+dmxB7imzZqm4oCDk5QFshskmwhsQ+5KSpNxcr1uBCERwg6dysjLV3Nx5j1qKrwdqkwCgh9ii3Nb7Fo7ExER3eYDM7GxVlZeHdBsbEbeT4AbEvn79WMMNQRHc4Km83OxOZ5U0qS0ENwCRw8a5rTsW/naGjx6t2YsWqaykpNNlUdra6ziqIbwBsY0ySXSA4AZP5eVkdzo5iSG4AYg0D++VGnpg+O3Ciy7SwMGDVVZUFNL1Ld5tIbgBsY3ghg4kd/QLoK/WcktwR7J1jFLJvvf6P76tN+76zlmX5Y2cqpv/d4/7fV35KW3485d0csszaq6vdn83/+1f14Tlb+1wm5vv+YGOvHK/Kk7uUVJqhoZOu1BL3/8j9Rs19cx11v/x/2nfc3coOT1LS973Q01e/a4zvzu07h7te/6vuuqbj/TKYwa6o7qpdW23G6eHt528AQPc8PbMAw+o38CBSk5J6fI2xxxHxY6jwZRSAbGJ4IYOENzgfXDr4tiDHjdv9B8zU9f+17Nnfk5M/Pfu4oWfvldNtRW68hsPKz13kA6s/Yee+++3K/enr2vQxPlBt1ewY61mXPspDZ68WI6/Ra/99Wt6/JtX6Kbf7lJKepaOvvaIu51rvvu0KvP3a+0vP6jR869Uet4gNdVWauPfvq5rb/t3ewCvPXtYWjFWGhTmOrlWLrlr82YV5edrxNixId3mDb9fVyQmKoHwBsSeAQO8bgEiFKWS8Dy4WdVPZ7OqJfvrWRLAA4lJycrsP+zMlwWogMI9r2jmmz6jIVOWKHfYBC24+ValZvVTyYFNHW7vmu88qamXvV8Dxs7UwPFztfpzd6im+NiZ25Qf363hs1dr8ORFmrTqnUrNzFVV4WH3dxtu/7KmX/0JZQ8Z0wePHAhNi196oLUTOizpmZlasmqV/D6fGurrQ7pNqaSjlEwCsTmjZE6O161AhCK4wVO5OdlKTk7qdGZJlgTwhvV63fm+EbrrwxP0/E/epZqif8/GYGWOh176pxqqy+T4/Trw4t3yNTW4wStU1otm0nJazyxamCs58Loaa8pVfGCTWhrrlTdikk7tXKfSg29o1nWf7YVHCYTn9XzpYGiTQnbKlgYYP22aik6eDPk2NtathfAGxF6ZJD3p6ECCE+oCMkAvKC2v1A9/fYeyszKVm5PV4fVO9F+hmvTRfdq2eHbs9SfU0lDjjl2rKy9wx7vVlp7U2369Q6mZOWqsqdBz/32zTmx+WglJyUpOy9TlX7lHoxZcEdL2Lew9ddv1aqyt0Jv/e91ZY+sOrLnTHQO36F3f1ZhF1+r+zy90e+cK96zXzkd/5ZZmrvjU/7k9d0AkGN9P+sry8I+1jh08qHv//Gdl5eQoJy8vpNvMSUjQrETOwQIxY/Jk6eKLvW4FIhRj3OD5rJIZ6WlqaGzqNLgxzq1vjVl09ZnvB46foyFTluofHxqrQ+v+pWlXfEiv//0bbuiyMXAWpI5seFDP/vfbdf0PX9KAcbO73P66//2Uyo7t0PU/+ndoM4tu+bb7FbDpru9o5NzLlJiUos3//C+97dfbdWzjo1rzs/fqLT/vuCwT6EuHK6SN+dKSkeFtZ/SECZoxf77eePllZefmhjR+bZfjaKLjKIMz9EBsYGISdILTdPCUlUkOHNBPDY22tGzHCG7eSsvup34jpqiq4ICqCg5q56O/1qrP/lkj517qljgufOe3NHjSIu187Dddbmvd/37aDV9v+t4Lyh40qsPrVRzfo/0v3KnF775NBdvXaPjMlcrIG6wJF71dJQffUFMdrwlEjgf3SM2dL0nZJQtqi1esUG6/fiovKQnpNlZkvo3CGSB2ENzQCYIbPDd8yEA1NjZ3eh2WBPBWc32Nqk4dVGb/4WpprHMvS2hXnpWQmCTH6XgRYavKttB2ZP0DetP3nlfusPGdXvel33xMyz78U6VkZMvv98nva32N+Fta/3X8YR4lAz2otL51lslwDRw6VPMvvFBV5eXy+UJ7jR90HJUT3oDYQHBDJwhu8NzA/v06nVXS0OPWtzb86YvK375W1YVHdGr3K3r6+ze6wWziqneq36hpyh0+yQ1WRftec3vgtj3wPzqx5RmNu+CGM9t49OuXasejvz7z88u/+5Q7fu2SL/5DKRk57lpw9mWTkLS35+k/Kj1vsMYuuc79ediM5Tq57XkV7tmg7Q/9TP1Hz3B7AYFI8uQBqarz4oGQzLvgAg0ZMULFBQUh38aWBwAQ5ZKTmVESnWKMGzzXLzdbjv3nOB2O6QgsCeAk8JLtCzWlJ/T8T96phqpStzxx6IyLdMNPNrjfm6u//bheveM/9dRt17m9cRbkVn/uLxqz6Joz27Aeuoaqf5d77Xrid+6/j37t7JknV/3H7e4yAQF15YXa/K/v6c3//cqZy2zZgTk3fEFPfvdaZeQN0erP/6VXHz9wPhpapIf2Su+ZE952bHKSxStX6ol//UtNjY1KTUvr8jaFkk46jkYy1g2IXswoiS4wqyQ8d/DoCf38D3dp1PAhSk1N6fB6hwddrcYUSggARK7EBOnWFdLI3PC209zcrHv++EedOHRIoyZMCOk2dpfXJCYqkQM/IDpNmSKtDn1ZHcQfSiXhuX65OUpPS3VnluxMenNZn7UJAM6H35Hu2RX+dlJSUrR09WolJiWptia0dSyrJB3gXCwQvQYN8roFiHAEN0TQkgCdDw4huAGIBrtLpO1Wuxim8VOnaurs2SrJz+9yHHCAzTDZRHgDotOwYV63ABGO4IaIWBJgkLskAD1uAGLDfbslX5jzhSQmJmrxqlXKzMlRZXl5SLexvegOghsQfVJSpIEDvW4FIhzBDRFhWAhLAqQ1l9sc8H3WJgA4XwU10ovHwt/OsFGjNHfJEpUXF8sf4vIA+xxH1YQ3ILoMHcrEJOgSwQ1RsyRAovxKa6nsszYBQDge3SfVd34+KiQLli/XoKFDVVpUFNL17fTWFpYHAKILZZIIAcENEaFfnq1b0rokQGcolwQQLWqapMf2h7+d3P79tWjFCtXV1LizTYbiuC0RQK8bED0IbggBwQ0Rs5ZbcnKymptbOr1eenNpn7UJAML1whGpuDb87cxauFAjx41TcX5+txblZsUfIAokJkpDhnjdCkQBghsiwoB+ue7MknUNDZ1ejx43ANGkxS/dvzv87aRlZGjJ6tXuOLeGurqQbmPTmRwhuAGRzyYlSU72uhWIAgQ3RMxabv375aq2rr7T66U1V0hOaAP0ASASvHFKOtAD55wmz5ypidOnq6gbywNscRy1EN6AyEaZJEJEcENESEhI0PgxI1Rb13mPGxOUAIhG/9ophZufkpKStPTii5WWnq6aytD2g3YqbDfBDYhsBDeEiOCGiDFy6OCQziJTLgkg2hytlF49Gf52bJzbjIUL3RkmQ+112+U4qiO8AZGL4IYQEdwQMYYMGuCeUe5qgpKMJiYoARB9HtwjNfnCr05YvGKF8gYMUFlxcUi3sbvcRnADIlNenpSR4XUrECUIbogYQwcNUHZmRpfj3OhxAxCNyhukZw6Gv50BgwdrwYUXqrqiQr6Wzk90BRxyHJUR3oDIQ28buoHghoiRl5sd2gQlLZVKYIISAFHoqYNSZedDeUMyd+lSDR05UsUFBd1aHgBAhCG4oRsIbogYVgI0YcwI1dV3flSTYBOU2OySABBlGn3Sg3vD305mdraWrFqlpqYmNTU2hnSbIluYm143ILIQ3NANBDdElBFDB8vv7/rAIqO5pE/aAwA9bf1x6XgPTI47bd48jZs8WYUnQ5/1ZIvfLx/hDYgMNrbNxrgBISK4IeLGuSUnJ6mpubnT62U2FvZZmwCgJ1lsumdX+NtJSUnR0tWrlZycrNrq6pBuY9faT3ADIsPQoV63AFGG4IaIm1kyy52gpPNyycymQslhvAaA6LS3VNp6KvztjJsyRVNnz1bJqVMhLw+w3XHUSHgDvDdqlNctQJQhuCGi5OZkaWD/vC4nKElympXWwjg3ANHrvt2Szx/+2OAlq1crKydHlWWhzbhr9Qw7CG6A98aM8boFiDIEN0QUOwgZP2ak6rrocTOZjTbUHgCiU2GttOZI+NsZMmKEO8tkRUmJ/L7QZtzd5ziqIrwB3hk4UMrO9roViDIEN0ScEUMHyXFHgajrckkAiGKP7Zdqm8LfzoLlyzVo2DCVFIa2X7Q97GaWBwC8M3as1y1AFCK4ITInKElKUlNTFxOUNBUxzg1AVKttbg1v4crJy9OiFStUX1ur5qbQkqDNRXmKXjfAGwQ3nAeCGyLOkMEDlJ2VqZoQxrmlN5f3WbsAoDdYuWRhTfjbmblwoUZPmKCi/PxuLcrtJ7wBfSszUxo0yOtWIApFXHBbs2aNO86poiJyJ5749re/rXnz5sVMO0LZzvvf/37dcMMN6gs5WZluuWR1TW2X181s6oFp2QDAQz6ndaKScKWlp7uLcstxVF9XF9Jt7JP2MMEN6PtJSRISvG4FYj242cG7haqPf/zj5/zuU5/6lPs7u0608DKAbd26Vddff72GDBmi9PR0jRs3TjfffLOKikKfcOOLX/yinnvuOU/CVW+y19GUiWPV0NB1uU9WI8ENQPTbWijtLQl/OxNnzNDEmTPdXrdQlwfY6jhqJrwBfWfcOK9bgHjpcRs9erTuvvtu1df/u4ytoaFB//jHPzQmQqY1bQqxvt8rxcXFuvTSSzVgwAA99dRT2r17t26//XaNGDFCtbVd9zIFZGdna6DNShSDRg0fosSkRLW0tHR6vcymYiX4O78OAEQDW5TbH2Z+SkpK0tJVq5SRkaHqysqQbmNz+O4muAF9IzlZGjHC61YgXoLbggUL3PB2//33n7nMvrfQNn/+/LOu29jYqM9+9rNnepUuuugibdy48azrPP7445oyZYr7IXPxxRfryJFz50Zet26dVqxY4V7H7tu22TbgWG/Vbbfdpve+973Kzc3VRz/6Uffyr3zlK+62MzMzNWHCBH3jG99Qc3PrhBd33HGHvvOd77g9X9bDY192mbEyzQ9/+MMaPHiwu71LLrnEvV4wL774olJSUnTq1Nk9P5/73OfcNgfz8ssvq7KyUn/84x/d52z8+PHuY//Zz37mfh9oX79+/c663YMPPui2M1iPoX3/l7/8RQ899NCZx2Nlp109D239/ve/d59fu97b3/52t40d8fv9+sEPfuC21/4uc+fO1b333queMnrEUOVmZ6mqpvNynwT5WycpAYAod7xKWn8i/O2MGDvWHe9WVljo7qtDYcGtlvAG9L6RI1vDG9BXY9w++MEPuj1EAX/+85/1gQ984JzrffnLX9Z9993nBoo33nhDkyZN0pVXXqmy04uEHj9+XG95y1t03XXXacuWLW5Y+s///M+ztnHw4EFdddVVeutb36pt27bpn//8pxvkPv3pT591vZ/85CdueNi8ebMbTExOTo4bgHbt2qVf/OIX+sMf/uCGI2NliV/4whc0c+ZMFRQUuF92mbnpppvcksUnnnhCmzZtcsOq9ZAF2t3WypUr3TD0t7/97cxlFor+/ve/u89TMMOGDXN7kh544IGQS1lCKZu0sGXPVeDxXHjhhV0+DwEHDhzQv/71Lz3yyCN68skn3efxk5/8ZIf3Z6Htr3/9q/73f/9XO3fu1Oc//3m9+93v1tq1a3vk8eTlZGvksCGqqu66BzKrqaBH7hMAvPbwHqkxzCICO3G3aOVK9Rs4UOXFxSHdxne6ZBJAL2M2SfR1cLMDdAtPR48edb+sB8kua8t6xH73u9/pxz/+sa6++mrNmDHDDQzWO/OnP/3JvY79fuLEifqf//kfTZ06Ve9617vOGSNnAcEutx6syZMnu2Hkl7/8pRsarEQzwHrFLIjZ9uzL3Hrrre71rUfOwqGFGwsnxtphpYbJyclukLIvu8we12uvvaZ77rlHixYtcu/TQqH1fnXUo/ShD33orCBr4cfaZkEqmAsuuEBf+9rXdMstt2jQoEHu82PPU2GI6+8EY4/F2p+Wlnbm8aSmpnb5PARYe+05tR48C6O/+tWv3JLY9j2JgZ7U73//+25gtyBuwdX+bvYasF67njJl4hg1NjLODUD8qGiUnjoY/nb6DxyoBRdd5JZLdlVyHnDEcVRKeAN6V4QMK0IcBTcrIbz22mvdXhwLLPa9BZD2PWXW87R8+fIzl1lJ4ZIlS9wxXcb+Xbp06Vm3W7Zs2Vk/W4mi3Y8Fk8CXhQUr/zh8+PCZ61nIas965+z+LcTY7SzAHDt2rNPHZvdXU1Pjjh1re592X/aYgrHQYj1WGzZscH+29lpoy8rK6vB+vve977mhyHqsrNfP/p02bZq2b9+unhbK82ClriOt+77N38Ge4717956zPXusdXV1uvzyy896jiz4dfQcne84t6QQxrmltVQq2RfaDGoAEOmeOSSVd74aSkjmLl2q4aNHq7igoFvLAwDoJUOGtC4FAJyn8y6ytTLAQLnib37zG/UWC1Ef+9jH3HFt7bWdDKV9SFq/fr3bU2fj2Czo5eXluT1I1rvX1f0NHz78zPiwttqPOQuwMXzWk2Uh1sZ8WYllsNu3Z+HQyjLty3qwbLyb9e5ZaWliYuI5ZZTBxqV15Xyfh66eI/PYY4+dFfaM9fj19Di3yupaDeyf1+l1sxrzVZk5qcfuGwC80uSTHtwjfeDsYePdlpGZqcWrVumxu+5SY0ODu1xAV6yw8pjjaAxTlQM9j942eBXcbCyVzd5otfQWCNqzckUr1bMyyrGn63kteNjkJFb2aKZPn66HH374rNsFeq0CbHyZjc2y8XHd8corr7j3+/Wvf/3MZVbW2Za1z+fznXN/1hNmJZRWWhgqG5/3zne+U6NGjXIfe9uexlBYW+x2gUlXrFezurra/TkQSm0cYFfbaP94QnkejPXA5efnuzNbBv4OFh6thLU9K3u1gGa3WWVrBvUSC21jRg3Xrv2HugxuOQ0nCG4AYsarJ6VLxktjg58vDNm0OXO06403dGjvXo05PYygK5v9fo1MTFQS4Q3oWYxvg1cLcNuUw1bqaKHKvm/PwsYnPvEJfelLX3Inu7DrfeQjH3FL7GxMmLH14Pbv3+9ex0rybEmBwMyOATYjooUP692z4GLXt5kT209O0p6NTbNgYb1LVr5n4+JsMpC2LJhZCaRtt6SkxB27ddlll7llgrYe2tNPP+3Ocmn3b8Hn9ddf7/D+LLzaDJT/9V//FXSilrYeffRRdzyY/btv3z73sVtPm82w+eY3v9m9jpWQ2uyONhbO2h/suWnPHo9N4GLbs8djQTmU58HYrJ/ve9/73FLRl156ye3htHJPK69szyY7sXFyNiGJ9Q7adm3yGRsXZz/3pGkTx6qpqaXLSVxsnFuiP7KXgQCAUDmnlwcIV7INUVi92h2qUFNVFdJt7PThPsa6AT0rO9tKrbxuBeI1uBkLKvbVkR/+8IfubJDvec973J4sGxtl65b179//TKmjzTpp09zbjJA2zstKBtuaM2eOO1OhBRybXt/KCb/5zW+e6RnqiC1ubcHCAp5NuGHhKzDbZIC1zXoObSp+6+G666673B5EC1A2QYcFMJtG/x3veIfbSzV06NAO7896p2ysm/V42bIEnbEeKwtlNpmKtc0mK7HJQmx5AHuujK3xduedd7ptmT17tts2m/K/MxaMrYfMxvvZ47HezlCeB2M9mjbD5zXXXKMrrrjCfd5/+9vfdnhftvyCbccmj7GeU3serXQysJxBTxkzapjSU1O6nKTElgXIbjjZo/cNAF7aXya90QOT5o6dNEnT5s1TyalTIc9kvMNx1EB4A3oOvW3oAQlOT81HD7cn0RbXbl/+ifPX2NSs7//ydjU0NWrY4M7PVFWnjdLJASv7rG0A0NsGZ0rfXi0lh3WaVe4EJXf//vdKTEpS/3aTiXVkckKCFieGeccAWl1/va0H5XUrEOXYI/cAW6jalhGwcsbPfOYzXjcnpqSlpmjqxLGqDmU9t8YCJfi7P4ELAESq4jrphX9PoHzeBg8frnnLlqmytPScsdAdOeA4quTcLhC+vDxCG3oEwa0H2Lg0Ky+0MXs2RT561sRxI+X3O+7yBJ1JlE/Zjfl91i4A6AuPH5BqemAI7/xly9wAZyWToXBOT1QCIEyTJ3vdAsQIglsPsKn/bdKVn/3sZ143JSaNHTlc2VkZqqnremGjnIbjfdImAOgrdc3SI/vC3052bq4WrVypxvp6NTeFlgTtVFgBvW7A+bPZWadM8boViBEEN0S8IYP6a/jQwSqvqO7yutbjluB0vmA3AESbF49Kp1qX0AzLzAULNHrCBBXl53drUW4/4Q04PzaZns0oCfQAghsins3YOXfGZNXXN3Q5I1qi0+IuDQAAscTvSPf2wPIAqWlp7vIApv70uqFdqZR0iOAGnB/KJNGDCG6ICpPHj1ZmRrrq6hu6vC7lkgBi0fYiaXdx+NuZOH26Js+c6fa6hTqx9DbHUTPhDeielBRpwgSvW4EYQnBDVBg1fIiGDx2ksoquF5DNbjghOaHNmgYA0cR63az3LdwqBut1y8jKUlVFRUi3sVNmOwluQPfY2rbJyV63AjGE4IaokJSU5JZL1oYwQUmS06ysxsI+aRcA9KUT1dLLx8LfzogxYzR78WKVFxd3OWNvwB7HUQ3hDQjd1KletwAxhuCGqDFp/Gilp6VRLgkgrj28T2rogTmYFi5frn6DBqmsqCik61u820pwA0KTk8PabehxBDdE1bIAQwcN6Ea5JOsPAYg9VY3SkwfC306/gQO16KKLVFNVpZbm5pBuc9RxVEJ4A7pmSwDYUgBADyK4IWokJydp7szJqq3tulwy2WlUZhPlkgBi07OHpLKud4VdmrN4sUaMHauigoJuLQ8AoAvMJoleQHBDVJkyfoxSU1PU0NDY5XX71R3qkzYBQF9r9kv37w5/O+mZmVqycqV8LS3uwtyhKLGeN8Ib0LHhw6XcXK9bgRhEcENUGTt6uIYM7B9iueRxJfq7DngAEI1ez5cOl4e/nalz5mjC1Kkq7Mai3JsdRy2UTAIdl0kCvYDghqiSmpKiOTMmq6qmrsvrJsqvvPojfdIuAOhrFpvu6YFFuZOSk7V09Wqlpqa6491CYXvgvQQ34Fw2/T9rt6GXENwQdaZMGKOU5CQ1NjV1ed28uh4YwQ8AEepgeWvPW7hGT5yo6fPnq+TUqZAX5bZ13eoJb8C5a7fZwttALyC4IeqMHzNSg0Msl0xvqVR6U2mftAsAvGBj3Zp94W0jISFBi1euVG6/fiovsVFsXbMVCbYT3ICzTZvmdQsQwwhuiDppqSmaPW2SqqpqQ7p+Xv3BXm8TAHiltF567nD42xk0dKjmLVumqvJy+XyhJcGDjqMKwhvQatCg1olJgF5CcENUmjJxjJKSktTU1PXaQ7n1R5Tg74HVagEgQj1xQKrugbmY5i9bpiEjRrglk6GwyMbyAMBpc+Z43QLEOIIbotKksaM0dPAAlZRXdnndJKdFOQ3H+qRdAOCFhhbp4X3hbycrJ8ctmbSlAZoaQ0uCFvHy6XVDvMvKYlIS9DqCG6JSenqaFs6ZpqrqmpAG0vero1wSQGxbd0zKrw5/OzPmz9eYiRNV3I3lAazXzU94QzybNUtK5LAavYtXGKKWjXPLysxQdW3XSwNkNhcrtaXr3jkAiFZ+p2eWB0hJTdXSiy92Jyypq6kJ6TY2VdQBghvilc0iOX26161AHCC4IWqNHjHULZksLq0I6fp5dYd6vU0A4KVdxdKOovC3M37qVE2ZPVtFBQUhLw9gM0w2Ed4Qj6ZOlVJTvW4F4gDBDVHLzgYvnDtdLS0taglhBrS8+kOSE+ac2QAQ4e7b3dr7Fo7ExEQtWb1aWdnZ7iyToWg8vbYbEFcSEqTZs71uBeIEwQ1RbcbkCRrUv59KQ5ikJNnfqOzGHlipFgAimI1ze+lo+NsZNmqU5ixerLKSEvlDnDlyr+OomvCGeDJunJST43UrECcIbohqOdmZWjB7msorQhuRzyQlAOLBI/uk+q5XS+nSwosu0sDBg1VaWBjS9S3ebWF5AMQTlgBAHyK4IerNmT5J6Wmpqq1r6PK6WY35SmnpgWnXACCCVTdJjx8Ifzu5/ftr0YoVqq2uVktzaEnwuKQiet0QD4YObf0C+gjBDVFv/JgRGjtymIpLy7q8boKkAbV7+qRdAOCl5w9LJV1Putul2YsWaeS4ce5EJd1ZHiDUSU2AqMXYNvQxghuiXlJSkhbPm6HGxuaQxmHY7JJJvq575wAgmrX4pft3h7+dtIwMLVm1Sv6WFjXU14d0GzuNdoTghlhm49rGj/e6FYgzBDfEhJlTJ6pfXo7KKmw1oc4lyqd+dfv7pF0A4KVNBdLBrosRujRl1ixNnD5dRSdPhnybrY6jFsIbYrm3zWaUBPoQwQ0xYUC/XM2ZPlll5V0HN9O/bp8SnJZebxcAeM0W5Q43PyUlJ7vLA6Slp6u6sutZfI1Vae4huCEW2ZpttnYb0McIbogZc2dOVlJSohoam0JaGiCv7nCftAsAvHS4QtrYAyuhjBo/XjMWLHBnmAx1/Noux1Ed4Q2xZvp0KSXF61YgDhHcEDMmjx+tkcOHqLg0tMViB9TulhymrQYQ+2ysW5MvvG0kJCS4M0zaTJPlxcUh3cbqGrYR3BBLkpKkWbO8bgXiFMENMSM1JUVL5s1UTW19SJOUpPpqlN1wok/aBgBeKm+Qnj0U/nYGDhmiBcuXq6qiQr6W0MrNDzmOyglviBUzZkhZWV63AnGK4IaYW9PNxruVhjjWbaD1ugFAHHjygFTVGP525i1dqqEjR6r41KluLQ8ARD0rj5w/3+tWII4R3BBTBg/sr0Vzp6u0rCKkMRgZzaXKaCrqk7YBgJcafdJDPbCMZWZ2thavXKmmhgY1NYaWBAslnaDXDbEwk2R6utetQBwjuCHmLJ0/U9nZmaqsrgnp+gNq6HUDEB9ePi6dCK0goVPT58/XuMmTu7U8wGa/Xz7CG6KVBbY5c7xuBeIcwQ0xZ9TwoZo9bZKKSkKbpCS78aRSW0Kb3hoAoplzenmAcKXYmOLVq5WYlKTamtBOklVLOkBwQ7SaN691GQDAQwQ3xByb+eyCBbOUmpys2rr6rq/v9rr1QP0QAESBPSXSNqtdDNP4qVM1bc4cleTnh7w8wHbHUSPhDdHGJiOxSUkAjxHcEJOmTBijKRPH6lRRaUjXz60/rCRf1yEPAGLBfbsln78HlgdYuVJZubmqLCsL6Ta2yuYOghuizYIFUnKy160ACG6ITYmJiVq2cLZ7FrixqesFuRPl14DavX3SNgDw2qkaae3R8LczbNQozVm6VBUlJfL7Qlsobp/jqIrwhmiRlydNnep1KwAXwQ0xa9a0iRo7cnjIvW796/bS6wYgbjy6T6prDn87Cy68UAOHDlVJUWgz9Fpk28LyAIgWCxfa2WCvWwG4eCUiphfkXr5krhoamtTS0vWZ4ETHp0E1O/ukbQDgtdpm6bH94W8nt18/t2SyvqZGzc2hJcETtkQAvW6IdAMHShMnet0K4AyCG2La3BmTNXzoIBWWhDb+ol/dAaW0hDZDGgBEuzVHpKLa8Lcza+FCjRw3TsX5+d1alNtPeEMkW7zYBnN63QrgDIIbYlp2VqY71q26ulb+EEpzEuTXoJrtfdI2APBai791opJwpaWnu8sD2H62oa4upNvYgi1HCG6IVEOHSmPGeN0K4CwEN8S8hXOma0D/PJWWV4Y8w2Rqc0WvtwsAIsGWU9K+0IYCd2ryzJmaNH26irqxPMBWx1EL4Q2RaMkSr1sAnIPghpg3sH+eFs+dodKyypAOJqwoYnD1tj5pGwBEAluUO9z8lJSUpCUXX6y0jAxVV4Z2osymg9pNcEOkGT1aGj7c61YA5yC4IS4smT9TOdlZqqgKbfxaTuMJpTeV9Hq7ACASHKuUNpwMfzsjx47VzIULVVpYGFJ5utnlOKojvCHSxrYBEYjghrgwcthgzZs5RYXFZSGX8Ayu3trr7QKASPHgHqkptKXYOl+U+6KL1G/gQJUXF4d0G9/pkkkgIkyeLA0a5HUrgKAIbogLdjCxatkC5WZnqryiKqTbZDUVKrPxVK+3DQAiQUWD9NTB8LczYPBgLVy+XDWVlfK1tIR0m8OOo1LCG7yWmiotXep1K4AOEdwQN0aPGOqWTBaVlNPrBgBBPHOwNcCFa87SpRo6erSKCgpCvs1mFuVGJCy2nZnpdSuADhHcEFdWLp2v/v1yVVIW2qyRGc2lym443uvtAoBI0OhrLZkMV2ZWlpasXKmWpiY1NoSWBIskHafXDV4ZMECaOdPrVgCdIrghrgwdPFAXLpqjkrLKkAfOu71uDmeCAcSHDSdaJysJ17S5czV28mR3eYDu9Lr5CG/wwvLlUiKHxYhsvEIRd5YvnqshA/u7JZOhSGupUm79kV5vFwBEAuf08gDhSk5J0dLVq5WcnKza6uqQbmPz/u4nuKGvTZzI9P+ICgQ3xOW6bisumK+Kymr5fKFNoWbruiU4oQ2yB4BoZwty28Lc4Ro3ZYqmzpmjkoKCkMcWb3ccNRDe0FdSUqQLLvC6FUBICG6IS8sWztaIYYN1qqg0pOun+Os0sKYHTkEDQJS4b7fk84c/o++SVauUlZenitLQ9rfNknYQ3NBXFiyQsrK8bgUQEoIb4lJudpa7PEBNbZ2aQ5yuekDNLqW0hFbuAwDRrqhWeqEHqsSHjBiheUuXqrK0VP4QqxysXLKS8IbeNnCgNHu2160AQkZwQ9xaMm+mRo8cpvxTJSFdP1F+Da18vdfbBQCR4rH9Um1T+NuZf+GFGjRsmEoKC0O6vkW2LSwPgN6UkCCtWMGEJIgqvFoRtzIz0nXxhYvU2NikxiYrzuladlMBywMAiBt1zdKj+8LfTk5enhavXKn62lo1N4WWBE9KOkWvG3rL9OnSkCFetwLoFoIb4tqC2dM0YexI5Z8qDvk2Qys3KcHPRCUA4sPao1KhTfcYppkLF2r0hAndWh7gDb9ffsIbepotsr1kidetALqN4Ia4lp6WqkuWL3Jnl2xoaOzGRCU7er1tABAJfI507+7wt5OaluZOVGKs5y0UFZIOEdzQ0y68UEpN9boVQLcR3BD35syYrCkTxupEQei9bgNr9yi1pQdWqAWAKLCtUNoT2nDgTk2cMcP9KurG8gDbHEfNhDf0lDFjpAkTvG4FcF4Iboh7KcnJunTFYiUnJaqqOrSzwAnuRCWber1tABAp7t0l+cPMT0lJSbpg9WplZGaqusL607rWIGkXwQ09ITlZWr7c61YA543gBtjYiykTtGjeDJ08VSx/iDOZZTWdUk79sV5vGwBEguNV0voemJtp+JgxmrVwocqKikLe3+5xHNUS3hAuG9eWk+N1K4DzRnADTi8Se+WqCzR4QD8VlpSFfLshVTZRSWgzUgJAtHtor9TQEv7+duGKFeo3aJDKikMrUbfV37YS3BCO0aOlmTO9bgUQFoIbcNqQQQN0yUWLVVVVq6YQlwdI8ddrEBOVAIgTlY3SUwfD307/gQO18KKLVFNZqZaW0JLgEcdRCeEN5yM9XbKJcWztNiCKEdyANpYvmqPJ40freH5oi8SaAUxUAiCOPHNQKq8PfztzlizR8NGjVdzN5QGAbrOFtm0JACDKEdyANtLT03TlxcvcAfRV1aEtXJQgR0MrX+/1tgFAJGj2Sw/sCX87NkHJktWr1dLcrMYGm4Kkazax5VHCG7pj6lRp/HivWwH0CIIbEGSiksVzbaKSkm5MVFKovLoDvd42AIgEr52UjoQ2KWSnps6erfFTp6rw5MmQb7PFceSjZBKhsIlIbM02IEYQ3IAgA+evWN06Ucmp4tAnKhla9YZSWkLrpQOAaGax6Z5d4W8nOSXF7XVLTU1VTVVVSLexRVv2EtzQFRvPdsklUkqK1y0BegzBDQhiyMD+umzlUlVX16oxxIlKEp0WDa/cIHFAASAOHCiTNoU+PK1DYydN0rS5c1Vy6lTIi3LvdBw1sK9FZ+bPl4YO9boVQI8iuAEdWLZwtqZMHKPjJ0OfqCSzqUj9a/f2arsAIFLcv0dqtrn6w6xyWLxqlXLy8lRRWhrSbex02jaCGzoyeLC0YIHXrQB6HMEN6EB6WqquWn2hUlKSVBniRCVmcPUWZpkEEBdK6qTnj4S/ncHDhmn+smWqLCuTzxdaEjzoOKogvKEdJzm5tUQykUNcxB5e1UAnpk8ep8XzZqqgGxOVJMqv4RXrJYeZzwDEvif2SzVN4W9n3rJlGjx8uFsyGQqLbJuZYRLtJFxwgZSX53UzgF5BcAO6mqhk5VINGdRfp4pCK+ExGc1lGlizs1fbBgCRoL5FergHKsSzc3O1eOVKNdbXq7kptCRYICmfXjcEjBkjzZjhdSuAXkNwA7oweGB/XbpiiWpq69XQ0Bjy7QbV7FBac+izUgJAtHrpmFRQHf52Zi5YoNETJqioG8sDWK+bn/CGjAxp1SqvWwH0KoIbEIILF83R7OkTdfRE6LOe2cLcIyrWK8EJc+Q+AEQ4vyPd2wPLA6SkprrLA9hU7vW1NvF/1ypPj3dDnFu5sjW8ATGM4AaEIDUlRddfsUoD+uWqoLAk5NultVRqUPW2Xm0bAESCHcXSruLwtzNx+nRNnjVLRQUFIZ8osxkmmwhv8cvKI8eO9boVQK8juAEhGjV8iK66+ELV1tWrrr4h5NsNqN2tjKaiXm0bAEQCW5Tbet/CkZiYqKWrVyszK0tV5eUh3caK2HcR3OKTrdW2bJnXrQD6BMEN6GbJ5PxZ03Ts5KmQZ5lMkDS8YoMS/KEt5A0A0Sq/Wlp3LPztDB89WrMXLVJZSegz+u5xHNUQ3uKKk5kpXX65lJTkdVOAPkFwA7ohOTlJ11+xQkMHDdTJU6HXBKX6ajSkenOvtg0AIoHNMNnQEv52Fl50kQYMHqyyotAqFizebSG4xQ0nMVEJV1whWXgD4gTBDeimoYMH6upLLlRTY7NqautCvl3/ugPKqe+BU9EAEMGqm1rXdgtX3oABWnTRRaqtrlZLc2gVC8ccR8WEt7iQcNFF0pAhXjcD6FMEN+A8LJk3Q4vnzdDx/EL5urEA7LDKDUppqerVtgGA1547LJWGfl6rQ1YuOXzMGBXl54d8mzf8/pAnNUEUT0YybZrXrQD6HMENOA9JSUl60+UrNGr4UJ3ILwz9dk6LRpa/pAR/D9QRAUCEavZLD+wJfzvpmZlasmqV/D6fGurrQ7pNqaSjBLeY5bdetgsv9LoZgCcIbsB5Gtg/T9deulw+n19V1TUh3y69pVLDql7r1bYBgNc25ksHQ5sUslNTZ8/W+GnTutXrZmPdWghvMceXnq5EG9eWyOEr4hOvfCAM82dN1bKFc3SyoFgtvtAX2s6rP6J+tT0wCAQAItg9O8PfRlJyspauWqXUtDRVV9py212zKs29BLeY4k9IUNJVVzEZCeIawQ0Ic70h63UbO3q4jp8MvWTSDKnapPQmK+oBgNh0uELaeDL87YyeOFEz5s9XaWFhyOPXdjqO6glvsWPFCiYjQdwjuAFhysvN1nWXr1BCQoLKK6tDvl2i/BpRsU6Jfls6FgBik411aw69ICEo278uXrFCuf36qbykJKTb2EjibQS3mOCbOlWJTEYCENyAnjB72iStWDpPp4pK1dQU+kLbqb5ajahYL3FwASBGldZLzx4OfzsDhw7V/AsvVFV5uXwhlqYfchyVs3+Nas2DBinJetsAENyAnmBng6+++ELNmDJeh4/nd2sq6uzGfA2s6YGBIAAQoZ48IFX1QHHBvAsu0JARI1RcUBDS9Z3TywMgOrWkpSnl6quZjAQ4jXcC0EOyMjP0tmsv1eCB/XWsm+PdBtVsU2bjqV5rGwB4qaFFenhv+NvJysnR4pUr1dTQoKbG0JKg7Y1P0usWlZORJF9zjZSR4XVTgIhBcAN60KjhQ3TDlavcHreyitAX2k6QNKLiZSX7emDFWgCIQC8fl06Gvlvs0IwFCzRm0iQVnQx91pPNfr/8hLeo4f6lVq+WBg/2uilARCG4AT1swexpumT5IhWVlKmhIfTaoGR/o0aUr5OcMEfxA0AE8jvSvbvD305KSoqWrl6txKQk1dWEtoam5cUDBLeo4Vu0SImTJ3vdDCDiENyAXhjvduXqZZo3c6qOnCiQrxvjKzKbSzS0anOvtg8AvLKrWNrevUryoMZPnaops2a5Y91CHVO83XHURHiLePVTpyp5wQKvmwFEJIIb0AvS01L1tmsv0ejhQ3X0eGiD6AP61+1Tv9p9vdY2APDSfbslnz/8NTSXrF6tzOxsVZaXh3Qbq3/YQXCLaDUjRihj1SqvmwFELIIb0EtskpIbr7lYqakpbtlkdwyt2qSshvxeaxsAeKWgRnrpWPjbGTZqlOYuWaKKkhL5Q6xs2Oc4qia8RaSK3Fxl22QkADpEcAN60cwpE3TV6mWqqKxRbV19yLdLkOMuzp3WXNGr7QMALzyyT6oPfcnLDi1YvlwDBw9WaWFo9ZcW77awPEDEKU9JUe5b38q0/0AXeIcAveziCxdp6YJZOnbylFpaQp94JMlp0ajyNUryhR74ACAa1DRJj+0Pfzu5/ftr0cqV7iQlLc2hJcHjkorodYsYVuia+da3KjElxeumABGP4Ab0suTkJN141WpNGjtah4+d7Nbi3Cm+Oo0qf1EJTkuvthEA+toLR6Ti2vC3M2vhQo0cO1ZF+aGXl9ui3N3ZF6N3VPn9Srj2WqXl5nrdFCAqENyAPpCXm623XXepcnOylV9Y0q3bZjSXanjFeomDDAAxpMUv3b8n/O2kZWS4E5X4fT411IdWoWCjjo+wT/VUnc+nmosuUr+RI71uChA1CG5AH5kwZqSuu3yFu7ZbZXVoaw8F5DYc1+BqlgkAEFveKJAOdG/upqAmz5qlidOnd6vXbYvjqIXw5olGv1/FCxZoxKxZXjcFiCoEN6APLVs4WysvmK+CwhLVd2NxbjOwdo/61+7ttbYBgBfu2Rl+QUFSUpLb65aWlqbqysqQbmN9c7sJbn3OwnL+1Kkau3Sp100Bog7BDehDtvbQ9Zev1MLZ09z13Zpbujd2bUjVJmXX98A82gAQIY5USq+eDH87o8aP14yFC90ZJkMdv7bLcVRHeOszfsfRkVGjNG71aq+bAkQlghvQx9LT0/T26y7X5AljdPjoyZDXHzIJkkZUvKKMpuJebSMA9KUH90hNoU+6G1RCQoIWr1ihvAEDVFYc2j7S7nIbwa3PHBw4UBOvucb9WwHoPoIb4IF+eTl65w1XaujggTp8vKBbs5slyq9RZWuV2lLVq20EgL5S3iA9cyj87QwYPFgLLrxQ1RUV8oVY0XDIcVRGeOt1+7KyNOGGGwhtQBgIboBHRg4brJuvv1yZ6WndnmkyyWnSqLIXlOyr67X2AUBfeuqAVNkQ/nbmLl2qoSNHqrigoFvLA6B32InJ7WlpGn/TTUpKTva6OUBUI7gBHpo+ebxuuGq1GhubVFoe2oD6gFRfrUaXPscC3QBiQqNPeqgH5l/KzM7WklWr1NTY6H6FokjSCXrdemVM2xvJyZp8001KSU31ujlA1CO4AREw0+SVq5eptKxSVTXdW402zVetMWXPK8nXA6epAcBjrxyXjvdAFfi0efM0bvJkFZ0MfdaTzX6/fIS3Hg1trzqOpt50k9IzM71uDhATCG6Ax6ze/6rVy7Rq2XzlFxR3e5mAtJZKjS57Xon+7t0OACKNc3p5gHClpKS4ywNYaV5tdXVIt7Fr7Se49QgLwC82NWn6TTcpOzfX6+YAMYPgBkSA5OQk3XDlai2aO0NHjuWrqbm5W7dPb6nQ6LIXlOhv6rU2AkBf2FsqbT0V/nbGT52qqbNnq+TUqZAngNrhOGokvIWlxe/Xc3V1mvm2t6nfwIFeNweIKQQ3IJKWCbj+Ms2cOlGHjubL5+ve3NgZzWUaXbZGif7uhT4AiDT37ZZ8/h5YHmDVKmXl5KiyrCyk2zSdDm84P81+v56srtbMG2/U4OHDvW4OEHMIbkAEycvJ1jtvvFJjRw3XoaMnu7VMgMloLtGo8jVK8HdvYW8AiCSFtdLao+Fvx2aXtFkmK0pK5A/xZNg+x1EV4a3bmvx+PVJWphnXXaeR48Z53RwgJhHcgAgzZGB/vevGKzVoQH8dPp7f7fCW2VSsUeVrleAQ3gBEr0f3SbU9UP09/8ILNWjYMJUUFoZ0fef0RCUIXaPfrwdLSzXjTW/ShGnTvG4OELMIbkAEsh63d7z5CmVlZOjoie4t0G2ymgo1svwlJTjdK7cEgEhR2yw9tj/87eT266dFK1aovrZWzSGOH7a5KE/R6xaSep9PD5WVaf6b36xpc+Z43RwgphHcgAg1Y8p43XLjlUpPS9Oxk4XdDm/ZjQUaUb5OcjhzDCA6rTkiFdaEv52ZCxdq1Pjx3V4ewKa0R8dqLbRVVGjhjTdq8qxZXjcHiHkENyCCzZk+WTdff7lSkpN0osCWiO2enMaTGlHxMuENQFTyOdL9u8PfTlp6ursotxxH9XV1Id2mXNJhgluHalpa9HBVlS5429s0cfp0r5sDxAWCGxDhFsye5oY3myHt5HmEt9yG4xpesYHwBiAqbSmU9paEv51JM2dq4owZbq9bqBUMWx1HzYS3c5Q3NenRmhotf9vb3IXOAfQNghsQBWx9t5vedJlbtpNf2P0jmLyGIxpZvo4xbwCi0j27JH+Y+SkpKcldlDsjM1PVlZUh3aZB0m6C21mO19Xpkbo6rXj72zVm4kSvmwPEFYIbECUuWDBLb7v2EjU3t+hUUWm3b5/TeEKj3EW6WecNQHQ5XiVtOBH+dkaOHeuOdystLJQ/xJkjLbjVEt5cOyor9WxLiy5/5zuZ8h/wAMENiCLLFs7RW66+WA2NTSosCW1B2baymoo0pvRZJfnqe6V9ANBbHtojNYVZNGAl5zbDZP+BA1VeXBzSbewut8V5cLPS0rXFxdqYlKRr3vlODR892usmAXGJ4AZEETvoWLF0nt585UrV1taruNSGz3dPeku5xpY+q+SWHpiqDQD6SEWj9NSB8LfTf9AgLbjoIrdcsqUltPUubZKS0jgNby2Oo4dOntSR7Gxd9653uYuaA/AGwQ2IwvB28YWLdP0VK1VVU6eSsopubyPVV62xpc8otbn7twUArzx9SCrvgYKBOUuWaNjo0SouKAj5Nm/E4aLc9Y6jO48cUfXgwbrulls0aOhQr5sExDWCGxCl4e2yFUt03WUXqbKqRqXloQ20byvFX++Gt4ym0MqFAMBrVir54N7wt5OZleUuD9DS1KTGBpuCpGu2pzwWR71u5X6//nzwoFJGjdKbbrlFAwYP9rpJQNwjuAFRHN4uX7lUV118ocrKq1ReUdXtbSQ5zRpd+ryyGvJ7pY0A/n97d/7c1nneC/yLfSUWEtxFUqRIbbRWa5eiJbGjKJXt2lnqpHZa386kP9yZ/nDnTvtTZ+72R2SauW1zZ9Kbe9vYbdzEa6Q6khVrNWXtpLjvJABi3885nfclpcqWJYMEQALk9zODAUQRB8fWEMT3vM/7PFRsF8eA4SIUC2zevh3tmzYtaih3j6pCWQPhbVxR8L/7+lDT0SFX2sSeQCJaeQxuRBVMr9fj1ImDOHn8gFx1W0rZpB4K1s19BFdyqCTnSERUTNrCeIBCGU0m7D12TN7HIvld+BI7g3tXeXC7nU7j//T2om3LFvzBD34Al9e70qdERAsY3IgqnJhNdPq5Izj93NcQicYxNbv4UQE6aGgMXYA3XoQaJCKiEusLAtfy3572RGJ49OadO+Gfmsp7KPdNTUNqFYY3MSf0fCyGXw0OYuehQ3KlrcrtXunTIqJHMLgRrZLw9q0TB/Hd099AJpPF2MR03h9CHtABqI9chS/6WcnOk4ioWN66A+TUwkvO9x09KgNKKJDfRa/sQnhbTbKahrcDAXwyPY0j3/oWvvnyy7Da7St9WkT0BQxuRKuE+ABydP8u/OClkzLIDY/lfwX5Ub7YTTSELgJagQOTiIhKaCYBnC1ChXdtYyN2HjyIcCAARcnvfa9P0xBeJeFNDBf/+dgYhjMZnPzud3H4+edhMBpX+rSI6EswuBGtIvLq8a5uvP6db8Npt6F/aBzqElpYe5L9aA2ehUHJr9saEdFK+E0fEMsUfpxdBw/C19goSybzISLbp6tgPMCYouCn9+8jK2a0/fCH2LZ3r/w9QkTlicGNaBXatqUTf/r906it8aB/aCzvq8iPsmdm0BZ4D5bs4od8ExEth0QWeLu38OM4XS7sPXoU6WQS2Ux+SVD04p2s0FU30RnzsmhCcu8efG1t+MMf/QjtGzeu9GkR0VdgcCNapbo6WvFnP3gR6xrr0Tc4hlwut+hjmJW4nPXmTI2W5ByJiAr1u2FgSrR7LFD37t1Y196OmYmJRQ3lFk09KklM7GeLRvHBwAA279olQ5soFyWi8sfgRrSKtTQ1yPDW1d4iw1s6I7bVL45ey6F57hxqojeACvuAQkSrn6oB/1SE8QBmiwX7jx+Xj5PxeF7PCQMYqKD3xVFNwy9mZ3FvakquMJ5+9VW52khElYHBjWiVq6+twX969UVs29yJgeExJFPpRR9D7Hiojd1AU+g8dOriwx8RUSndmAHu+As/zoatW9HV3Y2Zycm8mzt9pmmyK2O5l0ZeURT848gIIokEjp8+ja+/+KIMq0RUORjciNaAao9L7nnbu6Mbw2OTiMWTSzqOKzUqSyeNuSLUJRERFdE/3ZpffSuEXq/HvmPHYLPbEQ2F8nqOaOF0q4yDmyiNfC+Xw28HBmCxWnHq+9+X/42i+zARVRYGN6I1osppx2vfOYUj+3ZifHIGoUh0Scex5kJYH3gPtvR00c+RiGipxqLAhSJsx21qa8O2PXsQnJ3NuyvvXU2TbfXLzYim4VfpNHru34evvh4vvfYatuzcyc6RRBWKwY1oDbFZLfijF5/H80f3IxgMY2omsKRZb0Y1jdbgGXjifSU5TyKipfiXe0Bq8X2YHvPskSPw+HwIzszk9f0i3vWUUXCTXSNVFe+GQhgaHETHpk14+U/+RDZfIaLKxeBGtMaYTSa8fOo4vvfC8/Jq8uDIxJJmvemgoSFyGfXhy4BW+fOMiKjyRdLAu/cLP46npgZ7jhxBLBJBLpvfvt5hTYO/DMJbVJRGKgrOj43JoeJ7jh7Fi6+/jura2pU+NSIqEIMb0Rok9nEcPbALb/zRi/DVeNA7MIJMnh9Ovsib6JOrb0YlUfTzJCJarA8HgODStvF+jhhG3dTaKhuVLGY8wEoaVlVZGnm9vx8WiwXf+t738NxLL8HucKzoeRFRcTC4Ea1hWze248c/fBlbutrRPzS+5KYlYlj3+tl34EiNF/0ciYgWI6sCb90p/DiiQYlo4qFks3Iwdz78C+FpuWU0DZ+oKt4LhzE8OIi2zk688sYbMnyKC3VEtDrotKVscCGiVSUaS+Ctd87i91dvoNrrgq/as+RjBe2bMOvaCU3HjmVEtDJE642/Ogy0ews7jpLL4c2//3sM3L2Llg0b8nqOWNs6rdfDsEwNQMY0DZcUBcMTE7Ksc9fBgzjyzW/Carcvy+sT0fJhcCMiKZvL4YPfXcT7H30iO461NNUvufNYyujBhPcwMkZ30c+TiCgfG7zAXx4u/DjDfX345d/9HZxud97DqnfodOgu8UpXStNwVdNwP53GxPAw3DU1OHbqFLbu2sWukUSrFNfPiUgyGY04deIQXnvlFOxWK+4PjSGnKEsfGeB/F+5Ef9HPk4goH/1zwJWJwo/T2tkpW+j7p6by7sIr5rolS3hdfEhV8WtVxc1QCONDQ2jftAnffeMNdO/ezdBGtIpxxY2IHiM6Tf6/X72PgZEJtLc0wmq1LPlYEWsbptx7oerNRT1HIqKv4rMD/+0YYCqwcts/PY1f/OQn0On18Pp8eT2nU6fDviKvuiUW2vyPqipmJiag5nLYffgwDj//PCw2W1Ffi4jKD1fciOgx7a1N+PFrr2D3ts0YGptEOBJb8rFcqWGs978Da0Zs2yciWj7+BHBmsPDjiOHVOw8eRDgYhJJnJUK/piFUpGvj4hr7/YVVtsF0GiP9/bJT5LdffRUnXniBoY1ojeCKGxE9USqVxr/+9jz+7cJV2G1WNNTVLLkMR4MO/qrtCDi2ADpeMyKi5WE1Av/rBFC19MIBScx0E6tu0UgE9c3NeT2nEcAJg6HguWwXxQqbqGAIheRQ8M6tW2Vgq21oKOjYRFRZ+OmJiJ5IlEi+cuoEvv/C8zKw3R8cRTabW9KxxMDu2uh1tATPcuYbES2bVA74VW/hxxGNSfYeOyZHA2TS6byeIybATSzx+riqabirqviNqmJKUTA5MoJYOIxD3/gGXnr9dYY2ojWIK25ElPe+tzffOYt7/cNoqvfB7XIu+Vg5nQVTnv2IWdcV9RyJiL6MXgf89VGgqaqw42QzGfz/n/5UdnFsbm/P6zmiD+W39XroF1GtEFpYZQssrPSJxih1TU04cvIkNm3bxgYkRGsUgxsR5S0WT+A3Zz7GuYs9MBoNWNdYV9Bw14i1FdOuZ6EYuD+DiEqruxb4i/2FH+f+7dv455/9DB6fD3Znfhew9up06MrjvVLRNNzWNNmVUoxomR4fl1/ftmcPDj73HFyepc/YJKLKx+BGRIsi3jKuXL+Dtz84h2l/EOtF10nL0jtGKjqzDG8Re35Xr4mIluov9gHddYUdQ1VV/Os//ANuXbsmRwXks/oltte9oNfD/JTvFSWVV1UV0Qd72aan0djaKodpd3Z3c5WNiBjciGhpJqf9eOvds/jsdh9qqj3wVRd2JThmbpRjA3LGpZdgEhE9jSiVFCWTonSyEJOjo7Jk0myxwF1dnddztuh02PUlq25hTcOnqgoxci6XzWJqdBRGkwk7DhzAgRMn4KgqsL6TiFYNBjciWrJUOoMPz13CmfOXZVlP27oGGArooKbqDJit2oE5+0Z2niSikvjhNuBYW+HHOfv227hw5gzaOjvzKhkX33Far4dzYeUso2m4oWno1TTZiCQ8N4c5vx8t7e342smTWL9xI1fZiOhzGNyIqCDiLeR23yD++Z2zGB6fQmtzAxz2wvasJU01mHTvR8bE/RxEVFxVZuB/ngBspsKOI2a6/d+f/ATpVAq1jaLx/1dr1elwSKeTM94+0zSkFxqeiBU8q82G3YcOyc6VNru9sJMjolWJwY2IiiIYiuBf3vsIl3tuocrpQH1tdUFXizXoEXBuRcDZDU1X2BwkIqJHndwAvLKl8ONcOXcO77/5Jta1t8vyxny7TEYWLnqJFbZoKIS2ri65ytbS0VH4SRHRqsXgRkRFk8spOH+5B++cuYBoLI71LU0wmYwFHTNtdGHSfQAps69o50lEa5tRD/z344CvwIUtMdPtF3/zN5idmkJTa2vezxNz4MReNtGVcu/Ro9h9+DAsVmthJ0NEqx6DGxEV3cDION78zVn0DY6ixuuSjUsKW32D3Pfmr9oBVV9gfRMREYBnG4EfP1v4ce709ODtn/8cvsZGWe74NOIjV2BmBvFIBJ1bt8q5bI0tLYWfBBGtCQxuRFQSiWQKZy9cwb9duIpYPCkbl1gKGBsgZPV2zLh2ImpbX7TzJKK16y8PAxu8hR1DyeXw1s9+Jue7ifEATxKPRuGfnITL68WBr38d2/fvhynP8koiIoHBjYhKvvr26w/P41bvANxVzoL3vglJkw/Trt0snySigrR7gL86DBTavHGkvx+//Nu/hb2qClVu92NlkTPj49AbDNi8Ywf2nziB2oaGwl6QiNYkBjciKrl0JouPL/fgg48uIhiOorW5HnZbYfs5xBtXxNqG2aqdyBkdxTpVIlpj/mwXsK+5sGOIj1Lv//KXuPrxx7LRiLg4pSoK/NPTSCUSaN2wQQa2js2b2eKfiJaMwY2Ils341Cx+/eE59Nzqg81qRlNDbV7zj55GhQFB52YEHVu5/42IFq3aBvyP44CpwOa1Yu+aGA8ATYPBaMTc7Cx8DQ3Yd/w4unfvhslcWKk4ERGDGxEte+fJSz038e7ZTzA1G8C6xlo5PqDg4+qtcnh32NbO4d1ElDfRWfI/7wWaqgo/1oUPP8SZt9+W5ZI7DhzAs4cPP1Y6SUS0VAxuRLQiZgNzcmzA5eu3odfr0NJUD4Oh8HltKaMHM67dSFi4h4SInsxhAr7dBRxfPz8eoBgS8Tju9vSgsbWV3SKJqOgY3Ihoxaiqik9v9eKdMx9jZHwKjXU+eNxFuOwNIGppxoxrF7JGMe6WiGieCGkn1gOnOgEHqxeJqIIwuBHRigtHYnjvo9/jwpUbyGZzaGmug6UI+0E06DDn2Ai/8xmoektRzpWIKpNoCbK3CXhpc+GDt4mIVgKDGxGVBfFWdLtvEO9/9Al6+0dgs1nQVO8rSvmkojNhzrEJQccmBjiiNUfDjnodTm8EWrndjIgqGIMbEZXd6IAr12/jt+cvY2xyBjVeN2prPEVpoa3ojDLAiZuiL2wcARGVOdHdMdSHmshN/NcffVPOkSQiqmQMbkRUlsLRGM5d7MH5Sz2YC0fk6purSB+8VBHg7F0IOrZAMTDAEa0qmoqq1Ah8sVswpoPoGxjFiyeP4g++cWSlz4yIqCAMbkRU1iamZ+Xq29XP7iKTyWBdUz1s1uKUO4oZcHOOBwHOVpRjEtEK0VS4ksOoid2ERYk+/PLkjB96nR7/5c//GHU13hU9RSKiQjC4EVHZE29T9/qH8cHvLuHu/UGYTEY0N9TCaDQWLcCF7J0IOrcgZ2DXAqKKoilwJ4dQE7sFsxJ7+GVFUTA57Uc8mcKGtmb8+evfgasIMyOJiFYKgxsRVYxsLodrN+7iw3OXMDI+DY/LiTqfF3p9cYYwqdAjbO9EQAY4fsAjKmc6EdgSA6iJ34JJSTz8uvhYMxOYQ3BuvsT664f3Yt/OrbAWaaWeiGilMLgRUcWJxRO4cOUzfPT7a5gNzqGhrgYeV1VRGpgIGvQI2TsQcGxFzsiGBkTlRKfl4En0ozp2GyY1+fDr4uNMKBLD9EwAXo8LX9u/E0f27WRTEiJaNRjciKhiiavqZz6+jEuf3kIimUJDbQ1cVY4iBjgdotYW2YUyaa4tyjGJaGmMSgLuRD+8iT4Y1dTn/i4SjWFqJgCbzSpX104c2oP62poVO1ciolJgcCOiiibewgZHJ3Duk09x/U4fEokU6mur4XY5ixbghKSpGnOOzYhYWwBd4bPliCg/tswMvPE+2SlSB+1zP/uRaBxTswHZsGjb5k4cO7gbHa3NRf3ZJyIqFwxuRLQqiLeyodFJnL/cg56bvYglkqiv9Ra1hFLI6m0I2btkMxOOEiAqXTmkKzkEb7wX1lzoc3/3oCRyZjYIu92KHVu6cGT/TgY2Ilr1GNyIaFURb2kj41M4d0kEuHuIxhOo81XD6y5ugBONTKK2NjkPLmX2Fe24RGuZKReFJ9En97AZtOzjgS0cxbR/Dk6HDbu6N+HIvh1oW9fIwEZEawKDGxGtSuKtbWxyGh9fvi5nwEVicdTWeFHtcRX9Q17K6JXz4CLW9dD0xRlRQLRmaBoc6Ul4E71wpCeg+5Kf5WAogtnAnGznv+uZzTKwtTTVM7AR0ZrC4EZEq97oxDQ+vnId1z67i3AkhlpfaQKcojMhbGtHyNGFjNFd1GMTrTZ6NSPb+YvA9uj8tQfEx5PAXBj+QEjuWX12+xYc2rNdBjYiorWIwY2I1ozxqVlcuHIdl3tuywDnq/GgxusuyVX7hKkWEdt6RGytUPWcH0UkaSrsmRm5f82VGoZeUx7/Fk2DPxiGPzgn96ju29UtA1tTPTu7EtHaxuBGRGvO5LRfzoG71HMLc+GI/HAoyiiNxuJ3ixQz4WKWJhniYtYmaDqWUtLaY8kG58Nacvhzs9celcspci5jKByTK+L7dz2Dg89uk3MaiYiIwY2I1rDp2QCu3riLi9duYno2CIvFJGc/idbipSBKKcVcOBHiEuY6QKcvyesQlQNTLrawsjYESy7yxO+LJ1KY9geQzeZQ76vG3p3dchabaCpERET/gcGNiNa8WDyBz+704cKVGxgam4SqKPJDYzGHeX/ZWIH5Usr1SJu8JXkNouVmUFJy3poIbPas/4nfp6oq5sJRBIIhmEwmdLQ148DubXhmUwecDvuynjMRUaVgcCMieqRU627/EC59egu3egcQiyXgcVfJvXBGQ+mGbqeNboRFiLOuR87oKNnrEJWCTs2hKj0mw5roDvnokOwvymSzmPHPIRpLyBEdO7ZuxJ4dW7ChrRmGEv6MERGtBgxuRERfOkpgRs6Bu3z9DmYCQZhMRlnG5bDbSve6AJLmWkSsrYhbmpE1Okv2WkSFhjVHZkqurlWlxqDXck/9eRKr2iKwqaqGpgaf3L+2s3uj3FtKRET5YXAjInoKsTJw814/Ln56E4MjE0inM6j2ulHjdUGvL+0etbTRhZilWTY3EYGOe+JopYdjO9MTcKQmYM9MQw/1qd+vqCoCwTCCoTDsNiu6OlplYOvuaoe1RPtIiYhWMwY3IqI8KIoig5toZiJW4oLhqGxm4qv2wGm3lXwQsGhsErc0yiAn7hWDtaSvRwRNgT0zK4diO1MTsCiRr36KpslmI/5gCKl0Gj6vR85f271tE9rWNXJgNhFRARjciIgWKRiK4Obdfly7eRcjY1OIJ5JwOmyoqfaUrCPlo8SbdsrkkytxMWszm5tQ0RiU5PyqmrxNwvCUEshHpVJp+OfCsiRSrK6JIdl7tm/F9i2dcp8oEREVjsGNiGiJRGc8sRfuzv0hXLtxV86HS2ey8LicqPa6YDaZluU8RIfK+EKIi5vroemX53VpFdA0WLPB+VW19Lh8nO+amGjfH5gLIxyNwWQ0orHOJ1fWtnS1y+BW6lJiIqK1hsGNiKgIsrmcLKW8dW8A12/3YjYw34jB63Gh2lO1bB3zNOiQMnmRNNXKfXEJcy0UQ+kaqlBl0WkKrNkAbJlZ2DJ+WQpp0DKLKhkOhqKYC0Wg1+vgq/bKJiNbN7bLlv4iwBERUWkwuBERFVkylUbf4Ahu3O2XJZVz4YgcJyCamojVuOXe55MxOB+GOHGfMbqX9fVpZeeq2bL+haA2K1fUvqqpyJetLItVtcBcBKqiytLHZzZvwLbNG9DV3ros5cFERMTgRkRUUuID7737w+i51Yv7Q6PyzxaLWc6wcjkdK1JOpujMD0OcuKVM1dB0nKG1GphyEbmK9mBFLZ+GIk8Ka9F4Qu7nTKezcFc5sLGjFdu3dGFTZxvcVRxVQUS03BjciIiWgXirFeWTd2WIuyf3xkViceh1erhdDnhcVTCbV2Zvmgo9UqYaJM0+2egkbfQgbawCGObKmlFJwJwLw5qdkyHNlp2FUU0v+XhiOHYoHEM4EpPBrcppl3vVdj2zCZs616O22sOukEREK4jBjYhomYm3XdEuXeyJ6x0Ywb3+YQTDESg5BXa7Va7GiUHfK/khWeyVyxhd8yHO5F4Ic25kDU6AH96XlV5Nw5INw5ILwZJbuM+GF7U37cmt+5MIhaOyhb/BoEe1x4WNHW1y5lpHa5MckM2wRkRUHhjciIjKYE/c0OgEBkbG5Z64aX9QfqAWjR7EfiK3yyn3yJUDVWd4GOLmQ938YzZAKZxOzT0MZmIl7UFYM6nJor1GLqfIcl0R1nILFwrqfNVyv1p7SzPWtzTKdv5ERFR+GNyIiMqIKFGbmPZjcGRcllUODI8hFInJNbAqp0MGOavFXHarIDm9BRlDFXIGB7IGO7IGB3Lyfv6xqmcDC52Wg1FJwqTEZZmjSUnAqM7fm3MRmJRY3q348yV+xafSGcyFo4jF4nK11FPlREfbOmzubEN7azOa6n1s3U9EVAEY3IiIyphYHREllf1DY7jVOyDnZqXTGflB2+m0ocrhgMNuLbsg92UrdVn9gzA3H+6++FjTVXAreU2FUf1CKFu4l19TEwXtP8v7NDQNiWQK0VhCNhcRXSBFMxxftRtbuzrQ2d4iV9XYXISIqPIwuBERVQjRPGJ8chYT07MYHpuSXSpDkSiSyRTEG7kocRMNJZwOe9mUVi6GCgNUvUl2vRT3qs4ERd6bF+5Nj3zdPP/nh49F6PtCeNU9vm/vSXSaJlfE9J+7Zefv1YXHakbuKzOojz7OPHysk/8KyyunKIjFE4jFEkgk09CgwW61yvLaDW3r0LauAc2NdbLJCGesERFVNgY3IqIKJfYozQSCmJialV0q+wZHZedKsdIi3totZpMMcaLEUjymypfOZBGNxeW/cSaTlSutVQ47fNUeOVNtXWMdmhpqUe+rhtFYeeGdiIiejMGNiGiVEG/nYi+TCHLj07O4PziK8akZWTaXzeVg0OvhcNjkypxYlTGZuAJTzsS/mVhNFStpsURSdh0V/2ZiVXVdQx02tLegub4WjfU+2Q2y3MtliYioMAxuRESrmOhO+SDIifLKkfFJGeTEPihRZidYzGbYbRbYZKCzwFCBZZaVTFEUJFLphyEtncmIuk4YjAb57yFWTTtam9Eqyh4XgpoYF0FERGsLgxsR0RrrWim6VAaCIfjnwvAH5jA6OYOpGT/iyZQMD6rY7wUdrFYR6KzyJjpZsvNg4f/vk6kMkikR0FJIpebnsImFMhGaHTYrGup8aGmsQ021R5Y/1njd8LicDNNERMTgRkRE82V5QRHk5sIIBMNy79zoxDT8gdB8yEiLkCFaX0CW64k9c2aTef5ePjau+WAnglk2m5P70MRNNJPJLNwLD8Ow1QpfjRctTXWoq6lGTbUbPq8b1V43G4gQEdETMbgREdETpVLp+TA3F5bBTownEI/9cyFEowlZ1pfJ5mRAmafJvVYyzJlNsJjEvRlms1GGkkrdhyV+VcpQ9iCMPRLOxN+JUCaIACv/u81mWeIoQplYNfO6qhYez6+iiRVMIiKixWBwIyKiJRGhJRZPznc5jCUQicVla3oxosAfnA97YrXuwaqTWNWbDzji145onj//60es1InxBQaDfuFmgEFvgNGol/fiz0aD/vHQ9xUZ8EGYEsRraaoGRVXlTcw3k48VVa6Uice5XE7+Wez9E3//6OuJ54vgOR/KTLKs0etxy/loHleVDGkupwNOh0128RQNRMwmdvIkIqLiYXAjIqKSEL9eRImlCHaRWELun5tfocsinc7K1SsxTFyEu4e3lNhnl1kIUYoMUTJg5RS5964QIiCKzpry3vAf9yI0PmjQIgKYGGjusNtlQLNaLLBY5u8fhjKHXa6YVerqIRERVSYGNyIiKr+yxNx8+aUowxThToQ9EeCe9pyvIlbMxE3MNxOrYWKvnriJ4MYQRkRE5Y7BjYiIiIiIqMyt7RZgREREREREFYDBjYiIiIiIqMwxuBEREREREZU5BjciIiIiIqIyx+BGRERERERU5hjciIiIiIiIyhyDGxERERERUZljcCMiIiIiIipzDG5ERERERERljsGNiIiIiIiozDG4ERERERERlTkGNyIiIiIiojLH4EZERERERFTmGNyIiIiIiIjKHIMbERERERFRmWNwIyIiIiIiKnMMbkRERERERGWOwY2IiIiIiKjMMbgRERERERGVOQY3IiIiIiKiMsfgRkREREREVOYY3IiIiIiIiMocgxsREREREVGZY3AjIiIiIiIqcwxuREREREREZY7BjYiIiIiIqMwxuBEREREREZU5BjciIiIiIiKUt38HrCJV7W9CvdkAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1000x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== SUMMARY ===\n", "Total JD records: 841\n", "Total CR records: 826\n", "Total similarity pairs: 338433\n", "Match rate: 0.00%\n", "Neutral rate: 0.00%\n", "Mismatch rate: 0.00%\n"]}], "source": ["print(\"\\nKết quả Similarity Sample:\")\n", "print(df_similarity[['jd_id', 'cr_category', 'primary_skills_sim','secondary_skills_sim', 'total_similarity', 'suitability']].head(10))\n", "\n", "# <PERSON><PERSON><PERSON> tra phân bố nhãn\n", "class_counts = df_similarity['suitability'].value_counts()\n", "print(\"\\nPhân bố nhãn Suitability:\")\n", "print(class_counts)\n", "print(f\"\\nTổng số cặp JD-CR: {len(df_similarity)}\")\n", "\n", "# Th<PERSON>ng kê điểm similarity\n", "print(f\"\\nThống kê Primary Skills Similarity:\")\n", "print(f\"Mean: {df_similarity['primary_skills_sim'].mean():.4f}\")\n", "print(f\"Median: {df_similarity['primary_skills_sim'].median():.4f}\")\n", "print(f\"Min: {df_similarity['primary_skills_sim'].min():.4f}\")\n", "print(f\"Max: {df_similarity['primary_skills_sim'].max():.4f}\")\n", "\n", "# Tạo explode động cho biểu đồ\n", "labels = class_counts.index\n", "sizes = class_counts.values\n", "colors = ['#66b3ff', '#ff9999', '#99ff99'][:len(labels)]\n", "explode = [0.1 if i == 0 else 0 for i in range(len(labels))]\n", "\n", "# Vẽ biểu đồ tròn\n", "plt.figure(figsize=(10, 8))\n", "plt.pie(sizes, explode=explode, labels=labels, colors=colors, \n", "        autopct='%1.1f%%', shadow=True, startangle=90)\n", "plt.title('Class Distribution for Suitability (Based on Primary Skills Only)', fontsize=14)\n", "plt.axis('equal')\n", "plt.savefig('class_distribution_suitability.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "print(\"\\n=== SUMMARY ===\")\n", "print(f\"Total JD records: {len(df_jd)}\")\n", "print(f\"Total CR records: {len(df_cr)}\")\n", "print(f\"Total similarity pairs: {len(df_similarity)}\")\n", "print(f\"Match rate: {(class_counts.get('Match', 0) / len(df_similarity) * 100):.2f}%\")\n", "print(f\"Neutral rate: {(class_counts.get('Neutral', 0) / len(df_similarity) * 100):.2f}%\")\n", "print(f\"Mismatch rate: {(class_counts.get('Mismatch', 0) / len(df_similarity) * 100):.2f}%\")"]}, {"cell_type": "code", "execution_count": 47, "id": "8e82a3a9", "metadata": {}, "outputs": [], "source": ["df_similarity.to_csv('jd_cr_similarity.csv', index=False)"]}, {"cell_type": "code", "execution_count": 60, "id": "726f047a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          jd_id          cr_category  primary_skills_sim  \\\n", "589110  JOB_746  Mechanical Engineer                 1.0   \n", "618758  JOB_793             Advocate                 1.0   \n", "509939  JOB_633     Business Analyst                 1.0   \n", "509825  JOB_633  Mechanical Engineer                 1.0   \n", "509830  JOB_633  Mechanical Engineer                 1.0   \n", "...         ...                  ...                 ...   \n", "562553  JOB_710                   HR                 0.5   \n", "152419  JOB_184   Operations Manager                 0.5   \n", "152395  JOB_184   Operations Manager                 0.5   \n", "152391  JOB_184   Operations Manager                 0.5   \n", "152454  JOB_184     Python Developer                 0.5   \n", "\n", "        secondary_skills_sim  total_similarity          suitability  \n", "589110                   0.0          1.000000        Most Suitable  \n", "618758                   0.0          1.700000        Most Suitable  \n", "509939                   0.0          1.739130        Most Suitable  \n", "509825                   0.0          1.769231        Most Suitable  \n", "509830                   0.0          1.769231        Most Suitable  \n", "...                      ...               ...                  ...  \n", "562553                   0.0          1.357143        Most Suitable  \n", "152419                   0.0          1.393617        Most Suitable  \n", "152395                   0.0          1.393617        Most Suitable  \n", "152391                   0.0          1.393617        Most Suitable  \n", "152454                   0.0          0.500000  Moderately Suitable  \n", "\n", "[10000 rows x 6 columns]\n"]}], "source": ["# Lấy 20 dòng đầu tiên có điểm primary_skills_sim cao nhất\n", "top_20_similarities = df_similarity.sort_values(by='primary_skills_sim', ascending=False).head(10000)\n", "\n", "# In ra c<PERSON>c c<PERSON>t cần thiết\n", "print(top_20_similarities[['jd_id', 'cr_category', 'primary_skills_sim','secondary_skills_sim', 'total_similarity', 'suitability']])\n"]}, {"cell_type": "code", "execution_count": 61, "id": "4b0ff503", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>jd_id</th>\n", "      <th>cr_category</th>\n", "      <th>primary_skills_sim</th>\n", "      <th>secondary_skills_sim</th>\n", "      <th>adjectives_sim</th>\n", "      <th>adj_weight</th>\n", "      <th>total_similarity</th>\n", "      <th>suitability</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>JOB_0</td>\n", "      <td>Data Science</td>\n", "      <td>0.178571</td>\n", "      <td>0.285714</td>\n", "      <td>0.020408</td>\n", "      <td>43</td>\n", "      <td>1.341837</td>\n", "      <td>Most Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>JOB_0</td>\n", "      <td>Data Science</td>\n", "      <td>0.166667</td>\n", "      <td>0.250000</td>\n", "      <td>0.000000</td>\n", "      <td>7</td>\n", "      <td>0.416667</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>JOB_0</td>\n", "      <td>Data Science</td>\n", "      <td>0.222222</td>\n", "      <td>0.222222</td>\n", "      <td>0.000000</td>\n", "      <td>16</td>\n", "      <td>0.444444</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>JOB_0</td>\n", "      <td>Data Science</td>\n", "      <td>0.111111</td>\n", "      <td>0.166667</td>\n", "      <td>0.000000</td>\n", "      <td>43</td>\n", "      <td>0.277778</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>JOB_0</td>\n", "      <td>Data Science</td>\n", "      <td>0.181818</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1</td>\n", "      <td>0.181818</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>694651</th>\n", "      <td>JOB_989</td>\n", "      <td>Testing</td>\n", "      <td>0.142857</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3</td>\n", "      <td>0.142857</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>694653</th>\n", "      <td>JOB_989</td>\n", "      <td>Testing</td>\n", "      <td>0.200000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>12</td>\n", "      <td>0.200000</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>694658</th>\n", "      <td>JOB_989</td>\n", "      <td>Testing</td>\n", "      <td>0.142857</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3</td>\n", "      <td>0.142857</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>694660</th>\n", "      <td>JOB_989</td>\n", "      <td>Testing</td>\n", "      <td>0.200000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>12</td>\n", "      <td>0.200000</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "    <tr>\n", "      <th>694665</th>\n", "      <td>JOB_989</td>\n", "      <td>Testing</td>\n", "      <td>0.142857</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>3</td>\n", "      <td>0.142857</td>\n", "      <td>Moderately Suitable</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>338433 rows × 8 columns</p>\n", "</div>"], "text/plain": ["          jd_id   cr_category  primary_skills_sim  secondary_skills_sim  \\\n", "0         JOB_0  Data Science            0.178571              0.285714   \n", "1         JOB_0  Data Science            0.166667              0.250000   \n", "2         JOB_0  Data Science            0.222222              0.222222   \n", "3         JOB_0  Data Science            0.111111              0.166667   \n", "4         JOB_0  Data Science            0.181818              0.000000   \n", "...         ...           ...                 ...                   ...   \n", "694651  JOB_989       Testing            0.142857              0.000000   \n", "694653  JOB_989       Testing            0.200000              0.000000   \n", "694658  JOB_989       Testing            0.142857              0.000000   \n", "694660  JOB_989       Testing            0.200000              0.000000   \n", "694665  JOB_989       Testing            0.142857              0.000000   \n", "\n", "        adjectives_sim  adj_weight  total_similarity          suitability  \n", "0             0.020408          43          1.341837        Most Suitable  \n", "1             0.000000           7          0.416667  Moderately Suitable  \n", "2             0.000000          16          0.444444  Moderately Suitable  \n", "3             0.000000          43          0.277778  Moderately Suitable  \n", "4             0.000000           1          0.181818  Moderately Suitable  \n", "...                ...         ...               ...                  ...  \n", "694651        0.000000           3          0.142857  Moderately Suitable  \n", "694653        0.000000          12          0.200000  Moderately Suitable  \n", "694658        0.000000           3          0.142857  Moderately Suitable  \n", "694660        0.000000          12          0.200000  Moderately Suitable  \n", "694665        0.000000           3          0.142857  Moderately Suitable  \n", "\n", "[338433 rows x 8 columns]"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.DataFrame(df_similarity)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}