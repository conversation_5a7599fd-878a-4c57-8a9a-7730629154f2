# Job-Resume Matching API

FastAPI application for predicting job-resume suitability using XGBoost model.

## 🚀 Features

- **Single Prediction**: Predict suitability for one job-resume pair
- **Batch Prediction**: Predict suitability for multiple pairs at once
- **Model Information**: Get details about the loaded model
- **Feature Engineering**: Debug endpoint to see how features are processed
- **Health Checks**: Monitor API and model status

## 📋 Requirements

- Python 3.8+
- Trained XGBoost model files in `models/` directory
- Dependencies listed in `requirements.txt`

## 🛠️ Installation

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Ensure model files exist:**
```
api/models/
├── xgboost_model.joblib
├── scaler.joblib
└── model_metadata.json
```

3. **Run the API:**
```bash
cd api/app
python main.py
```

Or using uvicorn directly:
```bash
cd api/app
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 📖 API Documentation

Once running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🔗 API Endpoints

### Health Check
- `GET /` - Basic health check
- `GET /health` - Detailed health status

### Model Information
- `GET /model/info` - Get model details and performance metrics

### Predictions
- `POST /predict` - Single prediction
- `POST /predict/batch` - Batch predictions

### Debug
- `POST /features/engineer` - Show feature engineering process

## 📝 Usage Examples

### Single Prediction
```bash
curl -X POST "http://localhost:8000/predict" \
     -H "Content-Type: application/json" \
     -d '{
       "primary_skills_sim": 0.75,
       "secondary_skills_sim": 0.60,
       "adjectives_sim": 0.45
     }'
```

### Batch Prediction
```bash
curl -X POST "http://localhost:8000/predict/batch" \
     -H "Content-Type: application/json" \
     -d '{
       "features_list": [
         {
           "primary_skills_sim": 0.75,
           "secondary_skills_sim": 0.60,
           "adjectives_sim": 0.45
         },
         {
           "primary_skills_sim": 0.85,
           "secondary_skills_sim": 0.70,
           "adjectives_sim": 0.55
         }
       ]
     }'
```

## 📊 Response Format

### Single Prediction Response
```json
{
  "suitability_class": 2,
  "suitability_label": "Most Suitable",
  "confidence_scores": {
    "Not Suitable": 0.1,
    "Moderately Suitable": 0.3,
    "Most Suitable": 0.6
  },
  "features_used": {
    "primary_skills_sim": 0.75,
    "secondary_skills_sim": 0.60,
    "adjectives_sim": 0.45
  }
}
```

## 🎯 Suitability Classes

- **0**: Not Suitable
- **1**: Moderately Suitable  
- **2**: Most Suitable

## 🔧 Configuration

The API automatically loads model files from the `models/` directory. Ensure these files are generated by running the training script first.

## 🐳 Docker Support

Build and run with Docker:
```bash
cd api
docker build -t job-resume-api .
docker run -p 8000:8000 job-resume-api
```

## 📈 Model Training

To generate the required model files, run the training script:
```bash
python four_models_training.py
```

This will create the necessary files in `api/models/` directory.

## 🔍 Monitoring

- Check `/health` endpoint for detailed status
- Monitor logs for prediction errors
- Use `/features/engineer` for debugging feature processing

## 🚨 Error Handling

The API includes comprehensive error handling:
- Model loading failures
- Invalid input validation
- Prediction errors
- Missing model files

All errors return appropriate HTTP status codes and descriptive messages.
