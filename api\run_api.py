#!/usr/bin/env python3
"""
Script to run the Job-Resume Matching API
"""

import os
import sys
import subprocess
from pathlib import Path

def check_model_files():
    """Check if required model files exist"""
    models_dir = Path("models")
    required_files = [
        "xgboost_model.joblib",
        "model_metadata.json"
    ]
    
    print("🔍 Checking model files...")
    
    if not models_dir.exists():
        print(f"❌ Models directory not found: {models_dir}")
        return False
    
    missing_files = []
    for file_name in required_files:
        file_path = models_dir / file_name
        if file_path.exists():
            print(f"✅ Found: {file_path}")
        else:
            print(f"❌ Missing: {file_path}")
            missing_files.append(file_name)
    
    if missing_files:
        print(f"\n⚠️ Missing model files: {missing_files}")
        print("Please run the training script first:")
        print("  python four_models_training.py")
        return False
    
    print("✅ All model files found!")
    return True

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def run_api(host="0.0.0.0", port=8000, reload=True):
    """Run the FastAPI application"""
    print(f"🚀 Starting API server on {host}:{port}")
    print(f"📖 API Documentation: http://localhost:{port}/docs")
    print(f"🔍 Health Check: http://localhost:{port}/health")
    print("\nPress Ctrl+C to stop the server")
    
    try:
        # Change to app directory
        os.chdir("app")
        
        # Run uvicorn
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "main:app",
            "--host", host,
            "--port", str(port)
        ]
        
        if reload:
            cmd.append("--reload")
        
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Error running server: {e}")

def main():
    """Main function"""
    print("🤖 JOB-RESUME MATCHING API RUNNER")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("app/main.py").exists():
        print("❌ Please run this script from the api/ directory")
        print("Current directory should contain app/main.py")
        return
    
    # Check model files
    if not check_model_files():
        return
    
    # Install dependencies
    if not install_dependencies():
        return
    
    # Run API
    run_api()

if __name__ == "__main__":
    main()
