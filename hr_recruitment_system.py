#!/usr/bin/env python3
"""
HR Recruitment System
Hệ thống tuyển dụng tự động cho HR sử dụng mô hình XGBoost
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from pathlib import Path
import pickle
from xgboost_prediction import XGBoostPredictor

class HRRecruitmentSystem:
    def __init__(self):
        self.predictor = XGBoostPredictor()
        self.results_folder = "hr_results"
        self.candidates_db = "candidates_database.csv"
        self.ensure_folders_exist()
        
    def ensure_folders_exist(self):
        """Tạo các folder cần thiết"""
        os.makedirs(self.results_folder, exist_ok=True)
        os.makedirs("job_requirements", exist_ok=True)
        os.makedirs("candidate_uploads", exist_ok=True)
    
    def create_job_requirement_template(self):
        """
        Tạo template để HR điền thông tin job requirements
        """
        template = {
            "job_info": {
                "job_id": "JOB_2024_001",
                "job_title": "Senior Python Developer",
                "department": "Engineering",
                "location": "Ho Chi Minh City",
                "employment_type": "Full-time",
                "salary_range": "25-35M VND",
                "posting_date": datetime.now().strftime("%Y-%m-%d")
            },
            "requirements": {
                "required_skills": [
                    "Python", "Django", "PostgreSQL", "Docker", "AWS"
                ],
                "preferred_skills": [
                    "React", "Redis", "Kubernetes", "CI/CD"
                ],
                "required_experience": 3,  # years
                "required_education": 2,   # 1=High school, 2=Bachelor, 3=Master, 4=PhD
                "language_requirements": ["English - Intermediate"],
                "certifications": ["AWS Certified Developer (preferred)"]
            },
            "job_description": """
We are looking for a Senior Python Developer to join our engineering team.
You will be responsible for developing scalable web applications using Django,
working with PostgreSQL databases, and deploying applications on AWS.

Key Responsibilities:
- Develop and maintain Python/Django applications
- Design and optimize database schemas
- Collaborate with frontend developers
- Participate in code reviews and technical discussions
- Mentor junior developers

Requirements:
- 3+ years of Python development experience
- Strong knowledge of Django framework
- Experience with PostgreSQL and database optimization
- Familiarity with Docker and AWS services
- Good English communication skills
            """,
            "company_culture": {
                "work_style": "Hybrid",
                "team_size": "10-15 people",
                "company_stage": "Scale-up",
                "benefits": ["Health insurance", "13th month salary", "Flexible hours"]
            },
            "screening_preferences": {
                "minimum_confidence": 0.7,  # Chỉ show candidates có confidence >= 70%
                "max_candidates_to_review": 20,  # Top 20 candidates
                "priority_factors": ["skill_similarity", "experience_match"],
                "exclude_overqualified": False  # Có loại bỏ ứng viên quá qualified không
            }
        }
        
        # Save template
        template_file = "job_requirements/job_requirement_template.json"
        with open(template_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Job requirement template created: {template_file}")
        print("📝 Please edit this file with your specific job requirements")
        return template_file
    
    def load_job_requirements(self, job_file):
        """
        Load job requirements từ file JSON
        """
        try:
            with open(job_file, 'r', encoding='utf-8') as f:
                job_data = json.load(f)
            print(f"✅ Loaded job requirements: {job_data['job_info']['job_title']}")
            return job_data
        except FileNotFoundError:
            print(f"❌ Job file not found: {job_file}")
            return None
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON format in: {job_file}")
            return None
    
    def process_candidate_folder(self, folder_path):
        """
        Xử lý folder chứa CV của candidates
        """
        candidates = []
        folder = Path(folder_path)
        
        if not folder.exists():
            print(f"❌ Folder not found: {folder_path}")
            return []
        
        # Supported file types
        supported_extensions = ['.pdf', '.doc', '.docx', '.txt']
        
        for file_path in folder.iterdir():
            if file_path.suffix.lower() in supported_extensions:
                candidate_data = self.extract_candidate_info(file_path)
                if candidate_data:
                    candidates.append(candidate_data)
        
        print(f"✅ Processed {len(candidates)} candidate files")
        return candidates
    
    def extract_candidate_info(self, file_path):
        """
        Trích xuất thông tin từ CV file
        (Simplified version - trong thực tế cần CV parsing library)
        """
        # Đây là simplified version
        # Trong thực tế, bạn sẽ cần sử dụng CV parsing libraries như:
        # - pyresparser
        # - spacy với custom NER models
        # - hoặc API services như HireAbility, Sovren
        
        candidate_id = f"CAND_{file_path.stem}"
        
        # Mock data extraction (replace with real CV parsing)
        mock_candidate = {
            "candidate_id": candidate_id,
            "file_name": file_path.name,
            "name": file_path.stem.replace('_', ' ').title(),
            "email": f"{file_path.stem.lower()}@email.com",
            "phone": "+84 xxx xxx xxx",
            "skills": self.mock_extract_skills(file_path.stem),
            "experience": np.random.randint(0, 10),
            "education": np.random.randint(1, 4),
            "location": np.random.choice(["Ho Chi Minh City", "Hanoi", "Da Nang"]),
            "resume_text": f"Resume content for {file_path.stem}...",
            "processed_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return mock_candidate
    
    def mock_extract_skills(self, filename):
        """
        Mock skill extraction (replace with real implementation)
        """
        skill_pools = {
            'python': ['Python', 'Django', 'Flask', 'FastAPI'],
            'java': ['Java', 'Spring', 'Hibernate', 'Maven'],
            'frontend': ['JavaScript', 'React', 'Vue.js', 'HTML', 'CSS'],
            'data': ['Python', 'SQL', 'Pandas', 'Machine Learning'],
            'devops': ['Docker', 'Kubernetes', 'AWS', 'CI/CD']
        }
        
        filename_lower = filename.lower()
        skills = []
        
        for category, skill_list in skill_pools.items():
            if category in filename_lower:
                skills.extend(np.random.choice(skill_list, size=np.random.randint(2, 4), replace=False))
        
        if not skills:  # Default skills
            skills = np.random.choice(['Python', 'Java', 'JavaScript', 'SQL'], size=3, replace=False)
        
        return list(skills)

    def calculate_job_candidate_features(self, job_data, candidate_data):
        """
        Tính toán features giữa job requirements và candidate profile
        """
        # Skill similarity
        job_skills = set([skill.lower() for skill in job_data['requirements']['required_skills']])
        candidate_skills = set([skill.lower() for skill in candidate_data['skills']])

        skill_similarity = len(job_skills.intersection(candidate_skills)) / len(job_skills.union(candidate_skills)) if job_skills.union(candidate_skills) else 0

        # Experience match
        required_exp = job_data['requirements']['required_experience']
        candidate_exp = candidate_data['experience']
        experience_match = min(candidate_exp / required_exp, 1.0) if required_exp > 0 else 1.0

        # Education match
        required_edu = job_data['requirements']['required_education']
        candidate_edu = candidate_data['education']
        education_match = 1.0 if candidate_edu >= required_edu else candidate_edu / required_edu

        # Location compatibility
        job_location = job_data['job_info']['location'].lower()
        candidate_location = candidate_data['location'].lower()
        location_compatibility = 1.0 if job_location == candidate_location else 0.6

        # Text similarity (simplified)
        job_text = job_data['job_description'].lower()
        candidate_text = candidate_data['resume_text'].lower()

        # Simple word overlap for TF-IDF simulation
        job_words = set(job_text.split())
        candidate_words = set(candidate_text.split())
        text_similarity = len(job_words.intersection(candidate_words)) / len(job_words.union(candidate_words)) if job_words.union(candidate_words) else 0

        # Create feature vector
        features = {
            'tfidf_unigram': text_similarity,
            'tfidf_bigram': text_similarity * 0.8,  # Approximation
            'tfidf_trigram': text_similarity * 0.6,
            'tfidf_char': text_similarity * 0.4,
            'skill_similarity': skill_similarity,
            'experience_match': experience_match,
            'education_match': education_match,
            'semantic_similarity': text_similarity * 0.7,  # Approximation
            'location_compatibility': location_compatibility,
            'length_ratio': min(len(candidate_text) / len(job_text), 1.0) if len(job_text) > 0 else 0.5,
            'skill_count_ratio': min(len(candidate_data['skills']) / len(job_data['requirements']['required_skills']), 1.0),
            'required_experience': required_exp,
            'candidate_experience': candidate_exp,
            'required_education': required_edu,
            'candidate_education': candidate_edu,
            'job_skill_count': len(job_data['requirements']['required_skills']),
            'resume_skill_count': len(candidate_data['skills']),
            'job_length': len(job_text),
            'resume_length': len(candidate_text),
            'exp_edu_interaction': experience_match * education_match
        }

        return features

    def screen_candidates(self, job_data, candidates):
        """
        Screening candidates cho job position
        """
        print(f"\n🔍 SCREENING {len(candidates)} CANDIDATES")
        print(f"📋 Position: {job_data['job_info']['job_title']}")
        print("="*60)

        results = []

        for candidate in candidates:
            # Calculate features
            features = self.calculate_job_candidate_features(job_data, candidate)

            # Get prediction
            prediction = self.predictor.predict_single(features)

            # Add candidate info to result
            result = {
                'candidate_id': candidate['candidate_id'],
                'name': candidate['name'],
                'email': candidate['email'],
                'phone': candidate['phone'],
                'file_name': candidate['file_name'],
                'skills': candidate['skills'],
                'experience': candidate['experience'],
                'education': candidate['education'],
                'location': candidate['location'],
                'prediction': prediction['prediction'],
                'prediction_label': prediction['prediction_label'],
                'confidence': prediction['confidence'],
                'probabilities': prediction['probabilities'],
                'features': features,
                'match_reasons': self.generate_match_reasons(features, prediction),
                'red_flags': self.identify_red_flags(job_data, candidate, features)
            }

            results.append(result)

        # Sort by prediction and confidence
        results.sort(key=lambda x: (x['prediction'], x['confidence']), reverse=True)

        # Apply screening preferences
        filtered_results = self.apply_screening_preferences(results, job_data['screening_preferences'])

        print(f"✅ Screening completed: {len(filtered_results)} qualified candidates found")
        return filtered_results

    def generate_match_reasons(self, features, prediction):
        """
        Tạo lý do tại sao candidate phù hợp
        """
        reasons = []

        if features['skill_similarity'] > 0.7:
            reasons.append(f"🎯 Excellent skill match ({features['skill_similarity']:.1%})")
        elif features['skill_similarity'] > 0.5:
            reasons.append(f"✅ Good skill match ({features['skill_similarity']:.1%})")

        if features['experience_match'] >= 1.0:
            reasons.append(f"💼 Meets experience requirement ({features['candidate_experience']} years)")
        elif features['experience_match'] > 0.8:
            reasons.append(f"💼 Close to experience requirement ({features['candidate_experience']} years)")

        if features['education_match'] >= 1.0:
            reasons.append("🎓 Education requirement satisfied")

        if features['location_compatibility'] >= 1.0:
            reasons.append("📍 Perfect location match")
        elif features['location_compatibility'] > 0.5:
            reasons.append("📍 Acceptable location")

        if prediction['confidence'] > 0.9:
            reasons.append("🔥 Very high confidence match")
        elif prediction['confidence'] > 0.8:
            reasons.append("⭐ High confidence match")

        return reasons

    def identify_red_flags(self, job_data, candidate, features):
        """
        Xác định các red flags
        """
        red_flags = []

        if features['experience_match'] < 0.5:
            required = job_data['requirements']['required_experience']
            actual = candidate['experience']
            red_flags.append(f"⚠️ Under-experienced: {actual} years vs {required} required")

        if features['skill_similarity'] < 0.3:
            red_flags.append("⚠️ Low skill match - may need significant training")

        if features['education_match'] < 0.8:
            red_flags.append("⚠️ Education level below requirement")

        if features['location_compatibility'] < 0.6:
            red_flags.append("⚠️ Location mismatch - may require relocation")

        # Overqualification check
        if features['experience_match'] > 2.0:
            red_flags.append("💰 Potentially overqualified - may expect higher salary")

        return red_flags

    def apply_screening_preferences(self, results, preferences):
        """
        Áp dụng screening preferences của HR
        """
        # Filter by minimum confidence
        min_confidence = preferences.get('minimum_confidence', 0.7)
        filtered = [r for r in results if r['confidence'] >= min_confidence]

        # Limit number of candidates
        max_candidates = preferences.get('max_candidates_to_review', 20)
        filtered = filtered[:max_candidates]

        # Exclude overqualified if requested
        if preferences.get('exclude_overqualified', False):
            filtered = [r for r in filtered if r['features']['experience_match'] <= 2.0]

        return filtered

    def generate_hr_report(self, job_data, screening_results):
        """
        Tạo báo cáo cho HR
        """
        job_id = job_data['job_info']['job_id']
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create summary statistics
        total_candidates = len(screening_results)
        highly_suitable = len([r for r in screening_results if r['prediction'] == 2])
        moderately_suitable = len([r for r in screening_results if r['prediction'] == 1])
        not_suitable = len([r for r in screening_results if r['prediction'] == 0])

        avg_confidence = np.mean([r['confidence'] for r in screening_results]) if screening_results else 0

        # Create detailed report
        report = {
            'job_info': job_data['job_info'],
            'screening_summary': {
                'total_candidates_screened': total_candidates,
                'highly_suitable': highly_suitable,
                'moderately_suitable': moderately_suitable,
                'not_suitable': not_suitable,
                'average_confidence': avg_confidence,
                'screening_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },
            'top_candidates': screening_results[:10],  # Top 10 for detailed review
            'recommendations': self.generate_hr_recommendations(screening_results, job_data)
        }

        # Save detailed results to CSV
        csv_file = f"{self.results_folder}/{job_id}_screening_results_{timestamp}.csv"
        self.save_results_to_csv(screening_results, csv_file)

        # Save summary report to JSON
        json_file = f"{self.results_folder}/{job_id}_hr_report_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n📊 HR REPORT GENERATED")
        print(f"📁 Detailed results: {csv_file}")
        print(f"📁 Summary report: {json_file}")

        return report, csv_file, json_file

    def save_results_to_csv(self, results, filename):
        """
        Lưu kết quả screening vào CSV file
        """
        # Flatten results for CSV
        csv_data = []
        for result in results:
            row = {
                'candidate_id': result['candidate_id'],
                'name': result['name'],
                'email': result['email'],
                'phone': result['phone'],
                'file_name': result['file_name'],
                'skills': ', '.join(result['skills']),
                'experience_years': result['experience'],
                'education_level': result['education'],
                'location': result['location'],
                'prediction_score': result['prediction'],
                'prediction_label': result['prediction_label'],
                'confidence': result['confidence'],
                'prob_not_suitable': result['probabilities']['Not Suitable'],
                'prob_moderately_suitable': result['probabilities']['Moderately Suitable'],
                'prob_highly_suitable': result['probabilities']['Highly Suitable'],
                'skill_similarity': result['features']['skill_similarity'],
                'experience_match': result['features']['experience_match'],
                'education_match': result['features']['education_match'],
                'location_compatibility': result['features']['location_compatibility'],
                'match_reasons': ' | '.join(result['match_reasons']),
                'red_flags': ' | '.join(result['red_flags']) if result['red_flags'] else 'None'
            }
            csv_data.append(row)

        df = pd.DataFrame(csv_data)
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"✅ Results saved to CSV: {filename}")

    def generate_hr_recommendations(self, results, job_data):
        """
        Tạo recommendations cho HR
        """
        recommendations = []

        if not results:
            recommendations.append("❌ No qualified candidates found. Consider:")
            recommendations.append("   • Lowering experience requirements")
            recommendations.append("   • Expanding skill requirements")
            recommendations.append("   • Increasing salary range")
            return recommendations

        # Interview recommendations
        top_candidates = [r for r in results if r['confidence'] > 0.8]
        if top_candidates:
            recommendations.append(f"🎯 PRIORITY INTERVIEWS: {len(top_candidates)} high-confidence candidates")
            for candidate in top_candidates[:3]:
                recommendations.append(f"   • {candidate['name']} - {candidate['prediction_label']} ({candidate['confidence']:.1%})")

        # Skill analysis
        skill_matches = [r['features']['skill_similarity'] for r in results]
        avg_skill_match = np.mean(skill_matches)

        if avg_skill_match < 0.5:
            recommendations.append("⚠️ SKILL GAP DETECTED:")
            recommendations.append("   • Consider providing training for selected candidates")
            recommendations.append("   • Review if skill requirements are too specific")

        # Experience analysis
        exp_matches = [r['features']['experience_match'] for r in results]
        avg_exp_match = np.mean(exp_matches)

        if avg_exp_match < 0.7:
            recommendations.append("⚠️ EXPERIENCE MISMATCH:")
            recommendations.append("   • Consider junior candidates with growth potential")
            recommendations.append("   • Offer mentorship programs")

        # Location analysis
        location_issues = len([r for r in results if r['features']['location_compatibility'] < 0.8])
        if location_issues > len(results) * 0.5:
            recommendations.append("📍 LOCATION CHALLENGES:")
            recommendations.append("   • Consider remote work options")
            recommendations.append("   • Offer relocation assistance")

        return recommendations

    def display_screening_summary(self, report):
        """
        Hiển thị tóm tắt kết quả screening
        """
        print(f"\n📊 SCREENING SUMMARY")
        print("="*50)
        print(f"Position: {report['job_info']['job_title']}")
        print(f"Department: {report['job_info']['department']}")
        print(f"Location: {report['job_info']['location']}")
        print(f"Salary Range: {report['job_info']['salary_range']}")

        summary = report['screening_summary']
        print(f"\n📈 RESULTS:")
        print(f"Total Candidates Screened: {summary['total_candidates_screened']}")
        print(f"Highly Suitable: {summary['highly_suitable']} candidates")
        print(f"Moderately Suitable: {summary['moderately_suitable']} candidates")
        print(f"Not Suitable: {summary['not_suitable']} candidates")
        print(f"Average Confidence: {summary['average_confidence']:.1%}")

        print(f"\n🎯 TOP 5 CANDIDATES:")
        print("-" * 80)
        print(f"{'Rank':<4} {'Name':<20} {'Confidence':<12} {'Prediction':<18} {'Key Skills':<25}")
        print("-" * 80)

        for i, candidate in enumerate(report['top_candidates'][:5], 1):
            skills_str = ', '.join(candidate['skills'][:3])  # Show first 3 skills
            if len(candidate['skills']) > 3:
                skills_str += f" (+{len(candidate['skills'])-3} more)"

            print(f"{i:<4} {candidate['name']:<20} {candidate['confidence']:<11.1%} {candidate['prediction_label']:<18} {skills_str:<25}")

        print(f"\n💡 RECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"   {rec}")

    def run_interactive_screening(self):
        """
        Chạy screening process interactive cho HR
        """
        print("🏢 HR RECRUITMENT SYSTEM")
        print("="*50)
        print("Welcome to the AI-powered candidate screening system!")

        while True:
            print(f"\n📋 MAIN MENU:")
            print("1. Create job requirement template")
            print("2. Screen candidates for a position")
            print("3. View previous screening results")
            print("4. Exit")

            choice = input("\nSelect option (1-4): ").strip()

            if choice == "1":
                self.create_job_requirement_template()

            elif choice == "2":
                self.run_candidate_screening()

            elif choice == "3":
                self.view_previous_results()

            elif choice == "4":
                print("👋 Thank you for using HR Recruitment System!")
                break

            else:
                print("❌ Invalid choice. Please select 1-4.")

    def run_candidate_screening(self):
        """
        Chạy quá trình screening candidates
        """
        print(f"\n🔍 CANDIDATE SCREENING")
        print("-" * 30)

        # Step 1: Load job requirements
        print("Step 1: Load job requirements")
        job_files = list(Path("job_requirements").glob("*.json"))

        if not job_files:
            print("❌ No job requirement files found!")
            print("Please create a job requirement template first (Option 1)")
            return

        print("Available job requirement files:")
        for i, file in enumerate(job_files, 1):
            print(f"  {i}. {file.name}")

        try:
            file_choice = int(input(f"Select job file (1-{len(job_files)}): ")) - 1
            job_file = job_files[file_choice]
        except (ValueError, IndexError):
            print("❌ Invalid selection")
            return

        job_data = self.load_job_requirements(job_file)
        if not job_data:
            return

        # Step 2: Load candidates
        print(f"\nStep 2: Load candidate CVs")
        folder_path = input("Enter path to candidate CV folder (or press Enter for 'candidate_uploads'): ").strip()
        if not folder_path:
            folder_path = "candidate_uploads"

        candidates = self.process_candidate_folder(folder_path)
        if not candidates:
            print("❌ No candidates found or processed")
            return

        # Step 3: Screen candidates
        print(f"\nStep 3: Screening candidates...")
        screening_results = self.screen_candidates(job_data, candidates)

        # Step 4: Generate report
        print(f"\nStep 4: Generating HR report...")
        report, csv_file, json_file = self.generate_hr_report(job_data, screening_results)

        # Step 5: Display summary
        self.display_screening_summary(report)

        # Ask if HR wants to see detailed candidate info
        if screening_results:
            show_details = input(f"\nWould you like to see detailed candidate information? (y/n): ").lower()
            if show_details == 'y':
                self.show_detailed_candidates(screening_results[:5])

    def show_detailed_candidates(self, candidates):
        """
        Hiển thị thông tin chi tiết của candidates
        """
        for i, candidate in enumerate(candidates, 1):
            print(f"\n{'='*60}")
            print(f"CANDIDATE #{i}: {candidate['name']}")
            print(f"{'='*60}")
            print(f"📧 Email: {candidate['email']}")
            print(f"📱 Phone: {candidate['phone']}")
            print(f"📄 CV File: {candidate['file_name']}")
            print(f"🎯 Skills: {', '.join(candidate['skills'])}")
            print(f"💼 Experience: {candidate['experience']} years")
            print(f"🎓 Education Level: {candidate['education']}")
            print(f"📍 Location: {candidate['location']}")

            print(f"\n🤖 AI ASSESSMENT:")
            print(f"   Prediction: {candidate['prediction_label']}")
            print(f"   Confidence: {candidate['confidence']:.1%}")
            print(f"   Probabilities:")
            for label, prob in candidate['probabilities'].items():
                print(f"     • {label}: {prob:.1%}")

            print(f"\n✅ MATCH REASONS:")
            for reason in candidate['match_reasons']:
                print(f"   {reason}")

            if candidate['red_flags']:
                print(f"\n⚠️ RED FLAGS:")
                for flag in candidate['red_flags']:
                    print(f"   {flag}")

            print(f"\n📊 DETAILED SCORES:")
            features = candidate['features']
            print(f"   • Skill Similarity: {features['skill_similarity']:.1%}")
            print(f"   • Experience Match: {features['experience_match']:.1%}")
            print(f"   • Education Match: {features['education_match']:.1%}")
            print(f"   • Location Compatibility: {features['location_compatibility']:.1%}")

            if i < len(candidates):
                input("\nPress Enter to see next candidate...")

    def view_previous_results(self):
        """
        Xem kết quả screening trước đó
        """
        results_files = list(Path(self.results_folder).glob("*_hr_report_*.json"))

        if not results_files:
            print("❌ No previous screening results found")
            return

        print(f"\n📁 PREVIOUS SCREENING RESULTS:")
        for i, file in enumerate(results_files, 1):
            # Extract info from filename
            parts = file.stem.split('_')
            job_id = parts[0]
            date_time = '_'.join(parts[-2:])
            print(f"  {i}. {job_id} - {date_time}")

        try:
            choice = int(input(f"Select report to view (1-{len(results_files)}): ")) - 1
            selected_file = results_files[choice]
        except (ValueError, IndexError):
            print("❌ Invalid selection")
            return

        # Load and display report
        with open(selected_file, 'r', encoding='utf-8') as f:
            report = json.load(f)

        self.display_screening_summary(report)


def main():
    """
    Main function để chạy HR Recruitment System
    """
    system = HRRecruitmentSystem()
    system.run_interactive_screening()


if __name__ == "__main__":
    main()
