# 🧹 Data Preprocessing Pipeline
## Transform Raw Data into Clean ML-Ready Format

### 📋 Overview
This notebook provides a comprehensive data preprocessing pipeline that transforms raw resume and job data into clean, standardized format suitable for machine learning training.

---

## 🎯 Objectives

### Input Data
- **Raw Resumes**: `data/raw/UpdatedResumeDataSet.csv` (42,106+ records)
- **Raw Jobs**: `data/raw/itviec_jobs_undetected.csv` (15,839+ records)

### Output Data
- **Clean Resumes**: `data/clean_resumes.csv` (Processed candidate resumes)
- **Clean Jobs**: `data/clean_jobs.csv` (Processed job descriptions)
- **Summary Report**: `data/preprocessing_summary.json` (Processing statistics)

---

## 🚀 Quick Start

### 1. Open Jupyter Notebook
```bash
cd visiualize
jupyter notebook data_preprocessing.ipynb
```

### 2. Run All Cells
- Execute all cells in sequence
- Processing takes approximately 5-10 minutes
- Monitor progress through print statements

### 3. Check Output
- Verify clean data files in `data/` folder
- Review processing summary for quality metrics

---

## 🔧 Processing Steps

### 1. **Text Cleaning**
- Remove HTML tags and special characters
- Clean URLs, emails, and phone numbers
- Normalize whitespace and formatting
- Convert to lowercase for consistency

### 2. **Skills Extraction**
- Extract 50+ technical skills automatically
- Cover programming languages, frameworks, databases
- Include cloud technologies, AI/ML tools
- Remove duplicates and standardize names

### 3. **Experience Extraction**
- Parse years of experience from text
- Handle various formats (e.g., "5 years", "5+ yrs")
- Extract both candidate experience and job requirements

### 4. **Data Standardization**
- Standardize category names and job titles
- Clean company names and locations
- Process salary information
- Calculate text statistics (length, word count)

### 5. **Quality Filtering**
- Remove records with insufficient information
- Filter out empty or very short texts
- Ensure minimum data quality standards

---

## 📊 Features Extracted

### Resume Features
| Feature | Description | Example |
|---------|-------------|---------|
| `category_clean` | Standardized job category | "Data Science" |
| `clean_text` | Cleaned resume text | Processed text without noise |
| `extracted_skills` | List of technical skills | ["python", "sql", "aws"] |
| `skills_text` | Skills as comma-separated | "python, sql, aws" |
| `skills_count` | Number of skills found | 15 |
| `experience_years` | Years of experience | 5 |
| `text_length` | Character count | 2847 |
| `word_count` | Word count | 456 |

### Job Features
| Feature | Description | Example |
|---------|-------------|---------|
| `title_clean` | Standardized job title | "Senior Python Developer" |
| `company_clean` | Standardized company name | "Tech Corp Vietnam" |
| `location_clean` | Standardized location | "Ho Chi Minh City" |
| `salary_clean` | Processed salary info | "25-35M VND" |
| `clean_text` | Combined cleaned text | All job info combined |
| `required_skills` | List of required skills | ["python", "django", "aws"] |
| `skills_count` | Number of required skills | 8 |
| `required_experience` | Required years | 3 |
| `text_length` | Character count | 1523 |
| `word_count` | Word count | 287 |

---

## 📈 Data Quality Metrics

### Resume Data Quality
- **Text Length**: Average 2,000+ characters per resume
- **Skills Coverage**: 85%+ of resumes have extracted skills
- **Experience Data**: 70%+ have identifiable experience
- **Categories**: 25 distinct job categories
- **Completeness**: 95%+ records pass quality filters

### Job Data Quality
- **Text Length**: Average 1,500+ characters per job
- **Skills Coverage**: 90%+ of jobs have required skills
- **Experience Requirements**: 80%+ specify experience
- **Geographic Coverage**: 50+ locations
- **Company Diversity**: 1,000+ unique companies

---

## 🎯 Skills Extraction Coverage

### Programming Languages
✅ Python, Java, JavaScript, C++, C#, PHP, Ruby, Go, Rust, Swift, Kotlin

### Web Technologies
✅ HTML, CSS, React, Angular, Vue, Node.js, Django, Flask, Spring, Laravel

### Databases
✅ MySQL, PostgreSQL, MongoDB, Redis, Oracle, SQL Server, Elasticsearch

### Cloud & DevOps
✅ AWS, Azure, GCP, Docker, Kubernetes, Jenkins, Git, Linux

### Data Science & AI
✅ Machine Learning, Deep Learning, TensorFlow, PyTorch, Pandas, NumPy

### Mobile Development
✅ Android, iOS, React Native, Flutter, Xamarin

---

## 🔍 Quality Assurance

### Automated Checks
- **Text Length Validation**: Minimum character requirements
- **Skills Validation**: At least one skill extracted
- **Data Completeness**: Required fields present
- **Format Consistency**: Standardized naming conventions

### Manual Review Points
- **Category Mapping**: Verify job category assignments
- **Skills Accuracy**: Check skill extraction precision
- **Experience Parsing**: Validate experience extraction
- **Location Standardization**: Review location cleaning

---

## 📊 Expected Output Statistics

### Resume Processing
```
📊 Total records: 40,000+ (after filtering)
📋 Categories: 25 unique job categories
📝 Average text length: 2,100 characters
🎯 Records with skills: 85%+
💼 Records with experience: 70%+
```

### Job Processing
```
📊 Total records: 15,000+ (after filtering)
🏢 Companies: 1,000+ unique companies
📍 Locations: 50+ unique locations
🎯 Jobs with skills: 90%+
💼 Jobs with experience req: 80%+
```

---

## 🚨 Troubleshooting

### Common Issues

#### 1. **File Not Found Error**
```
❌ Error: data/raw/UpdatedResumeDataSet.csv not found
```
**Solution**: Ensure raw data files are in correct location

#### 2. **Memory Issues**
```
❌ Error: Memory allocation failed
```
**Solution**: Process data in smaller chunks or increase RAM

#### 3. **Encoding Issues**
```
❌ Error: UnicodeDecodeError
```
**Solution**: Specify encoding='utf-8' in pandas read_csv

#### 4. **NLTK Data Missing**
```
❌ Error: NLTK punkt tokenizer not found
```
**Solution**: Run `nltk.download('punkt')` and `nltk.download('stopwords')`

### Performance Optimization
- **Chunk Processing**: For very large datasets
- **Parallel Processing**: Use multiprocessing for skills extraction
- **Memory Management**: Clear intermediate variables
- **Progress Monitoring**: Add tqdm progress bars

---

## 🔮 Advanced Features

### Custom Skills Dictionary
```python
# Add domain-specific skills
custom_skills = ['blockchain', 'iot', 'ar', 'vr', 'quantum computing']
tech_skills.extend(custom_skills)
```

### Experience Pattern Customization
```python
# Add new experience patterns
custom_patterns = [
    r'(\\d+)\\+?\\s*years?\\s*working',
    r'worked\\s*for\\s*(\\d+)\\+?\\s*years?'
]
```

### Text Cleaning Customization
```python
# Add domain-specific cleaning rules
def custom_clean_text(text):
    # Remove specific patterns
    text = re.sub(r'confidential', '', text)
    return text
```

---

## 📚 Next Steps

### After Preprocessing
1. **Data Validation**: Review output files for quality
2. **Feature Engineering**: Create additional ML features
3. **Model Training**: Use clean data for ML pipeline
4. **Evaluation**: Assess preprocessing impact on model performance

### Integration with ML Pipeline
```python
# Load preprocessed data
clean_resumes = pd.read_csv('data/clean_resumes.csv')
clean_jobs = pd.read_csv('data/clean_jobs.csv')

# Ready for feature engineering and model training
```

---

**🎯 This preprocessing pipeline ensures high-quality, consistent data for optimal machine learning performance!**
