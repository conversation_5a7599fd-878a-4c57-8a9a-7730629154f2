{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 🎯 Create Training Pairs from Clean Data\n", "## Generate Job-Resume Matching Pairs for ML Training\n", "\n", "**Objective**: Transform clean resume and job data into labeled training pairs\n", "\n", "**Input**: \n", "- `data/clean_resumes.csv` (Processed candidate resumes)\n", "- `data/clean_jobs.csv` (Processed job descriptions)\n", "\n", "**Output**: \n", "- `data/job_resume_pairs.csv` (Labeled training pairs)\n", "- 4 clusters: Highly Suitable (2), Moderately Suitable (1), Not Suitable (0), Mismatch (-1)"]}, {"cell_type": "markdown", "id": "setup", "metadata": {}, "source": ["## 🔧 Setup and Configuration"]}, {"cell_type": "code", "execution_count": 1, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Libraries imported successfully\n", "📅 Processing started at: 2025-06-17 22:14:19\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import random\n", "from datetime import datetime\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seed for reproducibility\n", "random.seed(42)\n", "np.random.seed(42)\n", "\n", "print(\"✅ Libraries imported successfully\")\n", "print(f\"📅 Processing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "id": "load-data", "metadata": {}, "source": ["## 📂 Load Clean Data"]}, {"cell_type": "code", "execution_count": 2, "id": "load-clean-data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Loading clean datasets...\n", "✅ Loaded 962 clean resume records\n", "✅ Loaded 995 clean job records\n", "\n", "📋 Resume Data Info:\n", "Categories: 25 unique\n", "Average skills per resume: 7.0\n", "Average experience: 0.4 years\n", "\n", "📋 Job Data Info:\n", "Companies: 510 unique\n", "Locations: 20 unique\n", "Average required skills: 7.3\n", "Average required experience: 0.4 years\n", "\n", "📊 Top Resume Categories:\n", "category_clean\n", "Java Developer        84\n", "Testing               70\n", "Devops Engineer       55\n", "Python Developer      48\n", "Web Designing         45\n", "Hr                    44\n", "<PERSON><PERSON>                42\n", "Blockchain            40\n", "Etl Developer         40\n", "Operations Manager    40\n", "Name: count, dtype: int64\n"]}], "source": ["# Load clean datasets\n", "print(\"📊 Loading clean datasets...\")\n", "\n", "try:\n", "    # Load clean resume data\n", "    clean_resumes = pd.read_csv('../data/clean_resumes.csv')\n", "    print(f\"✅ Loaded {len(clean_resumes):,} clean resume records\")\n", "    \n", "    # Load clean job data\n", "    clean_jobs = pd.read_csv('../data/clean_jobs.csv')\n", "    print(f\"✅ Loaded {len(clean_jobs):,} clean job records\")\n", "    \n", "except FileNotFoundError as e:\n", "    print(f\"❌ Error loading data: {e}\")\n", "    print(\"Please run data_preprocessing.ipynb first to generate clean data\")\n", "\n", "# Display data structure\n", "print(\"\\n📋 Resume Data Info:\")\n", "print(f\"Categories: {clean_resumes['category_clean'].nunique()} unique\")\n", "print(f\"Average skills per resume: {clean_resumes['skills_count'].mean():.1f}\")\n", "print(f\"Average experience: {clean_resumes['experience_years'].mean():.1f} years\")\n", "\n", "print(\"\\n📋 Job Data Info:\")\n", "print(f\"Companies: {clean_jobs['company_clean'].nunique()} unique\")\n", "print(f\"Locations: {clean_jobs['location_clean'].nunique()} unique\")\n", "print(f\"Average required skills: {clean_jobs['skills_count'].mean():.1f}\")\n", "print(f\"Average required experience: {clean_jobs['required_experience'].mean():.1f} years\")\n", "\n", "# Show category distribution\n", "print(\"\\n📊 Top Resume Categories:\")\n", "print(clean_resumes['category_clean'].value_counts().head(10))"]}, {"cell_type": "markdown", "id": "matching-logic", "metadata": {}, "source": ["## 🎯 Matching Logic and Scoring"]}, {"cell_type": "code", "execution_count": 3, "id": "matching-functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Matching functions defined\n"]}], "source": ["# Matching functions\n", "def calculate_skill_similarity(resume_skills, job_skills):\n", "    \"\"\"\n", "    Calculate skill similarity between resume and job\n", "    \"\"\"\n", "    if not resume_skills or not job_skills:\n", "        return 0.0\n", "    \n", "    # Convert to sets for intersection/union operations\n", "    resume_set = set(eval(resume_skills) if isinstance(resume_skills, str) else resume_skills)\n", "    job_set = set(eval(job_skills) if isinstance(job_skills, str) else job_skills)\n", "    \n", "    if not resume_set or not job_set:\n", "        return 0.0\n", "    \n", "    # <PERSON><PERSON><PERSON> similarity\n", "    intersection = len(resume_set.intersection(job_set))\n", "    union = len(resume_set.union(job_set))\n", "    \n", "    return intersection / union if union > 0 else 0.0\n", "\n", "def calculate_experience_match(resume_exp, job_exp):\n", "    \"\"\"\n", "    Calculate experience compatibility\n", "    \"\"\"\n", "    if job_exp == 0:\n", "        return 1.0  # No experience requirement\n", "    \n", "    if resume_exp >= job_exp:\n", "        return 1.0  # Meets or exceeds requirement\n", "    else:\n", "        return resume_exp / job_exp  # Partial match\n", "\n", "def calculate_category_match(resume_category, job_title):\n", "    \"\"\"\n", "    Calculate category/title compatibility\n", "    \"\"\"\n", "    if pd.isna(resume_category) or pd.isna(job_title):\n", "        return 0.0\n", "    \n", "    resume_cat = str(resume_category).lower()\n", "    job_title_lower = str(job_title).lower()\n", "    \n", "    # Define category mappings\n", "    category_mappings = {\n", "        'data science': ['data scientist', 'data analyst', 'machine learning', 'ai', 'analytics'],\n", "        'web development': ['web developer', 'frontend', 'backend', 'full stack', 'react', 'angular'],\n", "        'python developer': ['python', 'django', 'flask', 'backend'],\n", "        'java developer': ['java', 'spring', 'backend', 'enterprise'],\n", "        'devops engineer': ['devops', 'infrastructure', 'cloud', 'aws', 'docker', 'kubernetes'],\n", "        'mobile app development': ['mobile', 'android', 'ios', 'react native', 'flutter'],\n", "        'database': ['database', 'dba', 'sql', 'mysql', 'postgresql'],\n", "        'network security engineer': ['security', 'cybersecurity', 'network', 'penetration'],\n", "        'testing': ['qa', 'test', 'quality assurance', 'automation'],\n", "        'business analyst': ['business analyst', 'ba', 'requirements', 'analysis']\n", "    }\n", "    \n", "    # Check for exact category match\n", "    if resume_cat in job_title_lower:\n", "        return 1.0\n", "    \n", "    # Check for related keywords\n", "    if resume_cat in category_mappings:\n", "        for keyword in category_mappings[resume_cat]:\n", "            if keyword in job_title_lower:\n", "                return 0.8\n", "    \n", "    # Check reverse mapping\n", "    for category, keywords in category_mappings.items():\n", "        if any(keyword in resume_cat for keyword in keywords):\n", "            if category in job_title_lower or any(kw in job_title_lower for kw in keywords):\n", "                return 0.6\n", "    \n", "    return 0.0\n", "\n", "def calculate_text_similarity(resume_text, job_text):\n", "    \"\"\"\n", "    Calculate text similarity using TF-IDF\n", "    \"\"\"\n", "    if pd.isna(resume_text) or pd.isna(job_text) or resume_text == '' or job_text == '':\n", "        return 0.0\n", "    \n", "    try:\n", "        vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')\n", "        tfidf_matrix = vectorizer.fit_transform([str(resume_text), str(job_text)])\n", "        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]\n", "        return similarity\n", "    except:\n", "        return 0.0\n", "\n", "def calculate_overall_score(skill_sim, exp_match, cat_match, text_sim):\n", "    \"\"\"\n", "    Calculate overall matching score\n", "    \"\"\"\n", "    # Weighted combination\n", "    weights = {\n", "        'skills': 0.4,\n", "        'experience': 0.25,\n", "        'category': 0.25,\n", "        'text': 0.1\n", "    }\n", "    \n", "    overall = (\n", "        skill_sim * weights['skills'] +\n", "        exp_match * weights['experience'] +\n", "        cat_match * weights['category'] +\n", "        text_sim * weights['text']\n", "    )\n", "    \n", "    return overall\n", "\n", "def assign_label(overall_score, skill_sim, exp_match, cat_match):\n", "    \"\"\"\n", "    Assign suitability label based on scores\n", "    \"\"\"\n", "    # Highly Suitable (2): High overall score with good skill match\n", "    if overall_score >= 0.7 and skill_sim >= 0.5 and exp_match >= 0.8:\n", "        return 2\n", "    \n", "    # Moderately Suitable (1): Decent match with some compatibility\n", "    elif overall_score >= 0.4 and (skill_sim >= 0.3 or cat_match >= 0.6):\n", "        return 1\n", "    \n", "    # Not Suitable (0): Low compatibility\n", "    elif overall_score >= 0.1:\n", "        return 0\n", "    \n", "    # Complete Mismatch (-1): Very poor match\n", "    else:\n", "        return -1\n", "\n", "print(\"✅ Matching functions defined\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}