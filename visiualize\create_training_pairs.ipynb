# Import required libraries
import pandas as pd
import numpy as np
import random
from datetime import datetime
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
random.seed(42)
np.random.seed(42)

print("✅ Libraries imported successfully")
print(f"📅 Processing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Load clean datasets
print("📊 Loading clean datasets...")

try:
    # Load clean resume data
    clean_resumes = pd.read_csv('../data/clean_resumes.csv')
    print(f"✅ Loaded {len(clean_resumes):,} clean resume records")
    
    # Load clean job data
    clean_jobs = pd.read_csv('../data/clean_jobs.csv')
    print(f"✅ Loaded {len(clean_jobs):,} clean job records")
    
except FileNotFoundError as e:
    print(f"❌ Error loading data: {e}")
    print("Please run data_preprocessing.ipynb first to generate clean data")

# Display data structure
print("\n📋 Resume Data Info:")
print(f"Categories: {clean_resumes['category_clean'].nunique()} unique")
print(f"Average skills per resume: {clean_resumes['skills_count'].mean():.1f}")
print(f"Average experience: {clean_resumes['experience_years'].mean():.1f} years")

print("\n📋 Job Data Info:")
print(f"Companies: {clean_jobs['company_clean'].nunique()} unique")
print(f"Locations: {clean_jobs['location_clean'].nunique()} unique")
print(f"Average required skills: {clean_jobs['skills_count'].mean():.1f}")
print(f"Average required experience: {clean_jobs['required_experience'].mean():.1f} years")

# Show category distribution
print("\n📊 Top Resume Categories:")
print(clean_resumes['category_clean'].value_counts().head(10))

# Matching functions
def calculate_skill_similarity(resume_skills, job_skills):
    """
    Calculate skill similarity between resume and job
    """
    if not resume_skills or not job_skills:
        return 0.0
    
    # Convert to sets for intersection/union operations
    resume_set = set(eval(resume_skills) if isinstance(resume_skills, str) else resume_skills)
    job_set = set(eval(job_skills) if isinstance(job_skills, str) else job_skills)
    
    if not resume_set or not job_set:
        return 0.0
    
    # Jaccard similarity
    intersection = len(resume_set.intersection(job_set))
    union = len(resume_set.union(job_set))
    
    return intersection / union if union > 0 else 0.0

def calculate_experience_match(resume_exp, job_exp):
    """
    Calculate experience compatibility
    """
    if job_exp == 0:
        return 1.0  # No experience requirement
    
    if resume_exp >= job_exp:
        return 1.0  # Meets or exceeds requirement
    else:
        return resume_exp / job_exp  # Partial match

def calculate_category_match(resume_category, job_title):
    """
    Calculate category/title compatibility
    """
    if pd.isna(resume_category) or pd.isna(job_title):
        return 0.0
    
    resume_cat = str(resume_category).lower()
    job_title_lower = str(job_title).lower()
    
    # Define category mappings
    category_mappings = {
        'data science': ['data scientist', 'data analyst', 'machine learning', 'ai', 'analytics'],
        'web development': ['web developer', 'frontend', 'backend', 'full stack', 'react', 'angular'],
        'python developer': ['python', 'django', 'flask', 'backend'],
        'java developer': ['java', 'spring', 'backend', 'enterprise'],
        'devops engineer': ['devops', 'infrastructure', 'cloud', 'aws', 'docker', 'kubernetes'],
        'mobile app development': ['mobile', 'android', 'ios', 'react native', 'flutter'],
        'database': ['database', 'dba', 'sql', 'mysql', 'postgresql'],
        'network security engineer': ['security', 'cybersecurity', 'network', 'penetration'],
        'testing': ['qa', 'test', 'quality assurance', 'automation'],
        'business analyst': ['business analyst', 'ba', 'requirements', 'analysis']
    }
    
    # Check for exact category match
    if resume_cat in job_title_lower:
        return 1.0
    
    # Check for related keywords
    if resume_cat in category_mappings:
        for keyword in category_mappings[resume_cat]:
            if keyword in job_title_lower:
                return 0.8
    
    # Check reverse mapping
    for category, keywords in category_mappings.items():
        if any(keyword in resume_cat for keyword in keywords):
            if category in job_title_lower or any(kw in job_title_lower for kw in keywords):
                return 0.6
    
    return 0.0

def calculate_text_similarity(resume_text, job_text):
    """
    Calculate text similarity using TF-IDF
    """
    if pd.isna(resume_text) or pd.isna(job_text) or resume_text == '' or job_text == '':
        return 0.0
    
    try:
        vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        tfidf_matrix = vectorizer.fit_transform([str(resume_text), str(job_text)])
        similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        return similarity
    except:
        return 0.0

def calculate_overall_score(skill_sim, exp_match, cat_match, text_sim):
    """
    Calculate overall matching score
    """
    # Weighted combination
    weights = {
        'skills': 0.4,
        'experience': 0.25,
        'category': 0.25,
        'text': 0.1
    }
    
    overall = (
        skill_sim * weights['skills'] +
        exp_match * weights['experience'] +
        cat_match * weights['category'] +
        text_sim * weights['text']
    )
    
    return overall

def assign_label(overall_score, skill_sim, exp_match, cat_match):
    """
    Assign suitability label based on scores
    """
    # Highly Suitable (2): High overall score with good skill match
    if overall_score >= 0.7 and skill_sim >= 0.5 and exp_match >= 0.8:
        return 2
    
    # Moderately Suitable (1): Decent match with some compatibility
    elif overall_score >= 0.4 and (skill_sim >= 0.3 or cat_match >= 0.6):
        return 1
    
    # Not Suitable (0): Low compatibility
    elif overall_score >= 0.1:
        return 0
    
    # Complete Mismatch (-1): Very poor match
    else:
        return -1

print("✅ Matching functions defined")