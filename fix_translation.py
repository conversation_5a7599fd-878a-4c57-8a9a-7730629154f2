#!/usr/bin/env python3
"""
Fix Translation Issues
Simple solution for Vietnamese to English translation
"""

import pandas as pd
import time
import random
from deep_translator import GoogleTranslator
from langdetect import detect

def simple_translate_vi_to_en(text):
    """
    Simple Vietnamese to English translation using basic mapping
    For production, use proper translation service
    """
    if not text or pd.isna(text) or len(str(text).strip()) == 0:
        return text
    
    # Basic Vietnamese to English mapping for common job terms
    vi_en_mapping = {
        # Job titles
        'kỹ sư': 'engineer',
        'lập trình viên': 'programmer',
        'phát triển': 'development',
        'quản lý': 'manager',
        'gi<PERSON>m đốc': 'director',
        'nhân viên': 'employee',
        'chuyên viên': 'specialist',
        'trưởng phòng': 'department head',
        'phó giám đốc': 'deputy director',
        'tư vấn': 'consultant',
        
        # Skills
        'lập trình': 'programming',
        'phần mềm': 'software',
        'ứng dụng': 'application',
        'website': 'website',
        'cơ sở dữ liệu': 'database',
        'mạng': 'network',
        'bảo mật': 'security',
        'kiểm thử': 'testing',
        'thiết kế': 'design',
        'phân tích': 'analysis',
        
        # Experience
        'kinh nghiệm': 'experience',
        'năm': 'years',
        'tháng': 'months',
        'làm việc': 'work',
        'dự án': 'project',
        'nhóm': 'team',
        'khách hàng': 'customer',
        'công ty': 'company',
        
        # Requirements
        'yêu cầu': 'requirements',
        'bằng cấp': 'degree',
        'chứng chỉ': 'certificate',
        'kỹ năng': 'skills',
        'tiếng anh': 'english',
        'giao tiếp': 'communication',
        'làm việc nhóm': 'teamwork',
        'giải quyết vấn đề': 'problem solving',
        
        # Common words
        'và': 'and',
        'hoặc': 'or',
        'với': 'with',
        'trong': 'in',
        'cho': 'for',
        'của': 'of',
        'có': 'have',
        'được': 'be',
        'sẽ': 'will',
        'cần': 'need',
        'phải': 'must',
        'tốt': 'good',
        'cao': 'high',
        'mới': 'new',
        'cũ': 'old'
    }
    
    text_lower = str(text).lower()
    
    # Replace Vietnamese terms with English
    for vi_term, en_term in vi_en_mapping.items():
        text_lower = text_lower.replace(vi_term, en_term)
    
    return text_lower

def detect_language_simple(text):
    """Simple language detection"""
    if not text or pd.isna(text):
        return 'unknown'
    
    text_str = str(text).lower()
    
    # Vietnamese indicators
    vietnamese_chars = ['ă', 'â', 'đ', 'ê', 'ô', 'ơ', 'ư', 'à', 'á', 'ả', 'ã', 'ạ']
    vietnamese_words = ['của', 'và', 'trong', 'với', 'cho', 'được', 'có', 'là', 'một', 'này']
    
    # Check for Vietnamese characters
    if any(char in text_str for char in vietnamese_chars):
        return 'vi'
    
    # Check for Vietnamese words
    if any(word in text_str for word in vietnamese_words):
        return 'vi'
    
    return 'en'

def proper_translate(text, lang):
    """Proper translation using deep-translator"""
    if not text or pd.isna(text) or len(str(text).strip()) == 0:
        return text

    if lang == 'vi':
        try:
            # Use deep-translator for proper translation
            translator = GoogleTranslator(source='vi', target='en')
            result = translator.translate(str(text))
            return result
        except Exception as e:
            print(f"Deep translator error: {e}")
            # Fallback to simple translation
            return simple_translate_vi_to_en(text)

    return text

def safe_translate(text, lang):
    """Safe translation with error handling"""
    try:
        if lang == 'vi':
            return proper_translate(text, lang)
        return text
    except Exception as e:
        print(f"Translation error: {e}")
        return text

def detect_language_proper(text):
    """Proper language detection using langdetect"""
    try:
        if not text or pd.isna(text) or len(str(text).strip()) < 3:
            return 'unknown'

        detected = detect(str(text))
        return detected
    except:
        # Fallback to simple detection
        return detect_language_simple(text)

def fix_translation_in_notebook():
    """Fix translation issues in the notebook data"""
    
    print("🔧 FIXING TRANSLATION ISSUES")
    print("=" * 40)
    
    # Try to load the data that was being processed
    try:
        # Look for the data file
        possible_files = [
            'data/Job_Description.csv',
            'data/job_descriptions.csv',
            'progress/job_data.csv'
        ]
        
        df = None
        for file_path in possible_files:
            try:
                df = pd.read_csv(file_path)
                print(f"✅ Loaded data from: {file_path}")
                print(f"   Shape: {df.shape}")
                break
            except FileNotFoundError:
                continue
        
        if df is None:
            print("❌ No data file found")
            return
        
        # Show columns
        print(f"📋 Columns: {list(df.columns)}")
        
        # Detect language for text columns
        text_columns = ['description', 'requirements', 'title']
        
        for col in text_columns:
            if col in df.columns:
                print(f"\n🔍 Processing column: {col}")
                
                # Detect language
                lang_col = f'{col}_language'
                df[lang_col] = df[col].apply(detect_language_proper)
                
                # Show language distribution
                lang_dist = df[lang_col].value_counts()
                print(f"   Language distribution: {dict(lang_dist)}")
                
                # Translate Vietnamese to English
                en_col = f'{col}_en'
                df[en_col] = df.apply(lambda x: safe_translate(x[col], x[lang_col]), axis=1)
                
                # Show sample
                vi_samples = df[df[lang_col] == 'vi'][col].head(3)
                if len(vi_samples) > 0:
                    print(f"   Sample translations:")
                    for idx, (original, translated) in enumerate(zip(vi_samples, df.loc[vi_samples.index, en_col])):
                        print(f"     {idx+1}. VI: {str(original)[:50]}...")
                        print(f"        EN: {str(translated)[:50]}...")
        
        # Export fixed data
        output_file = 'data/job_descriptions_translated.csv'
        df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"\n✅ Exported translated data to: {output_file}")
        
        # Summary
        print(f"\n📊 SUMMARY:")
        print(f"   Total records: {len(df):,}")
        print(f"   Columns processed: {[col for col in text_columns if col in df.columns]}")
        print(f"   Output file: {output_file}")
        
        return df
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def install_proper_translator():
    """Install proper translation library"""
    
    print("📦 INSTALLING PROPER TRANSLATION LIBRARY")
    print("=" * 45)
    
    commands = [
        "pip uninstall googletrans -y",
        "pip install googletrans==4.0.0rc1",
        "pip install deep-translator"
    ]
    
    print("Run these commands:")
    for cmd in commands:
        print(f"   {cmd}")
    
    print("\nAlternatively, use this simple translation fix above.")

def main():
    """Main function"""
    
    print("🛠️ TRANSLATION FIX UTILITY")
    print("=" * 30)
    
    # Option 1: Fix with simple translation
    print("\n1. Trying simple translation fix...")
    df = fix_translation_in_notebook()
    
    if df is not None:
        print("✅ Translation fix completed!")
    else:
        print("❌ Simple fix failed")
        
        # Option 2: Show installation instructions
        print("\n2. Installation instructions for proper translator:")
        install_proper_translator()

if __name__ == "__main__":
    main()
