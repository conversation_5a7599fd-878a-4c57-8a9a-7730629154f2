#!/usr/bin/env python3
"""
Test script to run training and save ROC curves as images
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run training and check generated files"""
    print("🎯 TESTING IMPROVED TRAINING WITH ROC CURVE SAVING")
    print("=" * 60)
    
    # Check if data file exists
    data_file = Path("progress/csv/jd_cr_similarity.csv")
    if not data_file.exists():
        print(f"❌ Data file not found: {data_file}")
        print("Please make sure the CSV file exists.")
        return
    
    print(f"✅ Data file found: {data_file}")
    print(f"📊 Starting improved training with 4 models:")
    print("   - Regularized Regression (Ridge/Lasso/ElasticNet)")
    print("   - Improved Decision Tree") 
    print("   - Improved AdaBoost")
    print("   - Improved XGBoost")
    print("\n🎨 Will generate and save ROC curves as images")
    print("=" * 60)
    
    try:
        # Run training
        result = subprocess.run([
            sys.executable, "four_models_training.py"
        ], capture_output=True, text=True, timeout=600)  # 10 minutes timeout
        
        if result.returncode == 0:
            print("✅ Training completed successfully!")
            
            # Show last part of output (results)
            output_lines = result.stdout.split('\n')
            print("\n📊 Training Results (last 30 lines):")
            print("-" * 50)
            for line in output_lines[-30:]:
                if line.strip():
                    print(line)
            
            # Check generated files
            print("\n📁 Checking Generated Files:")
            files_to_check = [
                ("improved_four_models_results.csv", "Model comparison results"),
                ("roc_curves_comparison.png", "Combined ROC curves"),
                ("individual_roc_curves.png", "Individual class ROC curves"),
                ("api/models/best_model.joblib", "Best trained model"),
                ("api/models/preprocessing.joblib", "Preprocessing components"),
                ("api/models/model_metadata.json", "Model metadata")
            ]
            
            all_files_exist = True
            for file_path, description in files_to_check:
                path = Path(file_path)
                if path.exists():
                    if path.suffix == '.png':
                        print(f"  ✅ {file_path} - {description} (Image file)")
                    elif path.suffix == '.csv':
                        lines = len(open(path).readlines())
                        print(f"  ✅ {file_path} - {description} ({lines} lines)")
                    elif path.suffix == '.joblib':
                        size = path.stat().st_size
                        print(f"  ✅ {file_path} - {description} ({size:,} bytes)")
                    elif path.suffix == '.json':
                        print(f"  ✅ {file_path} - {description} (JSON)")
                    else:
                        print(f"  ✅ {file_path} - {description}")
                else:
                    print(f"  ❌ {file_path} - {description} (NOT FOUND)")
                    all_files_exist = False
            
            if all_files_exist:
                print("\n🎉 All files generated successfully!")
                print("\n📈 ROC Curve Images:")
                print("  🖼️  roc_curves_comparison.png - Compare all models")
                print("  🖼️  individual_roc_curves.png - Each class separately")
                print("\n📊 Results:")
                print("  📄 improved_four_models_results.csv - Detailed metrics")
                print("\n🤖 API Ready:")
                print("  📁 api/models/ - Contains best model for deployment")
                
                # Show quick stats from CSV
                try:
                    import pandas as pd
                    results_df = pd.read_csv("improved_four_models_results.csv")
                    print(f"\n📈 Quick Model Comparison:")
                    print(f"{'Model':<25} {'Test Acc':<10} {'Overfitting':<12}")
                    print("-" * 50)
                    for _, row in results_df.iterrows():
                        model_name = row['Model'][:24]  # Truncate long names
                        test_acc = f"{row['Test_Accuracy']:.3f}"
                        overfitting = f"{row['Overfitting']:.3f}"
                        print(f"{model_name:<25} {test_acc:<10} {overfitting:<12}")
                except Exception as e:
                    print(f"⚠️  Could not read results CSV: {e}")
            else:
                print("\n⚠️  Some files are missing!")
            
        else:
            print("❌ Training failed!")
            print("Error output:")
            print(result.stderr)
            if result.stdout:
                print("Standard output:")
                print(result.stdout[-1000:])  # Last 1000 chars
            
    except subprocess.TimeoutExpired:
        print("❌ Training timeout (10 minutes)")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
