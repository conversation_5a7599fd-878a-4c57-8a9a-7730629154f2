{"cells": [{"cell_type": "markdown", "id": "title", "metadata": {}, "source": ["# 🎯 Generate Full Job-Candidate Pairs\n", "## Create Complete Dataset from Four Categories\n", "\n", "**Objective**: Generate comprehensive job-candidate matching pairs using extracted four categories\n", "\n", "**Input**: \n", "- `data/primary_skills.csv`\n", "- `data/secondary_skills.csv`\n", "- `data/adjectives.csv`\n", "- `data/adverbs.csv`\n", "\n", "**Output**: \n", "- `data/job_candidate_pairs_full.csv` (Complete dataset)\n", "- Multiple matching strategies and similarity calculations"]}, {"cell_type": "code", "execution_count": null, "id": "setup", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "from itertools import combinations\n", "import random\n", "from collections import defaultdict\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seed for reproducibility\n", "random.seed(42)\n", "np.random.seed(42)\n", "\n", "print(\"✅ Setup completed\")\n", "print(f\"📅 Processing started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "code", "execution_count": null, "id": "load-data", "metadata": {}, "outputs": [], "source": ["# Load four categories data\n", "print(\"📂 Loading four categories data...\")\n", "\n", "try:\n", "    # Load category datasets\n", "    primary_skills = pd.read_csv('../data/primary_skills.csv')\n", "    secondary_skills = pd.read_csv('../data/secondary_skills.csv')\n", "    adjectives = pd.read_csv('../data/adjectives.csv')\n", "    adverbs = pd.read_csv('../data/adverbs.csv')\n", "    \n", "    print(f\"✅ Loaded data:\")\n", "    print(f\"   Primary skills: {len(primary_skills):,} records\")\n", "    print(f\"   Secondary skills: {len(secondary_skills):,} records\")\n", "    print(f\"   Adjectives: {len(adjectives):,} records\")\n", "    print(f\"   Adverbs: {len(adverbs):,} records\")\n", "    \n", "except FileNotFoundError as e:\n", "    print(f\"❌ Error: {e}\")\n", "    print(\"Please run extract_four_categories_simple.ipynb first\")\n", "\n", "# Also load original clean data for additional info\n", "try:\n", "    clean_resumes = pd.read_csv('../data/clean_resumes.csv')\n", "    clean_jobs = pd.read_csv('../data/clean_jobs.csv')\n", "    print(f\"\\n✅ Also loaded:\")\n", "    print(f\"   Clean resumes: {len(clean_resumes):,} records\")\n", "    print(f\"   Clean jobs: {len(clean_jobs):,} records\")\n", "except FileNotFoundError:\n", "    print(\"⚠️ Clean data not found - will work with categories only\")"]}, {"cell_type": "code", "execution_count": null, "id": "analyze-data", "metadata": {}, "outputs": [], "source": ["# Analyze available data\n", "print(\"🔍 Analyzing available data...\")\n", "\n", "# Get unique job and resume IDs\n", "job_ids = set()\n", "resume_ids = set()\n", "\n", "for df in [primary_skills, secondary_skills, adjectives, adverbs]:\n", "    if len(df) > 0:\n", "        jobs = df[df['source_type'] == 'job']['record_id'].unique()\n", "        resumes = df[df['source_type'] == 'resume']['record_id'].unique()\n", "        job_ids.update(jobs)\n", "        resume_ids.update(resumes)\n", "\n", "print(f\"\\n📊 Available for matching:\")\n", "print(f\"   Unique job IDs: {len(job_ids):,}\")\n", "print(f\"   Unique resume IDs: {len(resume_ids):,}\")\n", "print(f\"   Potential pairs: {len(job_ids) * len(resume_ids):,}\")\n", "\n", "# Show sample IDs\n", "print(f\"\\n📋 Sample job IDs: {list(job_ids)[:5]}\")\n", "print(f\"📋 Sample resume IDs: {list(resume_ids)[:5]}\")"]}, {"cell_type": "code", "execution_count": null, "id": "create-feature-vectors", "metadata": {}, "outputs": [], "source": ["# Create feature vectors for each record\n", "print(\"🔄 Creating feature vectors...\")\n", "\n", "def create_feature_vector(record_id, source_type):\n", "    \"\"\"Create feature vector for a record\"\"\"\n", "    features = {\n", "        'record_id': record_id,\n", "        'source_type': source_type,\n", "        'primary_skills': [],\n", "        'secondary_skills': [],\n", "        'adjectives': [],\n", "        'adverbs': []\n", "    }\n", "    \n", "    # Extract primary skills\n", "    if len(primary_skills) > 0:\n", "        mask = (primary_skills['record_id'] == record_id) & (primary_skills['source_type'] == source_type)\n", "        skills = primary_skills[mask]['skill'].tolist()\n", "        features['primary_skills'] = skills\n", "    \n", "    # Extract secondary skills\n", "    if len(secondary_skills) > 0:\n", "        mask = (secondary_skills['record_id'] == record_id) & (secondary_skills['source_type'] == source_type)\n", "        skills = secondary_skills[mask]['skill'].tolist()\n", "        features['secondary_skills'] = skills\n", "    \n", "    # Extract adjectives\n", "    if len(adjectives) > 0 and 'adjective' in adjectives.columns:\n", "        mask = (adjectives['record_id'] == record_id) & (adjectives['source_type'] == source_type)\n", "        adjs = adjectives[mask]['adjective'].tolist()\n", "        features['adjectives'] = adjs\n", "    \n", "    # Extract adverbs\n", "    if len(adverbs) > 0 and 'adverb' in adverbs.columns:\n", "        mask = (adverbs['record_id'] == record_id) & (adverbs['source_type'] == source_type)\n", "        advs = adverbs[mask]['adverb'].tolist()\n", "        features['adverbs'] = advs\n", "    \n", "    return features\n", "\n", "# Create feature vectors for all jobs and resumes\n", "job_features = {}\n", "resume_features = {}\n", "\n", "print(\"   Creating job feature vectors...\")\n", "for job_id in list(job_ids)[:10]:  # Test with first 10 jobs\n", "    job_features[job_id] = create_feature_vector(job_id, 'job')\n", "\n", "print(\"   Creating resume feature vectors...\")\n", "for resume_id in list(resume_ids)[:20]:  # Test with first 20 resumes\n", "    resume_features[resume_id] = create_feature_vector(resume_id, 'resume')\n", "\n", "print(f\"✅ Created feature vectors:\")\n", "print(f\"   Jobs: {len(job_features)}\")\n", "print(f\"   Resumes: {len(resume_features)}\")\n", "\n", "# Show sample feature vector\n", "if job_features:\n", "    sample_job = list(job_features.keys())[0]\n", "    print(f\"\\n📋 Sample job features ({sample_job}):\")\n", "    for key, value in job_features[sample_job].items():\n", "        if isinstance(value, list):\n", "            print(f\"   {key}: {len(value)} items - {value[:3]}{'...' if len(value) > 3 else ''}\")\n", "        else:\n", "            print(f\"   {key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "id": "similarity-functions", "metadata": {}, "outputs": [], "source": ["# Similarity calculation functions\n", "def jaccard_similarity(set1, set2):\n", "    \"\"\"Calculate Jaccard similarity between two sets\"\"\"\n", "    if not set1 and not set2:\n", "        return 0.0\n", "    if not set1 or not set2:\n", "        return 0.0\n", "    \n", "    intersection = len(set1.intersection(set2))\n", "    union = len(set1.union(set2))\n", "    \n", "    return intersection / union if union > 0 else 0.0\n", "\n", "def calculate_similarity_scores(job_features, resume_features):\n", "    \"\"\"Calculate all similarity scores between job and resume\"\"\"\n", "    scores = {}\n", "    \n", "    # Primary skills similarity\n", "    job_primary = set(job_features['primary_skills'])\n", "    resume_primary = set(resume_features['primary_skills'])\n", "    scores['primary_skills_jaccard'] = jaccard_similarity(job_primary, resume_primary)\n", "    \n", "    # Secondary skills similarity\n", "    job_secondary = set(job_features['secondary_skills'])\n", "    resume_secondary = set(resume_features['secondary_skills'])\n", "    scores['secondary_skills_jaccard'] = jaccard_similarity(job_secondary, resume_secondary)\n", "    \n", "    # Adjectives similarity\n", "    job_adj = set(job_features['adjectives'])\n", "    resume_adj = set(resume_features['adjectives'])\n", "    scores['adjectives_jaccard'] = jaccard_similarity(job_adj, resume_adj)\n", "    \n", "    # Adverbs similarity\n", "    job_adv = set(job_features['adverbs'])\n", "    resume_adv = set(resume_features['adverbs'])\n", "    scores['adverbs_jaccard'] = jaccard_similarity(job_adv, resume_adv)\n", "    \n", "    # Overall suitability (weighted combination)\n", "    weights = {\n", "        'primary': 0.5,\n", "        'secondary': 0.25,\n", "        'adjectives': 0.15,\n", "        'adverbs': 0.1\n", "    }\n", "    \n", "    overall = (\n", "        scores['primary_skills_jaccard'] * weights['primary'] +\n", "        scores['secondary_skills_jaccard'] * weights['secondary'] +\n", "        scores['adjectives_j<PERSON><PERSON>'] * weights['adjectives'] +\n", "        scores['adverbs_jaccard'] * weights['adverbs']\n", "    )\n", "    \n", "    scores['overall_suitability'] = overall\n", "    \n", "    # Suitability label\n", "    if overall >= 0.7:\n", "        scores['suitability_label'] = 2  # Highly suitable\n", "    elif overall >= 0.4:\n", "        scores['suitability_label'] = 1  # Moderately suitable\n", "    else:\n", "        scores['suitability_label'] = 0  # Not suitable\n", "    \n", "    return scores\n", "\n", "print(\"✅ Similarity functions defined\")"]}, {"cell_type": "code", "execution_count": null, "id": "generate-pairs", "metadata": {}, "outputs": [], "source": ["# Generate job-candidate pairs\n", "print(\"🔄 Generating job-candidate pairs...\")\n", "\n", "all_pairs = []\n", "pair_id = 1\n", "\n", "total_combinations = len(job_features) * len(resume_features)\n", "print(f\"   Total combinations to process: {total_combinations:,}\")\n", "\n", "processed = 0\n", "for job_id, job_feat in job_features.items():\n", "    for resume_id, resume_feat in resume_features.items():\n", "        # Calculate similarity scores\n", "        scores = calculate_similarity_scores(job_feat, resume_feat)\n", "        \n", "        # Create pair record\n", "        pair = {\n", "            'pair_id': pair_id,\n", "            'job_id': job_id,\n", "            'candidate_id': resume_id,\n", "            'primary_skills_jaccard': round(scores['primary_skills_jaccard'], 3),\n", "            'secondary_skills_jaccard': round(scores['secondary_skills_jaccard'], 3),\n", "            'adjectives_jaccard': round(scores['adjectives_jaccard'], 3),\n", "            'adverbs_jaccard': round(scores['adverbs_jaccard'], 3),\n", "            'overall_suitability': round(scores['overall_suitability'], 3),\n", "            'suitability_label': scores['suitability_label']\n", "        }\n", "        \n", "        all_pairs.append(pair)\n", "        pair_id += 1\n", "        processed += 1\n", "        \n", "        # Progress update\n", "        if processed % 50 == 0:\n", "            print(f\"   Processed {processed}/{total_combinations} pairs ({processed/total_combinations*100:.1f}%)\")\n", "\n", "print(f\"\\n✅ Generated {len(all_pairs):,} job-candidate pairs\")\n", "\n", "# Create DataFrame\n", "pairs_df = pd.DataFrame(all_pairs)\n", "print(f\"✅ Created pairs DataFrame with {len(pairs_df):,} records\")\n", "\n", "# Show distribution\n", "print(f\"\\n📊 Suitability distribution:\")\n", "label_counts = pairs_df['suitability_label'].value_counts().sort_index()\n", "for label, count in label_counts.items():\n", "    label_name = {0: 'Not Suitable', 1: 'Moderately Suitable', 2: 'Highly Suitable'}[label]\n", "    print(f\"   {label} ({label_name}): {count:,} pairs ({count/len(pairs_df)*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "id": "analyze-results", "metadata": {}, "outputs": [], "source": ["# Analyze results\n", "print(\"📈 Analyzing results...\")\n", "\n", "# Basic statistics\n", "print(f\"\\n📊 SIMILARITY STATISTICS:\")\n", "similarity_cols = ['primary_skills_jaccard', 'secondary_skills_jaccard', 'adjectives_jaccard', 'adverbs_jaccard', 'overall_suitability']\n", "\n", "for col in similarity_cols:\n", "    mean_val = pairs_df[col].mean()\n", "    max_val = pairs_df[col].max()\n", "    min_val = pairs_df[col].min()\n", "    std_val = pairs_df[col].std()\n", "    print(f\"\\n{col.replace('_', ' ').title()}:\")\n", "    print(f\"   Mean: {mean_val:.3f}\")\n", "    print(f\"   Max: {max_val:.3f}\")\n", "    print(f\"   Min: {min_val:.3f}\")\n", "    print(f\"   Std: {std_val:.3f}\")\n", "\n", "# Top matches\n", "print(f\"\\n🔥 TOP 10 MATCHES:\")\n", "top_matches = pairs_df.nlargest(10, 'overall_suitability')\n", "for idx, row in top_matches.iterrows():\n", "    print(f\"   {row['job_id']} + {row['candidate_id']}: {row['overall_suitability']:.3f} (Label: {row['suitability_label']})\")\n", "\n", "# Distribution by score ranges\n", "print(f\"\\n📊 SCORE DISTRIBUTION:\")\n", "score_ranges = [\n", "    (0.0, 0.1, 'Very Low'),\n", "    (0.1, 0.3, 'Low'),\n", "    (0.3, 0.5, 'Medium'),\n", "    (0.5, 0.7, 'High'),\n", "    (0.7, 1.0, 'Very High')\n", "]\n", "\n", "for min_score, max_score, label in score_ranges:\n", "    count = len(pairs_df[(pairs_df['overall_suitability'] >= min_score) & (pairs_df['overall_suitability'] < max_score)])\n", "    percentage = count / len(pairs_df) * 100\n", "    print(f\"   {label} ({min_score}-{max_score}): {count:,} pairs ({percentage:.1f}%)\")\n", "\n", "print(\"\\n✅ Analysis completed\")"]}, {"cell_type": "code", "execution_count": null, "id": "export-results", "metadata": {}, "outputs": [], "source": ["# Export results\n", "print(\"💾 Exporting results...\")\n", "\n", "# Add metadata\n", "processing_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "pairs_df['created_date'] = processing_date\n", "\n", "# Export full dataset\n", "output_file = '../data/job_candidate_pairs_full.csv'\n", "pairs_df.to_csv(output_file, index=False, encoding='utf-8')\n", "print(f\"✅ Exported {len(pairs_df):,} pairs to: {output_file}\")\n", "\n", "# Export high-quality matches only\n", "high_quality = pairs_df[pairs_df['overall_suitability'] >= 0.3]\n", "if len(high_quality) > 0:\n", "    high_quality_file = '../data/job_candidate_pairs_high_quality.csv'\n", "    high_quality.to_csv(high_quality_file, index=False, encoding='utf-8')\n", "    print(f\"✅ Exported {len(high_quality):,} high-quality pairs to: {high_quality_file}\")\n", "\n", "# Export by suitability labels\n", "for label in [0, 1, 2]:\n", "    label_data = pairs_df[pairs_df['suitability_label'] == label]\n", "    if len(label_data) > 0:\n", "        label_names = {0: 'not_suitable', 1: 'moderately_suitable', 2: 'highly_suitable'}\n", "        label_file = f'../data/job_candidate_pairs_{label_names[label]}.csv'\n", "        label_data.to_csv(label_file, index=False, encoding='utf-8')\n", "        print(f\"✅ Exported {len(label_data):,} {label_names[label].replace('_', ' ')} pairs\")\n", "\n", "# Create summary report\n", "summary = {\n", "    'generation_info': {\n", "        'created_date': processing_date,\n", "        'total_pairs': len(pairs_df),\n", "        'unique_jobs': len(job_features),\n", "        'unique_candidates': len(resume_features)\n", "    },\n", "    'similarity_statistics': {\n", "        col: {\n", "            'mean': float(pairs_df[col].mean()),\n", "            'max': float(pairs_df[col].max()),\n", "            'min': float(pairs_df[col].min()),\n", "            'std': float(pairs_df[col].std())\n", "        } for col in similarity_cols\n", "    },\n", "    'suitability_distribution': {\n", "        int(label): int(count) for label, count in pairs_df['suitability_label'].value_counts().items()\n", "    },\n", "    'top_matches': [\n", "        {\n", "            'job_id': row['job_id'],\n", "            'candidate_id': row['candidate_id'],\n", "            'overall_suitability': float(row['overall_suitability']),\n", "            'suitability_label': int(row['suitability_label'])\n", "        } for _, row in pairs_df.nlargest(10, 'overall_suitability').iterrows()\n", "    ]\n", "}\n", "\n", "# Save summary\n", "import json\n", "summary_file = '../data/job_candidate_pairs_summary.json'\n", "with open(summary_file, 'w', encoding='utf-8') as f:\n", "    json.dump(summary, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"✅ Summary saved to: {summary_file}\")\n", "\n", "print(\"\\n🎉 FULL PAIRS GENERATION COMPLETED!\")\n", "print(\"=\"*60)\n", "print(f\"📊 Results:\")\n", "print(f\"   • Total Pairs: {len(pairs_df):,}\")\n", "print(f\"   • Jobs: {len(job_features)}\")\n", "print(f\"   • Candidates: {len(resume_features)}\")\n", "print(f\"   • High Quality Matches: {len(high_quality):,}\")\n", "print(f\"\\n📁 Output Files:\")\n", "print(f\"   • Full dataset: job_candidate_pairs_full.csv\")\n", "print(f\"   • High quality: job_candidate_pairs_high_quality.csv\")\n", "print(f\"   • By labels: job_candidate_pairs_[label].csv\")\n", "print(f\"   • Summary: job_candidate_pairs_summary.json\")\n", "print(f\"\\n🚀 Ready for machine learning training!\")"]}, {"cell_type": "code", "execution_count": null, "id": "scale-up-info", "metadata": {}, "outputs": [], "source": ["# Information about scaling up\n", "print(\"🔮 SCALING UP TO FULL DATASET:\")\n", "print(\"\\nThis notebook processed a sample for demonstration.\")\n", "print(\"To process the full dataset:\")\n", "print(\"\\n1. Modify the limits in the 'create-feature-vectors' cell:\")\n", "print(\"   - Change 'list(job_ids)[:10]' to 'list(job_ids)'\")\n", "print(\"   - Change 'list(resume_ids)[:20]' to 'list(resume_ids)'\")\n", "print(\"\\n2. Expected full dataset size:\")\n", "print(f\"   - Jobs: {len(job_ids):,}\")\n", "print(f\"   - Resumes: {len(resume_ids):,}\")\n", "print(f\"   - Total pairs: {len(job_ids) * len(resume_ids):,}\")\n", "print(\"\\n3. Processing time estimate:\")\n", "print(f\"   - Sample processing: ~{len(pairs_df)} pairs\")\n", "print(f\"   - Full processing: ~{len(job_ids) * len(resume_ids):,} pairs\")\n", "print(f\"   - Estimated time: {(len(job_ids) * len(resume_ids)) / len(pairs_df) * 2:.0f} minutes\")\n", "print(\"\\n4. Memory considerations:\")\n", "print(\"   - Process in batches if memory issues occur\")\n", "print(\"   - Consider using only top N candidates per job\")\n", "print(\"   - Filter low-quality matches to reduce dataset size\")\n", "print(\"\\n✅ Current sample demonstrates the methodology successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}